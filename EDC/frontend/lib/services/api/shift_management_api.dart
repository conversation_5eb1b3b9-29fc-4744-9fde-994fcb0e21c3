import 'package:dio/dio.dart';
import '../../models/shift_model.dart';
import 'api_client.dart';

/// 班次管理API服务
/// 
/// 专门用于班次管理相关的API调用
/// 包含开班、结班、获取班次列表等功能
class ShiftManagementApi {
  ShiftManagementApi({required ApiClient apiClient}) : _apiClient = apiClient;
  final ApiClient _apiClient;

  // ======================== 核心接口 ========================

  /// 开班 - POST /api/v1/shifts/start
  Future<ShiftModel> startShift(StartShiftRequest request) async {
    try {
      final Response<dynamic> response = await _apiClient.post(
        '/api/v1/shifts/start',
        data: request.toJson(),
      );

      if (response.statusCode == 201) {
        return ShiftModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw ApiException(
          message: '开班失败: ${response.statusMessage}',
          statusCode: response.statusCode,
          data: response.data,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: '开班请求失败: $e',
        statusCode: null,
        data: null,
      );
    }
  }

  /// 班结 - POST /api/v1/shifts/{station_id}/end
  Future<ShiftModel> endShift(int stationId) async {
    try {
      final Response<dynamic> response = await _apiClient.post(
        '/api/v1/shifts/$stationId/end',
      );

      if (response.statusCode == 200) {
        return ShiftModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw ApiException(
          message: '结班失败: ${response.statusMessage}',
          statusCode: response.statusCode,
          data: response.data,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: '结班请求失败: $e',
        statusCode: null,
        data: null,
      );
    }
  }

  /// 获取当前活跃班次 - GET /api/v1/shifts/current/{station_id}
  Future<ShiftModel?> getCurrentShift(int stationId) async {
    try {
      final Response<dynamic> response = await _apiClient.get(
        '/api/v1/shifts/current/$stationId',
      );

      if (response.statusCode == 200) {
        return ShiftModel.fromJson(response.data as Map<String, dynamic>);
      } else if (response.statusCode == 404) {
        // 没有活跃班次
        return null;
      } else {
        throw ApiException(
          message: '获取当前班次失败: ${response.statusMessage}',
          statusCode: response.statusCode,
          data: response.data,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: '获取当前班次请求失败: $e',
        statusCode: null,
        data: null,
      );
    }
  }

  /// 根据ID获取班次详情 - GET /api/v1/shifts/{id}
  Future<ShiftModel> getShiftById(String id) async {
    try {
      final Response<dynamic> response = await _apiClient.get(
        '/api/v1/shifts/$id',
      );

      if (response.statusCode == 200) {
        return ShiftModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw ApiException(
          message: '获取班次详情失败: ${response.statusMessage}',
          statusCode: response.statusCode,
          data: response.data,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: '获取班次详情请求失败: $e',
        statusCode: null,
        data: null,
      );
    }
  }

  // ======================== 扩展接口 ========================

  /// 获取班次列表 - GET /api/v1/shifts
  Future<ShiftListResponse> getShifts(ShiftListRequest request) async {
    try {
      final Response<dynamic> response = await _apiClient.get(
        '/api/v1/shifts',
        queryParameters: request.toQueryParams(),
      );

      if (response.statusCode == 200) {
        return ShiftListResponse.fromJson(
            response.data as Map<String, dynamic>);
      } else {
        throw ApiException(
          message: '获取班次列表失败: ${response.statusMessage}',
          statusCode: response.statusCode,
          data: response.data,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: '获取班次列表请求失败: $e',
        statusCode: null,
        data: null,
      );
    }
  }

  /// 根据班次编号获取班次 - GET /api/v1/shifts/number/{number}
  Future<ShiftModel> getShiftByNumber(String number) async {
    try {
      final Response<dynamic> response = await _apiClient.get(
        '/api/v1/shifts/number/$number',
      );

      if (response.statusCode == 200) {
        return ShiftModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw ApiException(
          message: '根据编号获取班次失败: ${response.statusMessage}',
          statusCode: response.statusCode,
          data: response.data,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: '根据编号获取班次请求失败: $e',
        statusCode: null,
        data: null,
      );
    }
  }

  /// 确保班次存在 - POST /api/v1/shifts/{station_id}/ensure
  Future<ShiftModel> ensureShift(int stationId) async {
    try {
      final Response<dynamic> response = await _apiClient.post(
        '/api/v1/shifts/$stationId/ensure',
      );

      if (response.statusCode == 200) {
        return ShiftModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw ApiException(
          message: '确保班次失败: ${response.statusMessage}',
          statusCode: response.statusCode,
          data: response.data,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: '确保班次请求失败: $e',
        statusCode: null,
        data: null,
      );
    }
  }

  /// 软删除班次 - DELETE /api/v1/shifts/{id}
  Future<void> deleteShift(String id) async {
    try {
      final Response<dynamic> response = await _apiClient.delete(
        '/api/v1/shifts/$id',
      );

      if (response.statusCode != 204) {
        throw ApiException(
          message: '删除班次失败: ${response.statusMessage}',
          statusCode: response.statusCode,
          data: response.data,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: '删除班次请求失败: $e',
        statusCode: null,
        data: null,
      );
    }
  }

  /// 恢复已删除班次 - POST /api/v1/shifts/{id}/restore
  Future<void> restoreShift(String id) async {
    try {
      final Response<dynamic> response = await _apiClient.post(
        '/api/v1/shifts/$id/restore',
      );

      if (response.statusCode != 204) {
        throw ApiException(
          message: '恢复班次失败: ${response.statusMessage}',
          statusCode: response.statusCode,
          data: response.data,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: '恢复班次请求失败: $e',
        statusCode: null,
        data: null,
      );
    }
  }

  // ======================== 工具方法 ========================

  /// 检查网络连接
  Future<bool> checkConnection() async {
    try {
      // 尝试调用一个简单的接口来检查连接
      await _apiClient.get('/api/v1/health',
          queryParameters: <String, dynamic>{'check': 'connection'});
      return true;
    } catch (e) {
      return false;
    }
  }
}

/// API异常类
class ApiException implements Exception {
  ApiException({
    required this.message,
    this.statusCode,
    this.data,
  });

  final String message;
  final int? statusCode;
  final dynamic data;

  @override
  String toString() {
    return 'ApiException: $message (Status code: $statusCode)';
  }
}
