// EDC/frontend/lib/models/auth_models.dart

/// 认证相关数据模型
/// 基于BOS认证API接口文档定义

import 'dart:convert';
import 'package:equatable/equatable.dart';

/// 登录请求模型
class LoginRequest extends Equatable {
  const LoginRequest({
    required this.username,
    required this.password,
    required this.system,
    this.authType = 'local',
    this.rememberMe = false,
    this.captcha,
    this.captchaId,
  });

  /// 用户名或邮箱地址
  final String username;
  
  /// 登录密码
  final String password;
  
  /// 目标系统: BOS|EDC
  final String system;
  
  /// 认证类型: local|ldap|ad
  final String authType;
  
  /// 是否记住登录状态
  final bool rememberMe;
  
  /// 验证码
  final String? captcha;
  
  /// 验证码ID
  final String? captchaId;

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'username': username,
      'password': password,
      'system': system,
      'authType': authType,
      'rememberMe': rememberMe,
    };
    
    if (captcha != null) {
      data['captcha'] = captcha;
    }
    
    if (captchaId != null) {
      data['captchaId'] = captchaId;
    }
    
    return data;
  }

  @override
  List<Object?> get props => [
        username,
        password,
        system,
        authType,
        rememberMe,
        captcha,
        captchaId,
      ];
}

/// 站点信息模型
class SiteInfo extends Equatable {
  const SiteInfo({
    required this.siteId,
    required this.siteName,
    required this.role,
    required this.isDefault,
  });

  final String siteId; // 根据实际API，应该是String类型
  final String siteName;
  final String role;
  final bool isDefault;

  factory SiteInfo.fromJson(Map<String, dynamic> json) {
    return SiteInfo(
      siteId: json['siteId'] as String,
      siteName: json['siteName'] as String,
      role: json['role'] as String,
      isDefault: json['isDefault'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'siteId': siteId,
      'siteName': siteName,
      'role': role,
      'isDefault': isDefault,
    };
  }

  @override
  List<Object?> get props => [siteId, siteName, role, isDefault];
}

/// 联系信息模型
class ContactInfo extends Equatable {
  const ContactInfo({
    required this.phone,
    required this.email,
  });

  final String phone;
  final String email;

  factory ContactInfo.fromJson(Map<String, dynamic> json) {
    return ContactInfo(
      phone: json['phone'] as String,
      email: json['email'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
      'email': email,
    };
  }

  @override
  List<Object?> get props => [phone, email];
}

/// 加油站详细信息模型
class StationDetail extends Equatable {
  const StationDetail({
    required this.id,
    required this.siteCode,
    required this.siteName,
    required this.type,
    required this.businessStatus,
    required this.address,
    required this.city,
    required this.province,
    required this.country,
    required this.latitude,
    required this.longitude,
    required this.contactInfo,
    required this.managerName,
    required this.companyId,
    required this.createdAt,
    required this.updatedAt,
  });

  final int id;
  final String siteCode;
  final String siteName;
  final String type;
  final String businessStatus;
  final String address;
  final String city;
  final String province;
  final String country;
  final double latitude;
  final double longitude;
  final ContactInfo contactInfo;
  final String managerName;
  final int companyId;
  final DateTime createdAt;
  final DateTime updatedAt;

  factory StationDetail.fromJson(Map<String, dynamic> json) {
    return StationDetail(
      id: json['id'] as int,
      siteCode: json['site_code'] as String,
      siteName: json['site_name'] as String,
      type: json['type'] as String,
      businessStatus: json['business_status'] as String,
      address: json['address'] as String,
      city: json['city'] as String,
      province: json['province'] as String,
      country: json['country'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      contactInfo: ContactInfo.fromJson(json['contact_info'] as Map<String, dynamic>),
      managerName: json['manager_name'] as String,
      companyId: json['company_id'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'site_code': siteCode,
      'site_name': siteName,
      'type': type,
      'business_status': businessStatus,
      'address': address,
      'city': city,
      'province': province,
      'country': country,
      'latitude': latitude,
      'longitude': longitude,
      'contact_info': contactInfo.toJson(),
      'manager_name': managerName,
      'company_id': companyId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        siteCode,
        siteName,
        type,
        businessStatus,
        address,
        city,
        province,
        country,
        latitude,
        longitude,
        contactInfo,
        managerName,
        companyId,
        createdAt,
        updatedAt,
      ];
}

/// 用户加油站信息模型（根据实际API响应结构）
class UserStationInfo extends Equatable {
  const UserStationInfo({
    required this.id,
    required this.siteCode,
    required this.siteName,
    required this.address,
    required this.status,
  });

  final int id;
  final String siteCode;
  final String siteName;
  final Map<String, dynamic> address; // 地址信息，结构可能复杂
  final String status;

  factory UserStationInfo.fromJson(Map<String, dynamic> json) {
    return UserStationInfo(
      id: json['id'] as int,
      siteCode: json['siteCode'] as String,
      siteName: json['siteName'] as String,
      address: json['address'] as Map<String, dynamic>? ?? {},
      status: json['status'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'siteCode': siteCode,
      'siteName': siteName,
      'address': address,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [
        id,
        siteCode,
        siteName,
        address,
        status,
      ];
}

/// 系统访问权限模型
class SystemAccess extends Equatable {
  const SystemAccess({
    required this.system,
    required this.accessLevel,
    required this.scopeType,
    required this.scopeIds,
    required this.stationIds,
    required this.stationCount,
    required this.permissions,
  });

  final String system;        // HOS|BOS|EDC
  final String accessLevel;   // admin|manager|supervisor|operator
  final String scopeType;     // global|station|operation
  final List<int> scopeIds;   // 权限范围ID列表
  final List<int> stationIds; // 可访问站点ID列表（向后兼容）
  final int stationCount;     // 可访问站点数量
  final List<String> permissions; // 用户权限列表

  factory SystemAccess.fromJson(Map<String, dynamic> json) {
    return SystemAccess(
      system: json['system'] as String,
      accessLevel: json['accessLevel'] as String,
      scopeType: json['scopeType'] as String,
      scopeIds: (json['scopeIds'] as List<dynamic>)
          .map((e) => e as int)
          .toList(),
      stationIds: (json['stationIds'] as List<dynamic>)
          .map((e) => e as int)
          .toList(),
      stationCount: json['stationCount'] as int,
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'system': system,
      'accessLevel': accessLevel,
      'scopeType': scopeType,
      'scopeIds': scopeIds,
      'stationIds': stationIds,
      'stationCount': stationCount,
      'permissions': permissions,
    };
  }

  @override
  List<Object?> get props => [
        system,
        accessLevel,
        scopeType,
        scopeIds,
        stationIds,
        stationCount,
        permissions,
      ];
}

/// 用户信息模型
class AuthUser extends Equatable {
  const AuthUser({
    required this.id,
    required this.username,
    required this.email,
    required this.fullName,
    required this.phone,
    required this.status,
    required this.roles,
    required this.permissions,
    required this.lastLoginAt,
    required this.language,
    required this.sites,
    required this.stations,
  });

  final String id;
  final String username;
  final String email;
  final String fullName;
  final String phone;
  final String status;
  final List<String> roles;
  final List<String> permissions;
  final DateTime lastLoginAt;
  final String language;
  final List<SiteInfo> sites;
  final List<UserStationInfo> stations;

  factory AuthUser.fromJson(Map<String, dynamic> json) {
    return AuthUser(
      id: json['id'] as String,
      username: json['username'] as String,
      email: json['email'] as String,
      fullName: json['fullName'] as String,
      phone: json['phone'] as String,
      status: json['status'] as String,
      roles: (json['roles'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      lastLoginAt: DateTime.parse(json['lastLoginAt'] as String),
      language: json['language'] as String,
      sites: (json['sites'] as List<dynamic>)
          .map((e) => SiteInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      stations: (json['stations'] as List<dynamic>)
          .map((e) => UserStationInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'fullName': fullName,
      'phone': phone,
      'status': status,
      'roles': roles,
      'permissions': permissions,
      'lastLoginAt': lastLoginAt.toIso8601String(),
      'language': language,
      'sites': sites.map((e) => e.toJson()).toList(),
      'stations': stations.map((e) => e.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        username,
        email,
        fullName,
        phone,
        status,
        roles,
        permissions,
        lastLoginAt,
        language,
        sites,
        stations,
      ];
}

/// 登录响应数据模型
class LoginResponseData extends Equatable {
  const LoginResponseData({
    required this.accessToken,
    required this.refreshToken,
    this.tokenType = 'Bearer',
    required this.expiresIn,
    required this.user,
    required this.systemAccess,
  });

  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final int expiresIn;
  final AuthUser user;
  final SystemAccess systemAccess;

  factory LoginResponseData.fromJson(Map<String, dynamic> json) {
    // 从实际API响应解析基本字段
    final String accessToken = json['accessToken'] as String;
    final String refreshToken = json['refreshToken'] as String;
    final String tokenType = json['tokenType'] as String? ?? 'Bearer';
    final int expiresIn = json['expiresIn'] as int;

    // 从JWT token解析用户信息
    final Map<String, dynamic> tokenPayload = _parseJwtPayload(accessToken);

    return LoginResponseData(
      accessToken: accessToken,
      refreshToken: refreshToken,
      tokenType: tokenType,
      expiresIn: expiresIn,
      user: _createUserFromToken(tokenPayload),
      systemAccess: _createSystemAccessFromToken(tokenPayload),
    );
  }

  /// 解析JWT token的payload部分
  static Map<String, dynamic> _parseJwtPayload(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) {
        throw Exception('Invalid JWT token format');
      }

      // JWT payload是base64编码的，需要解码
      String payload = parts[1];

      // 添加padding如果需要
      switch (payload.length % 4) {
        case 0:
          break;
        case 2:
          payload += '==';
          break;
        case 3:
          payload += '=';
          break;
        default:
          throw Exception('Invalid base64 string');
      }

      // 解码base64
      final List<int> bytes = base64Url.decode(payload);
      final String decoded = utf8.decode(bytes);

      // 解析JSON
      return json.decode(decoded) as Map<String, dynamic>;
    } catch (e) {
      // 如果解析失败，返回默认值
      print('JWT解析失败: $e');
      return {};
    }
  }

  /// 从JWT token创建用户对象
  static AuthUser _createUserFromToken(Map<String, dynamic> tokenPayload) {
    return AuthUser(
      id: tokenPayload['user_id'] as String? ?? 'unknown',
      username: tokenPayload['username'] as String? ?? 'admin',
      email: tokenPayload['email'] as String? ?? '<EMAIL>',
      fullName: tokenPayload['username'] as String? ?? 'Administrator',
      phone: '',
      status: 'active',
      roles: List<String>.from(tokenPayload['roles'] as List? ?? ['Super Admin']),
      permissions: List<String>.from(tokenPayload['permissions'] as List? ?? []),
      lastLoginAt: DateTime.now(),
      language: 'en',
      sites: [],
      stations: [],
    );
  }

  /// 从JWT token创建系统访问对象
  static SystemAccess _createSystemAccessFromToken(Map<String, dynamic> tokenPayload) {
    return SystemAccess(
      system: tokenPayload['system'] as String? ?? 'EDC',
      accessLevel: tokenPayload['access_level'] as String? ?? 'manager',
      scopeType: tokenPayload['scope_type'] as String? ?? 'operation',
      scopeIds: List<int>.from(tokenPayload['scope_ids'] as List? ?? [1]),
      stationIds: List<int>.from(tokenPayload['station_ids'] as List? ?? [1]),
      stationCount: (tokenPayload['station_ids'] as List?)?.length ?? 1,
      permissions: List<String>.from(tokenPayload['permissions'] as List? ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'tokenType': tokenType,
      'expiresIn': expiresIn,
      'user': user.toJson(),
      'systemAccess': systemAccess.toJson(),
    };
  }

  @override
  List<Object?> get props => [
        accessToken,
        refreshToken,
        tokenType,
        expiresIn,
        user,
        systemAccess,
      ];
}

/// 完整的登录响应模型
class LoginResponse extends Equatable {
  const LoginResponse({
    required this.code,
    required this.message,
    required this.data,
    required this.timestamp,
    required this.requestId,
  });

  final int code;
  final String message;
  final LoginResponseData data;
  final String timestamp;
  final String requestId;

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      code: json['code'] as int,
      message: json['message'] as String,
      data: LoginResponseData.fromJson(json['data'] as Map<String, dynamic>),
      timestamp: json['timestamp'] as String,
      requestId: json['requestId'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.toJson(),
      'timestamp': timestamp,
      'requestId': requestId,
    };
  }

  @override
  List<Object?> get props => [code, message, data, timestamp, requestId];
}

/// 认证错误响应模型
class AuthErrorResponse extends Equatable {
  const AuthErrorResponse({
    required this.code,
    required this.message,
    this.errors,
    required this.timestamp,
    required this.requestId,
  });

  final int code;
  final String message;
  final List<AuthFieldError>? errors;
  final String timestamp;
  final String requestId;

  factory AuthErrorResponse.fromJson(Map<String, dynamic> json) {
    return AuthErrorResponse(
      code: json['code'] as int,
      message: json['message'] as String,
      errors: json['errors'] != null
          ? (json['errors'] as List<dynamic>)
              .map((e) => AuthFieldError.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
      timestamp: json['timestamp'] as String,
      requestId: json['requestId'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'errors': errors?.map((e) => e.toJson()).toList(),
      'timestamp': timestamp,
      'requestId': requestId,
    };
  }

  @override
  List<Object?> get props => [code, message, errors, timestamp, requestId];
}

/// 认证字段错误模型
class AuthFieldError extends Equatable {
  const AuthFieldError({
    required this.field,
    required this.message,
  });

  final String field;
  final String message;

  factory AuthFieldError.fromJson(Map<String, dynamic> json) {
    return AuthFieldError(
      field: json['field'] as String,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'field': field,
      'message': message,
    };
  }

  @override
  List<Object?> get props => [field, message];
}
