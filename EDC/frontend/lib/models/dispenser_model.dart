import 'package:equatable/equatable.dart';

// Nozzle状态枚举 - 设备物理状态
enum NozzleStatus {
  idle, // 待机状态 - 绿色，可点击授权
  auth, // 已授权状态 - 黄色，显示授权信息
  fuelling, // 加油中状态 - 蓝色，显示实时数据
  complete, // 完成状态 - 紫色，显示交易结果
  pay,
  offline, // 离线状态 - 灰色，显示错误信息
}

// ✅ 新增：交易业务状态枚举
enum TransactionStatus {
  none, // 无交易
  pending, // 交易待处理（刚完成加油）
  processing, // 交易处理中（支付中）
  completed, // 交易已完成（已支付）
  cancelled, // 交易已取消
}

// 授权模式枚举
enum AuthMode {
  amount, // 金额模式
  volume, // 升数模式
  full, // 加满模式
}

// 授权请求模型
class AuthorizationRequest extends Equatable {
  const AuthorizationRequest({
    required this.nozzleId,
    required this.mode,
    this.value,
    required this.staffId,
    required this.requestTime,
  });

  factory AuthorizationRequest.fromJson(Map<String, dynamic> json) {
    return AuthorizationRequest(
      nozzleId: json['nozzle_id'] as String,
      mode: AuthMode.values
          .firstWhere((AuthMode e) => e.name == json['mode'] as String),
      value: json['value'] != null ? (json['value'] as num).toDouble() : null,
      staffId: json['staff_id'] as String,
      requestTime: DateTime.parse(json['request_time'] as String),
    );
  }
  final String nozzleId;
  final AuthMode mode;
  final double? value; // 金额或升数，加满模式时为null
  final String staffId;
  final DateTime requestTime;

  @override
  List<Object?> get props =>
      <Object?>[nozzleId, mode, value, staffId, requestTime];

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'nozzle_id': nozzleId,
      'mode': mode.name,
      'value': value,
      'staff_id': staffId,
      'request_time': requestTime.toIso8601String(),
    };
  }
}

// 预授权信息模型
class PreauthInfo extends Equatable {
  const PreauthInfo({
    required this.type,
    required this.number,
    required this.createdAt,
    required this.expiresAt,
  });

  factory PreauthInfo.fromJson(Map<String, dynamic> json) {
    return PreauthInfo(
      type: json['preauth_type'] as String,
      number: json['preauth_number'] as String,
      createdAt: DateTime.parse(json['preauth_created_at'] as String),
      expiresAt: DateTime.parse(json['preauth_expires_at'] as String),
    );
  }

  final String type; // 如 "preset_amount", "preset_volume", "full_tank"
  final String number; // 预设值，如 "20000"
  final DateTime createdAt;
  final DateTime expiresAt;

  @override
  List<Object?> get props => <Object?>[type, number, createdAt, expiresAt];

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'preauth_type': type,
      'preauth_number': number,
      'preauth_created_at': createdAt.toIso8601String(),
      'preauth_expires_at': expiresAt.toIso8601String(),
    };
  }

  // 检查预授权是否已过期
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  // 获取智能化显示的预设值
  String get displayValue {
    switch (type) {
      case 'preset_amount':
        final double amount = double.tryParse(number) ?? 0;
        return 'IDR ${_formatAmount(amount)}';
      case 'preset_volume':
        final double volume = double.tryParse(number) ?? 0;
        return '${volume.toStringAsFixed(2)} L';
      case 'full_tank':
        return 'Full Tank';
      default:
        return number;
    }
  }

  // 格式化金额显示
  String _formatAmount(double amount) {
    return amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }
}

// 当前交易信息模型
class CurrentTransaction extends Equatable {
  const CurrentTransaction({
    required this.transactionId,
    this.currentVolume,
    this.currentAmount,
    required this.startTime,
    required this.authRequest,
  });

  factory CurrentTransaction.fromJson(Map<String, dynamic> json) {
    return CurrentTransaction(
      transactionId: json['transaction_id'] as String,
      currentVolume: json['current_volume'] != null
          ? (json['current_volume'] as num).toDouble()
          : null,
      currentAmount: json['current_amount'] != null
          ? (json['current_amount'] as num).toDouble()
          : null,
      startTime: DateTime.parse(json['start_time'] as String),
      authRequest: AuthorizationRequest.fromJson(
          json['auth_request'] as Map<String, dynamic>),
    );
  }
  final String transactionId;
  final double? currentVolume;
  final double? currentAmount;
  final DateTime startTime;
  final AuthorizationRequest authRequest;

  @override
  List<Object?> get props => <Object?>[
        transactionId,
        currentVolume,
        currentAmount,
        startTime,
        authRequest,
      ];

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'transaction_id': transactionId,
      'current_volume': currentVolume,
      'current_amount': currentAmount,
      'start_time': startTime.toIso8601String(),
      'auth_request': authRequest.toJson(),
    };
  }
}

// Nozzle模型 - 状态的最小粒度
class Nozzle extends Equatable {
  // 离线状态的错误信息

  const Nozzle({
    required this.id,
    required this.dispenserId, // 优化：从接口获取的 dispenserId
    required this.pumpGroupId, // 优化：从接口获取的 pumpGroupId
    this.deviceName, // 设备名称，可选字段
    required this.number, // 新增必需字段
    required this.name,
    required this.fuelType,
    required this.fuelGrade,
    required this.price,
    required this.status,
    this.currentAuth,
    this.preauthInfo, // 新增：预授权信息
    this.currentVolume = 0.0, // 默认值为0.0
    this.currentAmount = 0.0, // 默认值为0.0
    required this.lastUpdateTime,
    this.errorMessage,
  });

  factory Nozzle.fromJson(Map<String, dynamic> json) {
    // 优化：从 metadata 中解析 dispenserId 和 pumpGroupId
    String dispenserId = '1'; // 默认值
    String pumpGroupId = '1'; // 默认值

    if (json['metadata'] != null) {
      final Map<String, dynamic> metadata = json['metadata'] as Map<String, dynamic>;
      dispenserId = metadata['dispenser_id'] as String? ?? '1';
      pumpGroupId = metadata['pump_group_id'] as String? ?? '1';
    }

    // 处理真实API数据结构
    return Nozzle(
      id: json['id'] as String,
      dispenserId: dispenserId, // 优化：从 metadata 中获取 dispenserId
      pumpGroupId: pumpGroupId, // 优化：从 metadata 中获取 pumpGroupId
      deviceName: json['device_name'] as String?, // 设备名称，可选
      number: json['number'] as int? ?? 1, // 如果没有提供，默认为1
      name: json['name'] as String? ?? '#${json['nozzle_number'] ?? ''}',
      fuelType: json['fuel_type'] as String? ??
          json['fuel_grade_name'] as String? ??
          'Unknown',
      fuelGrade: json['fuel_grade'] as String? ??
          json['fuel_grade_name'] as String? ??
          'Unknown',
      price: (json['price'] as num?)?.toDouble() ??
          (json['current_price'] as num?)?.toDouble() ??
          0.0,
      status: _parseNozzleStatus(json['status'] as String?),
      currentAuth: json['current_auth'] != null
          ? AuthorizationRequest.fromJson(
              json['current_auth'] as Map<String, dynamic>)
          : null,
      preauthInfo: _parsePreauthInfo(json), // 新增：解析预授权信息
      currentVolume: (json['current_volume'] as num?)?.toDouble() ?? 0.0,
      currentAmount: (json['current_amount'] as num?)?.toDouble() ?? 0.0,
      lastUpdateTime: DateTime.parse(json['last_update_time'] as String? ??
          json['last_update'] as String? ??
          DateTime.now().toIso8601String()),
      errorMessage: json['error_message'] as String?,
    );
  }

  // 解析预授权信息的静态方法
  static PreauthInfo? _parsePreauthInfo(Map<String, dynamic> json) {
    // 检查是否包含所有必需的预授权字段
    if (json['preauth_type'] != null &&
        json['preauth_number'] != null &&
        json['preauth_created_at'] != null &&
        json['preauth_expires_at'] != null) {
      try {
        return PreauthInfo.fromJson(json);
      } catch (e) {
        // 如果解析失败，返回null
        return null;
      }
    }
    return null;
  }

  final String id;
  final String dispenserId; // 优化：直接从接口获取的 dispenserId
  final String pumpGroupId; // 优化：直接从接口获取的 pumpGroupId
  final String? deviceName; // 设备名称，从 FCCDevice.name 传递
  final int number; // 新增：这个Nozzle是当前Pump的第几把枪
  final String name; // 如 "Nozzle 1"
  final String fuelType; // 如 "92#汽油"
  final String fuelGrade; // 如 "92#"
  final double price; // 每升价格
  final NozzleStatus status;
  final AuthorizationRequest? currentAuth;
  final PreauthInfo? preauthInfo; // 新增：预授权信息
  final double currentVolume; // 当前油量 (新增)
  final double currentAmount; // 当前金额 (新增)
  final DateTime lastUpdateTime;
  final String? errorMessage;

  @override
  List<Object?> get props => <Object?>[
        id,
        dispenserId, // 优化：新增到props
        pumpGroupId,
        deviceName, // 新增到props
        number, // 新增到props
        name,
        fuelType,
        fuelGrade,
        price,
        status,
        currentAuth,
        preauthInfo, // 新增：预授权信息到props
        currentVolume, // 新增到props
        currentAmount, // 新增到props
        lastUpdateTime,
        errorMessage,
      ];

  // 解析nozzle状态的辅助方法
  static NozzleStatus _parseNozzleStatus(String? statusStr) {
    if (statusStr == null) return NozzleStatus.offline;

    switch (statusStr.toLowerCase()) {
      case 'idle':
        return NozzleStatus.idle;
      case 'auth':
      case 'authorized':
        return NozzleStatus.auth;
      case 'fuelling':
      case 'filling': // 添加对API返回的 "filling" 状态的支持
      case 'active':
      case 'dispensing':
        return NozzleStatus.fuelling;
      case 'complete':
      case 'completed':
      case 'done':
        return NozzleStatus.complete;
      case 'offline':
      case 'disabled':
        return NozzleStatus.offline;
      default:
        return NozzleStatus.idle;
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{
      'id': id,
      'pump_group_id': pumpGroupId,
      if (deviceName != null) 'device_name': deviceName,
      'number': number,
      'name': name,
      'fuel_type': fuelType,
      'fuel_grade': fuelGrade,
      'price': price,
      'status': status.name,
      'current_auth': currentAuth?.toJson(),
      'current_volume': currentVolume, // 新增JSON序列化
      'current_amount': currentAmount, // 新增JSON序列化
      'last_update_time': lastUpdateTime.toIso8601String(),
      'error_message': errorMessage,
    };

    // 添加预授权信息（如果存在）
    if (preauthInfo != null) {
      json.addAll(preauthInfo!.toJson());
    }

    return json;
  }

  // 创建副本，用于状态更新
  Nozzle copyWith({
    String? id,
    String? dispenserId, // 优化：新增copyWith参数
    String? pumpGroupId,
    String? deviceName,
    int? number,
    String? name,
    String? fuelType,
    String? fuelGrade,
    double? price,
    NozzleStatus? status,
    AuthorizationRequest? currentAuth,
    PreauthInfo? preauthInfo, // 新增：预授权信息copyWith参数
    double? currentVolume, // 新增copyWith参数
    double? currentAmount, // 新增copyWith参数
    DateTime? lastUpdateTime,
    String? errorMessage,
  }) {
    return Nozzle(
      id: id ?? this.id,
      dispenserId: dispenserId ?? this.dispenserId, // 优化：新增copyWith逻辑
      pumpGroupId: pumpGroupId ?? this.pumpGroupId,
      deviceName: deviceName ?? this.deviceName,
      number: number ?? this.number,
      name: name ?? this.name,
      fuelType: fuelType ?? this.fuelType,
      fuelGrade: fuelGrade ?? this.fuelGrade,
      price: price ?? this.price,
      status: status ?? this.status,
      currentAuth: currentAuth ?? this.currentAuth,
      preauthInfo: preauthInfo ?? this.preauthInfo, // 新增：预授权信息copyWith逻辑
      currentVolume: currentVolume ?? this.currentVolume, // 新增copyWith逻辑
      currentAmount: currentAmount ?? this.currentAmount, // 新增copyWith逻辑
      lastUpdateTime: lastUpdateTime ?? this.lastUpdateTime,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  // 判断是否可以授权
  bool get canAuthorize => status == NozzleStatus.idle;

  // 判断是否正在进行交易
  bool get isTransacting =>
      status == NozzleStatus.auth ||
      status == NozzleStatus.fuelling ||
      status == NozzleStatus.complete;

  // 获取状态显示文本
  String get statusDisplayText {
    switch (status) {
      case NozzleStatus.idle:
        return 'Ready';
      case NozzleStatus.auth:
        return 'Authorized';
      case NozzleStatus.fuelling:
        return 'In Progress';
      case NozzleStatus.complete:
        return 'In Completed';
      case NozzleStatus.pay:
        return 'Pay';
      case NozzleStatus.offline:
        return 'Offline';
    }
  }


}

// PumpGroup模型 - 作为Nozzle的分组概念
class PumpGroup extends Equatable {
  const PumpGroup({
    required this.id,
    required this.dispenserId,
    required this.name,
    required this.nozzles,
    required this.lastUpdateTime,
  });

  factory PumpGroup.fromJson(Map<String, dynamic> json) {
    return PumpGroup(
      id: json['id'] as String,
      dispenserId: json['dispenser_id'] as String,
      name: json['name'] as String,
      nozzles: (json['nozzles'] as List<dynamic>)
          .map((dynamic nozzle) => Nozzle.fromJson(nozzle as Map<String, dynamic>))
          .toList(),
      lastUpdateTime: DateTime.parse(json['last_update_time'] as String),
    );
  }
  final String id;
  final String dispenserId;
  final String name; // 如 "Pump 1"
  final List<Nozzle> nozzles;
  final DateTime lastUpdateTime;

  @override
  List<Object?> get props =>
      <Object?>[id, dispenserId, name, nozzles, lastUpdateTime];

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'dispenser_id': dispenserId,
      'name': name,
      'nozzles': nozzles.map((Nozzle nozzle) => nozzle.toJson()).toList(),
      'last_update_time': lastUpdateTime.toIso8601String(),
    };
  }

  // 创建副本，用于状态更新
  PumpGroup copyWith({
    String? id,
    String? dispenserId,
    String? name,
    List<Nozzle>? nozzles,
    DateTime? lastUpdateTime,
  }) {
    return PumpGroup(
      id: id ?? this.id,
      dispenserId: dispenserId ?? this.dispenserId,
      name: name ?? this.name,
      nozzles: nozzles ?? this.nozzles,
      lastUpdateTime: lastUpdateTime ?? this.lastUpdateTime,
    );
  }

  // 获取指定ID的Nozzle
  Nozzle? getNozzleById(String nozzleId) {
    try {
      return nozzles.firstWhere((Nozzle nozzle) => nozzle.id == nozzleId);
    } catch (e) {
      return null;
    }
  }

  // 更新指定Nozzle
  PumpGroup updateNozzle(Nozzle updatedNozzle) {
    final List<Nozzle> updatedNozzles = nozzles.map((Nozzle nozzle) {
      return nozzle.id == updatedNozzle.id ? updatedNozzle : nozzle;
    }).toList();

    return copyWith(
      nozzles: updatedNozzles,
      lastUpdateTime: DateTime.now(),
    );
  }

  // 获取活跃交易数量
  int get activeTransactionCount {
    return nozzles.where((Nozzle nozzle) => nozzle.isTransacting).length;
  }

  // 获取可用Nozzle数量
  int get availableNozzleCount {
    return nozzles.where((Nozzle nozzle) => nozzle.canAuthorize).length;
  }

  // 获取离线Nozzle数量
  int get offlineNozzleCount {
    return nozzles
        .where((Nozzle nozzle) => nozzle.status == NozzleStatus.offline)
        .length;
  }


}

// Dispenser模型 - 分液器设备
class Dispenser extends Equatable {
  const Dispenser({
    required this.id,
    required this.name,
    required this.displayName,
    required this.pumpGroups,
    required this.isOnline,
    required this.lastUpdateTime,
  });

  factory Dispenser.fromJson(Map<String, dynamic> json) {
    return Dispenser(
      id: json['id'] as String, // 优化：支持 String 类型的 id
      name: json['name'] as String,
      displayName: json['display_name'] as String,
      pumpGroups: (json['pump_groups'] as List<dynamic>)
          .map((dynamic group) => PumpGroup.fromJson(group as Map<String, dynamic>))
          .toList(),
      isOnline: json['is_online'] as bool,
      lastUpdateTime: DateTime.parse(json['last_update_time'] as String),
    );
  }
  final String id; // 优化：改为 String 类型
  final String name; // 如 "Dispenser 1"
  final String displayName; // 如 "分液器1"
  final List<PumpGroup> pumpGroups;
  final bool isOnline;
  final DateTime lastUpdateTime;

  @override
  List<Object?> get props => <Object?>[
        id,
        name,
        displayName,
        pumpGroups,
        isOnline,
        lastUpdateTime,
      ];

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'display_name': displayName,
      'pump_groups':
          pumpGroups.map((PumpGroup group) => group.toJson()).toList(),
      'is_online': isOnline,
      'last_update_time': lastUpdateTime.toIso8601String(),
    };
  }

  // 创建副本，用于状态更新
  Dispenser copyWith({
    String? id, // 优化：改为 String 类型
    String? name,
    String? displayName,
    List<PumpGroup>? pumpGroups,
    bool? isOnline,
    DateTime? lastUpdateTime,
  }) {
    return Dispenser(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      pumpGroups: pumpGroups ?? this.pumpGroups,
      isOnline: isOnline ?? this.isOnline,
      lastUpdateTime: lastUpdateTime ?? this.lastUpdateTime,
    );
  }

  // 获取指定ID的Nozzle
  Nozzle? getNozzleById(String nozzleId) {
    for (final PumpGroup group in pumpGroups) {
      final Nozzle? nozzle = group.getNozzleById(nozzleId);
      if (nozzle != null) return nozzle;
    }
    return null;
  }

  // 更新指定Nozzle
  Dispenser updateNozzle(Nozzle updatedNozzle) {
    final List<PumpGroup> updatedGroups = pumpGroups.map((PumpGroup group) {
      if (group.nozzles.any((Nozzle nozzle) => nozzle.id == updatedNozzle.id)) {
        return group.updateNozzle(updatedNozzle);
      }
      return group;
    }).toList();

    return Dispenser(
      id: id,
      name: name,
      displayName: displayName,
      pumpGroups: updatedGroups,
      isOnline: isOnline,
      lastUpdateTime: DateTime.now(),
    );
  }


}
