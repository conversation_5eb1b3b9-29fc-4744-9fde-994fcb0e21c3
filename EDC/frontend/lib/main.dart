import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:go_router/src/router.dart';
import 'theme/app_theme.dart';
import 'routes/app_routes.dart';
import 'services/api/api_service.dart';
import 'services/api/service_registry.dart'; // 添加新的服务注册表
import 'services/shared/storage_service.dart';
import 'constants/api_constants.dart';
import 'constants/bp_colors.dart'; // 添加BP颜色常量
import 'l10n/generated/app_localizations.dart';
import 'services/log_service.dart';
import 'services/timezone_service.dart';
import 'services/nozzle_authorization_cache_service.dart';
import 'dart:async';

// 全局 StorageService Provider
// 使用 Provider 而不是 StateNotifierProvider，因为 StorageService 本身不管理需要监听的状态
final Provider<StorageService> storageServiceProvider =
    Provider<StorageService>((ProviderRef<StorageService> ref) {
  // 这里抛出一个错误，因为 StorageService 必须在 main 函数中被初始化
  // 并在 ProviderScope 的 overrides 中提供
  throw UnimplementedError(
      'StorageService must be initialized and provided in main');
});

// 全局 TimezoneService Provider
final ChangeNotifierProvider<TimezoneService> timezoneServiceProvider =
    ChangeNotifierProvider<TimezoneService>(
        (ChangeNotifierProviderRef<TimezoneService> ref) {
  throw UnimplementedError(
      'TimezoneService must be initialized and provided in main');
});

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 0. 初始化日志服务 (最早初始化，以便记录其他服务的初始化过程)
  await LogService.instance.initialize();
  logInfo('Main', '应用程序启动');

  // 1. 初始化 StorageService
  final StorageService storageService = StorageService();
  await storageService.init(); // 等待 SharedPreferences 初始化完成

  // 2. 初始化 TimezoneService
  final TimezoneService timezoneService = TimezoneService();
  await timezoneService.init(); // 等待时区服务初始化完成
  logInfo('Main', '时区服务初始化完成，当前时区: ${timezoneService.selectedTimezone}');

  // 3. 获取环境配置
  final ApiEnvironment currentEnvironment =
      await storageService.getApiEnvironment();
  logInfo('Main', '当前API环境: $currentEnvironment');

  // 3.1. 加载自定义服务器地址配置
  final customBosUrl = await storageService.getCustomBosUrl();
  final customFccUrl = await storageService.getCustomFccUrl();
  
  if (customBosUrl != null) {
    ApiConstants.setCustomServiceUrl(BackendService.base, customBosUrl);
    logInfo('Main', '加载自定义BOS地址: $customBosUrl');
  }
  
  if (customFccUrl != null) {
    ApiConstants.setCustomServiceUrl(BackendService.fcc, customFccUrl);
    logInfo('Main', '加载自定义FCC地址: $customFccUrl');
  }

  // 4. 初始化新的多服务架构
  logInfo('Main', '初始化多服务架构...');
  await ServiceRegistry().init(currentEnvironment);

  // 5. 为了兼容性，仍然初始化原有的 API 服务 (使用 base 服务作为默认)
  final String baseUrl =
      ApiConstants.getFinalServiceUrl(BackendService.base, currentEnvironment);
  logInfo('Main', '使用基础URL进行旧版API服务: $baseUrl');
  ApiService().init(baseUrl: baseUrl);

  // 修复鼠标追踪器错误
  _fixMouseTrackerIssue();

  // 设置屏幕方向
  SystemChrome.setPreferredOrientations(<DeviceOrientation>[
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // 应用启动时设置沉浸式状态栏和导航栏
  _setSystemUIOverlayStyle();

  // 6. 打印服务状态信息
  final Map<String, dynamic> serviceStatus =
      ServiceRegistry().getServiceStatus();
  logInfo('Main', '服务状态: $serviceStatus');

  // 7. 启动授权缓存定期清理任务（每30分钟清理一次过期数据）
  Timer.periodic(const Duration(minutes: 30), (Timer timer) {
    nozzleAuthorizationCache.cleanupExpiredAuthorizations();
  });
  logInfo('Main', '授权缓存清理任务已启动');

  runApp(
    ProviderScope(
      // 8. 重写 Provider，提供初始化好的服务实例
      overrides: <Override>[
        storageServiceProvider.overrideWithValue(storageService),
        timezoneServiceProvider.overrideWith(
            (ChangeNotifierProviderRef<TimezoneService> ref) =>
                timezoneService),
      ],
      child: const MyApp(),
    ),
  );
}

// 提供解决鼠标追踪器错误的函数
void _fixMouseTrackerIssue() {
  // 当运行在移动设备上时，禁用Flutter的鼠标事件断言检查
  if (!kIsWeb &&
      (defaultTargetPlatform == TargetPlatform.android ||
          defaultTargetPlatform == TargetPlatform.iOS)) {
    // 由于这是Flutter框架自身的错误，我们使用一个空的异常处理
    // 来避免应用因为鼠标事件错误而崩溃
    FlutterError.onError = (FlutterErrorDetails details) {
      final Object exception = details.exception;
      if (exception is AssertionError &&
          exception
              .toString()
              .contains('package:flutter/src/rendering/mouse_tracker.dart')) {
        // 忽略与鼠标追踪器相关的断言错误
        return;
      }
      // 处理其他错误
      FlutterError.presentError(details);
    };
  }
}

// 提取为单独的函数，方便在应用恢复时重新调用
void _setSystemUIOverlayStyle() {
  // 设置沉浸式状态栏和导航栏
  // 使用edgeToEdge模式，允许内容延伸到系统栏区域
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    // 状态栏 - 设置为BP绿色
    statusBarColor: BPColors.primary, // BP绿色状态栏
    statusBarIconBrightness: Brightness.light, // 状态栏图标（时间、电量等）为浅色
    statusBarBrightness: Brightness.dark, // 对于iOS，状态栏背景为深色
    // 导航栏 (Android)
    systemNavigationBarColor: Colors.transparent, // 透明导航栏
    systemNavigationBarDividerColor: Colors.transparent, // 透明导航栏分割线
    systemNavigationBarIconBrightness: Brightness.dark, // 导航栏按钮（返回、主页等）为深色
    systemNavigationBarContrastEnforced: false, // 不强制导航栏对比度
  ));
}

class MyApp extends StatefulWidget {
  // 移除 authService 属性
  // final AuthService authService;

  // const MyApp({Key? key, required this.authService}) : super(key: key);
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  // AppRouter 现在可能需要访问 Provider，我们稍后处理
  // late AppRouter _appRouter;
  // 暂时移除，GoRouter 的配置将在 build 方法中进行，以便访问 Provider

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // 不再在这里初始化 AppRouter
    // _appRouter = AppRouter(authService: widget.authService);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      // 应用从后台恢复时，重新应用系统UI设置
      _setSystemUIOverlayStyle();
    } else if (state == AppLifecycleState.detached) {
      // 应用被完全终止时，清除登录状态
      // 需要通过 Provider 获取 AuthService 实例
      // widget.authService.clearLoginState();
      // TODO: 在这里添加通过 Provider 清理状态的逻辑 (如果需要)
      // 例如: ProviderScope.containerOf(context).read(authServiceProvider).clearLoginState();
      // 但在 detached 状态下操作可能不稳定，需要谨慎处理
    }
  }

  @override
  Widget build(BuildContext context) {
    // 将 GoRouter 的配置移到这里，以便访问 Provider
    // 这不是最高效的方式，更好的方式是将 GoRouter 配置本身变成 Provider
    // 但作为修复步骤，暂时这样做
    return Consumer(
      builder: (BuildContext context, WidgetRef ref, Widget? child) {
        // 移除之前在这里获取 authService 和创建 AppRouter 的逻辑
        /*
        final authService = ref.watch(authServiceProvider);
        final appRouter = AppRouter(authService: authService);
        final goRouter = appRouter.router; 
        */

        // 直接通过 Provider 获取 GoRouter 实例
        final GoRouter goRouter = ref.watch(goRouterProvider);

        return MaterialApp.router(
          title: 'BP EDC',
          debugShowCheckedModeBanner: false,
          // 使用 Provider 提供的 GoRouter 配置
          routerConfig: goRouter,
          theme: AppTheme.lightTheme,
          // 国际化配置
          localizationsDelegates: const <LocalizationsDelegate<dynamic>>[
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const <Locale>[
            Locale('en', 'US'), // 英文（优先）
            Locale('zh', 'CN'), // 中文
          ],
          // builder: (context, child) { // REMOVE_START
          //   // 添加额外的底部边距，确保内容不被系统导航栏遮挡
          //   final mediaQuery = MediaQuery.of(context);
          //   final bottomPadding = mediaQuery.padding.bottom;
          //
          //   return MediaQuery(
          //     // 创建新的MediaQuery，保留原有的边距值
          //     data: mediaQuery.copyWith(
          //       // 增加底部边距，确保内容不被系统导航栏遮挡
          //       padding: mediaQuery.padding.copyWith(
          //         bottom: bottomPadding > 0 ? bottomPadding : 16,
          //       ),
          //     ),
          //     child: child!,
          //   );
          // }, // REMOVE_END
        );
      },
    );
  }
}
