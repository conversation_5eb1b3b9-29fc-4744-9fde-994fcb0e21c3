import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import '../models/fuel_transaction.dart';
import '../services/api/api_service.dart';
import '../services/api/fuel_transaction_api.dart';
import '../services/shift_service.dart';

/// BOS交易轮询服务
/// 定期轮询BOS系统获取待处理交易，并独立轮询班次状态变化
class BosTransactionPollingService {
  BosTransactionPollingService({
    ApiService? apiService,
    Duration pollingInterval = const Duration(seconds: 5), // BOS交易轮询间隔为5秒
    Duration shiftPollingInterval = const Duration(seconds: 30), // 班次状态轮询间隔为30秒
  })  : _apiService = apiService ?? ApiService(),
        _pollingInterval = pollingInterval,
        _shiftPollingInterval = shiftPollingInterval;
        
  final ApiService _apiService;
  final Duration _pollingInterval;
  final Duration _shiftPollingInterval;
  
  Timer? _pollingTimer; // 交易轮询定时器
  Timer? _shiftPollingTimer; // 班次状态轮询定时器
  bool _isPolling = false;

  /// 已处理的BOS交易ID集合，避免重复处理
  final Set<String> _processedTransactionIds = <String>{};

  /// 上次轮询发现的pending交易集合，用于检测交易消失
  final Set<String> _lastPendingTransactionIds = <String>{};

  /// 上次已知的班次状态，用于检测班次变化
  ShiftInfo? _lastKnownShift;

  /// 回调函数
  Function(List<FuelTransaction>)? onTransactionsFound;
  Function(List<String>)? onTransactionsDisappeared; // 交易消失回调
  Function(ShiftInfo?)? onShiftStatusChanged; // 班次状态变化回调
  Function(String)? onError;

  /// 启动轮询
  void startPolling() {
    if (_isPolling) {
      debugPrint('⚠️ BOS轮询已在运行中');
      return;
    }

    try {
      _isPolling = true;
      
      // 启动交易轮询定时器
      _pollingTimer = Timer.periodic(_pollingInterval, (Timer timer) {
        _pollPendingTransactions();
      });

      // 启动独立的班次状态轮询定时器
      _shiftPollingTimer = Timer.periodic(_shiftPollingInterval, (Timer timer) {
        _pollShiftStatus();
      });

      // 立即执行一次班次状态检查
      _pollShiftStatus();

      debugPrint('✅ BOS交易轮询已启动，间隔: ${_pollingInterval.inSeconds}秒');
      debugPrint('✅ 班次状态轮询已启动，间隔: ${_shiftPollingInterval.inSeconds}秒');
    } catch (e) {
      debugPrint('❌ BOS轮询启动失败: $e');
      onError?.call('Failed to start BOS polling: $e');
    }
  }

  /// 停止轮询
  void stopPolling() {
    try {
      _pollingTimer?.cancel();
      _pollingTimer = null;
      
      _shiftPollingTimer?.cancel();
      _shiftPollingTimer = null;
      
      _isPolling = false;
      debugPrint('⏹️ BOS交易轮询已停止');
      debugPrint('⏹️ 班次状态轮询已停止');
    } catch (e) {
      debugPrint('❌ 停止BOS轮询失败: $e');
    }
  }

  /// 轮询待处理的交易
  Future<void> _pollPendingTransactions() async {
    try {
      final FuelTransactionApi fuelTransactionApi =
          _apiService.fuelTransactionApi;

      // 获取查询时间范围
      final Map<String, String> timeRange = _getTransactionQueryTimeRange();

      // 查询待处理交易
      final FuelTransactionQueryParams queryParams = FuelTransactionQueryParams(
        status: 'pending', // 查询待处理的交易
        dateFrom: timeRange['dateFrom'],
        dateTo: timeRange['dateTo'],
        page: 1,
        limit: 20, // 最多查询20条
        sortBy: 'created_at',
        sortDir: 'desc',
      );

      final FuelTransactionResponse response =
          await fuelTransactionApi.getFuelTransactions(queryParams);

      // 获取当前pending交易ID集合
      final Set<String> currentPendingIds = response.items
          .map((FuelTransaction tx) => tx.transactionNumber)
          .toSet();

      // 检测消失的交易（上次有，这次没有）
      final Set<String> disappearedTransactionIds = 
          _lastPendingTransactionIds.difference(currentPendingIds);

      if (disappearedTransactionIds.isNotEmpty) {
        debugPrint('🔍 检测到 ${disappearedTransactionIds.length} 条交易已消失');
        debugPrint('   消失的交易ID: $disappearedTransactionIds');
        
        // 通知回调处理消失的交易
        onTransactionsDisappeared?.call(disappearedTransactionIds.toList());
      }

      // 更新上次pending交易集合
      _lastPendingTransactionIds.clear();
      _lastPendingTransactionIds.addAll(currentPendingIds);

      if (response.items.isNotEmpty) {
        debugPrint('🔄 发现 ${response.items.length} 条pending交易');
        debugPrint('   查询时间范围: ${timeRange['dateFrom']} ~ ${timeRange['dateTo']}');
        debugPrint('   数据来源: ${timeRange['source']}');
        
        // 对于pending交易，我们需要持续处理以保持nozzle的complete状态
        // 不再过滤"已处理"的交易，因为pending状态需要持续维护
        final List<FuelTransaction> pendingTransactions = response.items;
        
        for (final FuelTransaction transaction in pendingTransactions) {
          debugPrint('   - 交易 ${transaction.transactionNumber}: nozzle ${transaction.nozzleId}, volume=${transaction.volume}L, amount=${transaction.amount}');
        }

        // 通知回调处理所有pending交易（保持状态锁定）
        onTransactionsFound?.call(pendingTransactions);
      } else {
        debugPrint('📭 未发现pending交易');
      }
    } catch (e) {
      debugPrint('⚠️ BOS交易轮询失败: $e');
      onError?.call('BOS polling failed: $e');
    }
  }

  /// 获取交易查询时间范围
  Map<String, String> _getTransactionQueryTimeRange() {
    final DateTime now = DateTime.now();
    final ShiftService shiftService = ShiftService();
    final ShiftInfo? currentShift = shiftService.currentShift;

    String dateFrom;
    String dateTo;
    String source;

    if (currentShift != null && currentShift.status == ShiftStatus.active) {
      // 使用当前班次时间范围
      dateFrom = _formatDateForQuery(currentShift.startTime);
      dateTo = _formatDateForQuery(now);
      source = '班次时间 (${currentShift.shiftId})';
    } else {
      // 回退策略：查询最近6小时
      final DateTime sixHoursAgo = now.subtract(const Duration(hours: 6));
      dateFrom = _formatDateForQuery(sixHoursAgo);
      dateTo = _formatDateForQuery(now);
      source = '最近6小时 (无活跃班次)';
    }

    return <String, String>{
      'dateFrom': dateFrom,
      'dateTo': dateTo,
      'source': source,
    };
  }

  /// 格式化日期用于查询
  String _formatDateForQuery(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
  }

  /// 获取服务状态
  Map<String, dynamic> getServiceStatus() {
    return <String, dynamic>{
      'is_polling': _isPolling,
      'transaction_polling_interval_seconds': _pollingInterval.inSeconds,
      'shift_polling_interval_seconds': _shiftPollingInterval.inSeconds,
      'processed_transactions_count': _processedTransactionIds.length,
      'last_pending_transactions_count': _lastPendingTransactionIds.length,
      'has_transaction_callback': onTransactionsFound != null,
      'has_disappeared_callback': onTransactionsDisappeared != null,
      'has_shift_callback': onShiftStatusChanged != null,
      'has_error_callback': onError != null,
      'last_known_shift': _lastKnownShift?.shiftId,
      'last_known_shift_status': _lastKnownShift?.status.name,
    };
  }

  /// 清理已处理交易记录（避免内存无限增长）
  void clearProcessedTransactions() {
    final int previousProcessedCount = _processedTransactionIds.length;
    final int previousPendingCount = _lastPendingTransactionIds.length;
    
    _processedTransactionIds.clear();
    _lastPendingTransactionIds.clear();
    
    debugPrint('🧹 清理交易记录: 已处理 $previousProcessedCount -> 0, 上次pending $previousPendingCount -> 0');
  }

  /// 手动触发一次轮询（用于测试或立即检查）
  Future<void> pollOnce() async {
    debugPrint('🔄 手动触发BOS交易轮询...');
    await _pollPendingTransactions();
    await _pollShiftStatus();
  }

  /// 获取详细的调试信息
  void printDebugInfo() {
    final Map<String, dynamic> status = getServiceStatus();
    debugPrint('📊 BOS交易轮询服务状态:');
    status.forEach((String key, dynamic value) {
      debugPrint('   $key: $value');
    });

    debugPrint('📋 已处理交易ID列表:');
    for (final String id in _processedTransactionIds) {
      debugPrint('   - $id');
    }

    debugPrint('📋 上次pending交易ID列表:');
    for (final String id in _lastPendingTransactionIds) {
      debugPrint('   - $id');
    }

    debugPrint('🔗 回调函数状态:');
    debugPrint('   onTransactionsFound: ${onTransactionsFound != null ? "已设置" : "未设置"}');
    debugPrint('   onTransactionsDisappeared: ${onTransactionsDisappeared != null ? "已设置" : "未设置"}');
    debugPrint('   onShiftStatusChanged: ${onShiftStatusChanged != null ? "已设置" : "未设置"}');
    debugPrint('   onError: ${onError != null ? "已设置" : "未设置"}');
  }

  /// 轮询班次状态变化
  Future<void> _pollShiftStatus() async {
    try {
      final ShiftService shiftService = ShiftService();
      
      // 获取当前班次状态
      await shiftService.refreshCurrentShift();
      final ShiftInfo? currentShift = shiftService.currentShift;
      
      // 检查班次状态是否发生变化
      if (_hasShiftChanged(currentShift)) {
        debugPrint('📊 班次状态发生变化');
        debugPrint('   之前班次: ${_lastKnownShift?.shiftId ?? '无'} (${_lastKnownShift?.status.name ?? '无'})');
        debugPrint('   当前班次: ${currentShift?.shiftId ?? '无'} (${currentShift?.status.name ?? '无'})');
        
        // 更新已知班次状态
        _lastKnownShift = currentShift;
        
        // 通知回调处理班次状态变化
        onShiftStatusChanged?.call(currentShift);
      }
    } catch (e) {
      debugPrint('⚠️ 班次状态轮询失败: $e');
      // 班次状态轮询失败不影响交易轮询，只记录错误
    }
  }

  /// 检查班次状态是否发生变化
  bool _hasShiftChanged(ShiftInfo? currentShift) {
    // 如果都为null，没有变化
    if (_lastKnownShift == null && currentShift == null) {
      return false;
    }
    
    // 一个为null一个不为null，发生变化
    if (_lastKnownShift == null || currentShift == null) {
      return true;
    }
    
    // 班次ID或状态发生变化
    return _lastKnownShift!.shiftId != currentShift.shiftId ||
           _lastKnownShift!.status != currentShift.status;
  }
}

/// BOS交易轮询服务Provider
final Provider<BosTransactionPollingService>
    bosTransactionPollingServiceProvider =
    Provider<BosTransactionPollingService>(
        (Ref<BosTransactionPollingService> ref) {
  return BosTransactionPollingService();
});
