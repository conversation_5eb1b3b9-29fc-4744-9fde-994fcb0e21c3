import 'package:edc_app/models/member_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../constants/bp_colors.dart';
import '../constants/customer_type_constants.dart';
import '../theme/app_theme.dart';
import '../models/payment_transaction_data.dart';
import '../models/fuel_transaction.dart';
import '../models/promotion_response.dart';
import '../controllers/promotion_controller.dart';
import '../controllers/dispenser_controller.dart';
import '../models/dispenser_model.dart';
import '../services/shared/member_data_service.dart';
import '../widgets/promotion_widget.dart';

/// Order details component
/// Unified display of order information, supports PaymentTransactionData data source
class OrderDetailWidget extends ConsumerStatefulWidget {
  const OrderDetailWidget({
    super.key,
    required this.paymentData,
    this.showMemberInfo = true,
    this.showPromotions = true,
    this.showPaymentSummary = true,
    this.promotionResponse,
    this.isLoadingPromotion = false,
  });

  /// Payment transaction data
  final PaymentTransactionData paymentData;

  /// Whether to show member information
  final bool showMemberInfo;

  /// Whether to show promotion information
  final bool showPromotions;

  /// Whether to show payment summary
  final bool showPaymentSummary;

  /// Promotion response data (if provided directly)
  final PromotionResponse? promotionResponse;

  /// Whether promotion is loading
  final bool isLoadingPromotion;

  @override
  ConsumerState<OrderDetailWidget> createState() => _OrderDetailWidgetState();
}

class _OrderDetailWidgetState extends ConsumerState<OrderDetailWidget> {
  final NumberFormat _currencyFormatter = NumberFormat.currency(
    locale: 'id_ID',
    symbol: 'Rp ',
    decimalDigits: 0,
  );

  // Nozzle data loading state
  bool _isLoadingNozzle = false;
  Nozzle? _nozzleModel;

  // Member data loading state
  bool _isLoadingMember = false;
  Map<String, dynamic> _memberData = <String, dynamic>{};

  @override
  void initState() {
    super.initState();

    // Asynchronously load Nozzle, member data and promotion data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadNozzleData();
      if (widget.showMemberInfo) {
        _loadMemberData();
      }
      // Only load promotion data if no direct promotion response is provided
      if (widget.showPromotions && widget.promotionResponse == null) {
        _loadPromotionData();
      }
    });
  }

  @override
  void didUpdateWidget(OrderDetailWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If transaction data changes, reload data
    if (oldWidget.paymentData != widget.paymentData) {
      _loadNozzleData();
      if (widget.showMemberInfo) {
        _loadMemberData();
      }
      // Only load promotion data if no direct promotion response is provided
      if (widget.showPromotions && widget.promotionResponse == null) {
        _loadPromotionData();
      }
    }
  }

  /// Asynchronously load member data
  Future<void> _loadMemberData() async {
    if (!mounted) return;

    setState(() {
      _isLoadingMember = true;
    });

    try {
      // Get member information from PaymentTransactionData
      final Member? memberInfo = widget.paymentData.memberInfo;

      if (memberInfo != null) {
        setState(() {
          _memberData = memberInfo.toJson();
          _isLoadingMember = false;
        });
      } else {
        // Try to get member information from member data service
        final Map<String, dynamic> memberData =
            await getMemberDataWithCachePriority(
          widget.paymentData.toJson(),
        );
        debugPrint('💳 OrderDetailWidget: Member data: $memberData');

        if (mounted) {
          setState(() {
            _memberData = memberData;
            _isLoadingMember = false;
          });
        }
      }
    } catch (e) {
      debugPrint('❌ Failed to load member data: $e');
      if (mounted) {
        setState(() {
          _memberData = <String, dynamic>{};
          _isLoadingMember = false;
        });
      }
    }
  }

  /// Asynchronously load promotion data
  void _loadPromotionData() {
    final PromotionController promotionController =
        ref.read(promotionControllerProvider.notifier);

    // Build FuelTransaction object from PaymentTransactionData to get promotion information
    try {
      final PaymentTransactionData paymentData = widget.paymentData;
      final String id = (paymentData.transactionId);

      if (id != null) {
        // Create simplified transaction object
        final FuelTransaction transaction = FuelTransaction(
          id: id,
          transactionNumber: paymentData.transactionRef,
          stationId: paymentData.stationId,
          pumpId: paymentData.pumpId,
          nozzleId: paymentData.nozzleId,
          fuelType: paymentData.fuelType,
          fuelGrade: paymentData.fuelGrade,
          volume: paymentData.volume,
          unitPrice: paymentData.unitPrice,
          amount: paymentData.totalAmount,
          status: paymentData.status,
          metadata: paymentData.metadata,
          createdAt: paymentData.createdAt,
          updatedAt: paymentData.createdAt,
        );

        // Load promotion data
        promotionController.loadFuelTransactionPromotion(transaction);
        debugPrint('🎯 OrderDetailWidget: Starting to load promotion data, transaction ID=$id');
      } else {
        debugPrint('⚠️ OrderDetailWidget: Invalid transaction ID, skipping promotion loading');
      }
    } catch (e) {
      // Print error but don't interrupt component rendering
      debugPrint('❌ OrderDetailWidget: Failed to load promotion data: ${e.toString()}');
    }
  }

  /// Asynchronously load Nozzle data
  Future<void> _loadNozzleData() async {
    if (!mounted) return;

    setState(() {
      _isLoadingNozzle = true;
    });

    try {
      // nozzleId is in string format, e.g., "device_com7_pump01-nozzle-1", use directly
      final String nozzleId = widget.paymentData.nozzleId;

      if (nozzleId.isEmpty) {
        debugPrint('❌ OrderDetailWidget: Empty nozzleId');
        setState(() {
          _isLoadingNozzle = false;
        });
        return;
      }

      debugPrint('🔍 OrderDetailWidget: Looking for nozzle ID: $nozzleId');

      final DispenserController dispenserController =
          ref.read(dispenserControllerProvider.notifier);

      // First try to get from cache
      final DispenserState dispenserState =
          ref.read(dispenserControllerProvider);
      Nozzle? foundNozzle;

      // Traverse all pump groups to find matching nozzle
      for (final PumpGroup pumpGroup in dispenserState.currentPumpGroups) {
        for (final Nozzle nozzle in pumpGroup.nozzles) {
          if (nozzle.id == nozzleId) {
            foundNozzle = nozzle;
            break;
          }
        }
        if (foundNozzle != null) break;
      }

      if (foundNozzle != null) {
        debugPrint(
            '✅ OrderDetailWidget: Found nozzle from cache ${foundNozzle.id}: ${foundNozzle.deviceName} - ${foundNozzle.name}');
        setState(() {
          _nozzleModel = foundNozzle;
          _isLoadingNozzle = false;
        });
      } else {
        // If not found in cache, try to refresh from API
        debugPrint('⚠️ OrderDetailWidget: Nozzle not found in cache, trying to refresh from API');
        await dispenserController.refreshNozzleStatus();

        if (mounted) {
          // Try to find again after loading
          final DispenserState updatedState =
              ref.read(dispenserControllerProvider);
          for (final PumpGroup pumpGroup in updatedState.currentPumpGroups) {
            for (final Nozzle nozzle in pumpGroup.nozzles) {
              if (nozzle.id == nozzleId) {
                foundNozzle = nozzle;
                break;
              }
            }
            if (foundNozzle != null) break;
          }

          setState(() {
            _nozzleModel = foundNozzle;
            _isLoadingNozzle = false;
          });

          if (foundNozzle != null) {
            debugPrint(
                '✅ OrderDetailWidget: Found nozzle from API ${foundNozzle.id}: ${foundNozzle.deviceName} - ${foundNozzle.name}');
          } else {
            debugPrint('❌ OrderDetailWidget: Nozzle not found even after API loading');
          }
        }
      }
    } catch (e) {
      debugPrint('❌ OrderDetailWidget: Failed to load nozzle data: $e');
      if (mounted) {
        setState(() {
          _isLoadingNozzle = false;
        });
      }
    }
  }

  /// Get amount due - considering promotions
  double get _amountDue {
    // Use direct promotion response if provided
    if (widget.promotionResponse != null && widget.promotionResponse!.discountAmount > 0) {
      return widget.promotionResponse!.discountedAmount; // 新API返回的是正确的金额，无需转换
    }
    
    // Fallback to promotion controller
    final PromotionState promotionState = ref.read(promotionControllerProvider);
    if (promotionState.promotionData != null && 
        promotionState.promotionData!.discountAmount > 0) {
      return promotionState.promotionData!.discountedAmount;
    }
    
    return widget.paymentData.totalAmount;
  }

  /// Get fuel information - prioritize FCC configuration
  Map<String, String> get _fuelInfo {
    String nozzleNumber;
    String fuelType;
    String fuelGrade;

    // Prioritize real FCC nozzle configuration from dispenserController
    if (_nozzleModel != null) {
      // Use deviceName + name format
      final String deviceName =
          _nozzleModel!.deviceName ?? 'Device${_nozzleModel!.pumpGroupId}';
      nozzleNumber = '$deviceName - ${_nozzleModel!.name}';
      // Use FCC Nozzle configuration fuel information (real configuration)
      fuelType = _nozzleModel!.fuelType;
      fuelGrade = _nozzleModel!.fuelGrade;
      debugPrint(
          '✅ OrderDetailWidget: Using FCC real configuration - $nozzleNumber, $fuelType, $fuelGrade');
    } else if (_isLoadingNozzle) {
      nozzleNumber = 'Loading...';
      fuelType = 'Loading...';
      fuelGrade = 'Loading...';
      debugPrint('⏳ OrderDetailWidget: Loading FCC configuration...');
    } else {
      // Fallback handling: try to build more friendly display name instead of raw ID
      final String pumpId = widget.paymentData.pumpId;
      final String nozzleId = widget.paymentData.nozzleId;

      // Try to extract meaningful information from ID
      if (pumpId.contains('device_com') && nozzleId.contains('nozzle')) {
        // Extract information from device_com7_pump01-nozzle-2 format
        final RegExpMatch? comMatch =
            RegExp(r'device_com(\d+)_pump(\d+)').firstMatch(pumpId);
        final RegExpMatch? nozzleMatch =
            RegExp(r'-nozzle-(\d+)').firstMatch(nozzleId);

        if (comMatch != null && nozzleMatch != null) {
          final String? comNumber = comMatch.group(1);
          final String? pumpNumber = comMatch.group(2);
          final String? nozzleNumber2 = nozzleMatch.group(1);
          nozzleNumber =
              'COM$comNumber Pump$pumpNumber - Nozzle $nozzleNumber2';
        } else {
          nozzleNumber = 'Pump $pumpId - Nozzle $nozzleId';
        }
      } else {
        nozzleNumber =
            'Pump ${widget.paymentData.pumpId} - Nozzle ${widget.paymentData.nozzleId}';
      }

      // Use PaymentTransactionData fuel type (from actual transaction data)
      // Remove estimation identifier because this is the fuel type used in actual transactions
      fuelType = widget.paymentData.fuelType.isNotEmpty
          ? widget.paymentData.fuelType
          : 'Unknown Fuel';
      fuelGrade = widget.paymentData.fuelGrade.isNotEmpty
          ? widget.paymentData.fuelGrade
          : 'Unknown Grade';
      debugPrint(
          '🔄 OrderDetailWidget: Using transaction data fuel type - $nozzleNumber, $fuelType, $fuelGrade');
    }

    // Price and volume information from PaymentTransactionData (actual transaction data)
    final String unitPrice =
        _currencyFormatter.format(widget.paymentData.unitPrice);
    final String volume = '${widget.paymentData.volume.toStringAsFixed(3)} L';

    return <String, String>{
      'nozzleNumber': nozzleNumber,
      'fuelType': fuelType,
      'fuelGrade': fuelGrade,
      'unitPrice': unitPrice,
      'volume': volume,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        // Amount information card (only show if enabled)
        if (widget.showPaymentSummary) ...<Widget>[
          _buildAmountInfoCard(),
          const SizedBox(height: 16),
        ],

        // Fuel details card
        _buildFuelDetailsCard(),

        // Member information card
        if (widget.showMemberInfo) ...<Widget>[
          const SizedBox(height: 16),
          _buildMemberInfoCard(),
        ],

        // Promotion information - always show, let PromotionWidget handle various states
        if (widget.showPromotions) ...<Widget>[
          const SizedBox(height: 16),
          _buildPromotionInfoCard(),
        ],
      ],
    );
  }

  /// Build amount information card
  Widget _buildAmountInfoCard() {
    final double amountDue = _amountDue;
    final bool hasDiscount = amountDue < widget.paymentData.totalAmount;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: BPColors.neutral.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            'Payment Summary',
            style: EDCTextStyles.subTitle.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: BPColors.primary,
            ),
          ),
          const SizedBox(height: 16),

          // Original amount (if there's discount)
          if (hasDiscount) ...<Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'Original Amount',
                  style: EDCTextStyles.bodyText.copyWith(
                    fontSize: 14,
                    color: BPColors.neutral,
                  ),
                ),
                Text(
                  _currencyFormatter.format(widget.paymentData.totalAmount),
                  style: EDCTextStyles.bodyText.copyWith(
                    fontSize: 14,
                    color: BPColors.neutral,
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],

          // Amount due
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                hasDiscount ? 'Amount Due' : 'Total Amount',
                style: EDCTextStyles.bodyText.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF333333),
                ),
              ),
              Text(
                _currencyFormatter.format(amountDue),
                style: EDCTextStyles.emphasizedText.copyWith(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: hasDiscount ? BPColors.success : BPColors.primary,
                ),
              ),
            ],
          ),

          // Discount amount (if applicable)
          if (hasDiscount) ...<Widget>[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'You Save',
                  style: EDCTextStyles.bodyText.copyWith(
                    fontSize: 14,
                    color: BPColors.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _currencyFormatter.format(widget.paymentData.totalAmount - amountDue),
                  style: EDCTextStyles.bodyText.copyWith(
                    fontSize: 14,
                    color: BPColors.success,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Build fuel details card
  Widget _buildFuelDetailsCard() {
    final Map<String, String> fuelInfo = _fuelInfo;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: BPColors.neutral.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // Simplified title, no icon
          Text(
            'Transaction Details',
            style: EDCTextStyles.subTitle.copyWith(
              fontSize: 16, // Reduce font from 18 to 16
              fontWeight: FontWeight.bold,
              color: BPColors.primary,
            ),
          ),
          const SizedBox(height: 16),

          // Use grid layout to display details
          _buildDetailRow('Nozzle Number', fuelInfo['nozzleNumber']!),
          const SizedBox(height: 12),
          _buildDetailRow('Payment Method',
              widget.paymentData.paymentMethodName ?? 'Not specified'),
          const SizedBox(height: 12),
          _buildDetailRow('Fuel Type', fuelInfo['fuelType']!),
          const SizedBox(height: 12),
          _buildDetailRow('Fuel Grade', fuelInfo['fuelGrade']!),
          const SizedBox(height: 12),
          Row(
            children: <Widget>[
              Expanded(
                  child:
                      _buildDetailItem('Unit Price', fuelInfo['unitPrice']!)),
              const SizedBox(width: 16),
              Expanded(child: _buildDetailItem('Volume', fuelInfo['volume']!)),
            ],
          ),
        ],
      ),
    );
  }

  /// Build member information card - using same style as member_info_bottom_bar
  Widget _buildMemberInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: BPColors.neutral.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            'Member Information',
            style: EDCTextStyles.subTitle.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: BPColors.primary,
            ),
          ),
          const SizedBox(height: 16),

          // Loading state
          if (_isLoadingMember) ...<Widget>[
            const Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(BPColors.primary),
                ),
              ),
            ),
          ]
          // Has member data
          else if (_memberData.isNotEmpty) ...<Widget>[
            _buildMemberContentWithBottomBarStyle(),
          ]
          // No member data
          else ...<Widget>[
            const Row(
              children: <Widget>[
                Icon(Icons.person_outline, color: Colors.grey, size: 20),
                SizedBox(width: 8),
                Text(
                  'No member information',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Build member content with bottom bar style
  Widget _buildMemberContentWithBottomBarStyle() {
    // Extract member data - handle both Member.toJson() format and API response format
    String plateNumber = 'Not Set';
    String displayCustomerType = 'B2C';
    String displayVehicle = 'Vehicle';
    String memberName = 'Not Set';

    debugPrint('🔍 OrderDetailWidget: Processing member data for license plate display');
    debugPrint('   _memberData keys: ${_memberData.keys.toList()}');
    debugPrint('   _memberData content: $_memberData');

    // Handle Member.toJson() format (when memberInfo is available)
    if (_memberData.containsKey('metadata')) {
      debugPrint('📋 Using Member.toJson() format (metadata structure)');
      final Map<String, dynamic>? metadata = _memberData['metadata'] as Map<String, dynamic>?;
      if (metadata != null) {
        debugPrint('   metadata keys: ${metadata.keys.toList()}');
        debugPrint('   metadata content: $metadata');

        // Get plate numbers from metadata
        final List<dynamic>? plateNumbers = metadata['plateNumbers'] as List<dynamic>?;
        if (plateNumbers != null && plateNumbers.isNotEmpty) {
          plateNumber = plateNumbers.first.toString();
          debugPrint('   ✅ Found plateNumbers in metadata: $plateNumber');
        } else {
          debugPrint('   ❌ No plateNumbers found in metadata');
        }

        // Get vehicle type from metadata
        final String? vehicle = metadata['vehicle']?.toString();
        if (vehicle != null && vehicle.isNotEmpty && vehicle != 'Unknown Vehicle') {
          displayVehicle = vehicle;
        }

        // Get customer type from metadata
        final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(metadata);
        displayCustomerType = detectedType.value;
        debugPrint('   Customer type detected: $displayCustomerType');
      }

      // Get member name
      memberName = _memberData['name']?.toString() ?? 'Not Set';
    }
    // Handle API response format (from getMemberDataWithCachePriority)
    else {
      debugPrint('📡 Using API response format (cache/service structure)');

      // Try multiple possible plate number fields
      final dynamic vehiclePlate = _memberData['vehicle_plate'];
      final dynamic plateNumbers = _memberData['plate_numbers'];
      final dynamic plateNumbersFromMetadata = _memberData['plateNumbers']; // From spread metadata

      debugPrint('   vehicle_plate: $vehiclePlate');
      debugPrint('   plate_numbers: $plateNumbers');
      debugPrint('   plateNumbers (from spread metadata): $plateNumbersFromMetadata');

      // Priority order: vehicle_plate -> plateNumbers (from spread metadata) -> plate_numbers
      if (vehiclePlate != null) {
        if (vehiclePlate is List && vehiclePlate.isNotEmpty) {
          plateNumber = vehiclePlate.first.toString();
          debugPrint('   ✅ Found license plate in vehicle_plate (List): $plateNumber');
        } else if (vehiclePlate is String && vehiclePlate.isNotEmpty) {
          plateNumber = vehiclePlate;
          debugPrint('   ✅ Found license plate in vehicle_plate (String): $plateNumber');
        }
      } else if (plateNumbersFromMetadata != null) {
        if (plateNumbersFromMetadata is List && plateNumbersFromMetadata.isNotEmpty) {
          plateNumber = plateNumbersFromMetadata.first.toString();
          debugPrint('   ✅ Found license plate in plateNumbers (from metadata): $plateNumber');
        } else if (plateNumbersFromMetadata is String && plateNumbersFromMetadata.isNotEmpty) {
          plateNumber = plateNumbersFromMetadata;
          debugPrint('   ✅ Found license plate in plateNumbers (String): $plateNumber');
        }
      } else if (plateNumbers != null) {
        if (plateNumbers is List && plateNumbers.isNotEmpty) {
          plateNumber = plateNumbers.first.toString();
          debugPrint('   ✅ Found license plate in plate_numbers (List): $plateNumber');
        } else if (plateNumbers is String && plateNumbers.isNotEmpty) {
          plateNumber = plateNumbers;
          debugPrint('   ✅ Found license plate in plate_numbers (String): $plateNumber');
        }
      } else {
        debugPrint('   ❌ No license plate found in any field');
      }

      // Get vehicle type
      final String? vehicle = _memberData['vehicle_type']?.toString() ?? _memberData['vehicle']?.toString();
      if (vehicle != null && vehicle.isNotEmpty && vehicle != 'Unknown Vehicle') {
        displayVehicle = vehicle;
      }

      // Get customer type
      final String? customerType = _memberData['customerType']?.toString() ?? _memberData['customer_type']?.toString();
      if (customerType != null && customerType.isNotEmpty) {
        displayCustomerType = customerType;
      }

      // Get member name
      memberName = _memberData['name']?.toString() ?? 'Not Set';
    }

    debugPrint('🎯 Final license plate display result:');
    debugPrint('   License plate: $plateNumber');
    debugPrint('   Customer type: $displayCustomerType');
    debugPrint('   Vehicle type: $displayVehicle');
    debugPrint('   Member name: $memberName');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // Member name/company name display (if available and not "Not Set")
        if (memberName != 'Not Set') ...<Widget>[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: displayCustomerType == 'B2B' ? BPColors.accent.withValues(alpha: 0.1) : BPColors.primary.withValues(alpha: 0.1),
              border: Border.all(
                color: displayCustomerType == 'B2B' ? BPColors.accent : BPColors.primary,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: <Widget>[
                Icon(
                  displayCustomerType == 'B2B' ? Icons.business : Icons.person,
                  size: 16,
                  color: displayCustomerType == 'B2B' ? BPColors.accent : BPColors.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    memberName,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: displayCustomerType == 'B2B' ? BPColors.accent : BPColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
        ],

        // License plate display
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.black87, width: 2),
            borderRadius: BorderRadius.circular(4),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                offset: const Offset(0, 2),
                blurRadius: 4,
              ),
            ],
          ),
          child: Text(
            plateNumber,
            style: const TextStyle(
              fontFamily: 'monospace',
              fontSize: 15,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
              letterSpacing: 1.2,
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Customer type and vehicle type tags
        Row(
          children: <Widget>[
            _buildInfoTag(
              displayCustomerType,
              displayCustomerType == 'B2B' ? BPColors.accent : BPColors.primary,
            ),
            const SizedBox(width: 12),
            _buildInfoTag(
              displayVehicle,
              BPColors.neutral,
            ),
          ],
        ),
      ],
    );
  }

  /// Build promotion information card
  Widget _buildPromotionInfoCard() {
    // Use direct promotion response if provided
    if (widget.promotionResponse != null) {
      return _buildDirectPromotionWidget(widget.promotionResponse!);
    }
    
    // Show loading state if promotion is loading
    if (widget.isLoadingPromotion) {
      return _buildPromotionLoadingWidget();
    }
    
    // Fallback to promotion controller
    return const PromotionWidget();
  }

  /// Build direct promotion widget
  Widget _buildDirectPromotionWidget(PromotionResponse promotion) {
    final bool hasDiscount = promotion.discountAmount > 0;
    final Color titleColor = hasDiscount ? BPColors.success : Colors.grey;
    final IconData titleIcon = hasDiscount ? Icons.discount_outlined : Icons.receipt_outlined;
    final String titleText = hasDiscount ? 'Promotion Applied' : 'Payment Summary';

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: BPColors.neutral.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // Title bar
          Row(
            children: <Widget>[
              Icon(titleIcon, color: titleColor, size: 20),
              const SizedBox(width: 8),
              Text(
                titleText,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: titleColor,
                ),
              ),
              const Spacer(),
              if (hasDiscount)
                Text(
                  '- ${_currencyFormatter.format(promotion.discountAmount)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: titleColor,
                  ),
                )
              else
                Text(
                  _currencyFormatter.format(promotion.originalAmount),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
            ],
          ),

          // Applied promotions list
          if (hasDiscount && promotion.appliedPromotions.isNotEmpty) ...<Widget>[
            const SizedBox(height: 16),
            ...promotion.appliedPromotions.map((appliedPromotion) => 
              _buildAppliedPromotionItem(appliedPromotion)
            ).toList(),
          ],

          // Success message
          if (promotion.message.isNotEmpty) ...<Widget>[
            const SizedBox(height: 12),
            Row(
              children: <Widget>[
                const Icon(Icons.info_outline, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    promotion.message,
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Build applied promotion item
  Widget _buildAppliedPromotionItem(AppliedPromotion appliedPromotion) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: BPColors.success.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: BPColors.success.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              Icon(
                Icons.local_offer,
                size: 16,
                color: BPColors.success,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  appliedPromotion.promotionName,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: BPColors.success,
                  ),
                ),
              ),
              Text(
                                  '- ${_currencyFormatter.format(appliedPromotion.discountAmount)}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: BPColors.success,
                ),
              ),
            ],
          ),
          if (appliedPromotion.description.isNotEmpty) ...<Widget>[
            const SizedBox(height: 4),
            Text(
              appliedPromotion.description,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build promotion loading widget
  Widget _buildPromotionLoadingWidget() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: BPColors.neutral.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: const Row(
        children: <Widget>[
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(BPColors.primary),
            ),
          ),
          SizedBox(width: 12),
          Text(
            'Loading promotion information...',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// Build tag with colored dot - similar to member_info_bottom_bar
  Widget _buildInfoTag(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// Build detail row
  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: EDCTextStyles.bodyText.copyWith(
              fontSize: 14,
              color: BPColors.neutral,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: EDCTextStyles.bodyText.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF333333), // Dark Gray from design document
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// Build detail item
  Widget _buildDetailItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          label,
          style: EDCTextStyles.bodyText.copyWith(
            fontSize: 12,
            color: BPColors.neutral,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: EDCTextStyles.bodyText.copyWith(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF333333), // Dark Gray from design document
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}
