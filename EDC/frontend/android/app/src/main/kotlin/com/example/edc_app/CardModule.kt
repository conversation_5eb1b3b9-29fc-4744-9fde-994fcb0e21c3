package com.example.edc_app

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.RemoteException
import android.util.Log
import io.flutter.plugin.common.MethodChannel
import sunmi.paylib.SunmiPayKernel
import com.sunmi.pay.hardware.aidlv2.readcard.ReadCardOptV2
import com.sunmi.pay.hardware.aidlv2.AidlErrorCodeV2
import com.sunmi.pay.hardware.aidlv2.readcard.CheckCardCallbackV2
import com.sunmi.pay.hardware.aidlv2.AidlConstantsV2
import java.util.*
import com.example.edc_app.EdcApplication

/**
 * 卡模块实现类
 * 负责调用商米SDK实现卡模块功能
 */
class CardModule(private val context: Context) {
    companion object {
        private const val TAG = "CardModule"
    }

    private val mainHandler = Handler(Looper.getMainLooper())
    
    private var checkCardCallback: CheckCardCallbackV2? = null
    private var resultCallback: MethodChannel.Result? = null
    private var channel: MethodChannel? = null
    
    /**
     * 设置 MethodChannel (如果需要回调 Flutter)
     */
    fun setChannel(channel: MethodChannel) {
        this.channel = channel
        Log.d(TAG, "MethodChannel set for CardModule")
    }
    
    /**
     * 开始检测NFC卡
     */
    fun startCheckNFCCard(result: MethodChannel.Result) {
        Log.i(TAG, "=== Starting NFC Card Check ===")
        Log.d(TAG, "startCheckNFCCard called from Flutter")

        // 检查应用连接状态
        val app = EdcApplication.instance
        Log.d(TAG, "EdcApplication instance: $app")
        Log.d(TAG, "PaySDK connection status: ${app.isConnectPaySDK()}")
        Log.d(TAG, "ReadCard status: ${app.getReadCardStatus()}")

        val currentReadCardOptV2 = app.readCardOptV2
        Log.d(TAG, "ReadCardOptV2 instance: $currentReadCardOptV2")

        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "❌ readCardOptV2 is null when trying to start NFC check")
            Log.e(TAG, "SDK connection status: ${app.isConnectPaySDK()}")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接或CardModule未正确初始化", null)
            return
        }

        try {
            Log.i(TAG, "✅ ReadCardOptV2 is available, creating callback...")
            Log.d(TAG, "ReadCardOptV2 instance details: $currentReadCardOptV2")
            resultCallback = result

            Log.d(TAG, "Creating CheckCardCallbackV2 implementation...")
            checkCardCallback = object : CheckCardCallbackV2.Stub() {
                @Throws(RemoteException::class)
                override fun findMagCard(info: Bundle) {
                    Log.i(TAG, "🔍 Magnetic card detected!")
                    Log.d(TAG, "findMagCard callback triggered with info: $info")
                    // 记录磁条卡信息
                    logBundleContents("MagCard", info)
                }

                @Throws(RemoteException::class)
                override fun findICCardEx(info: Bundle) {
                    Log.i(TAG, "🔍 IC card detected!")
                    Log.d(TAG, "findICCardEx callback triggered with info: $info")
                    // 记录IC卡信息
                    logBundleContents("ICCard", info)
                }

                @Throws(RemoteException::class)
                override fun findRFCardEx(info: Bundle) {
                    Log.i(TAG, "🔍 NFC/RF card detected!")
                    Log.d(TAG, "findRFCardEx callback triggered from Binder thread with info: $info")
                    logBundleContents("RFCard", info)
                    val cardType = info.getInt("cardType")
                    val uuid = info.getString("uuid") ?: ""
                    val ats = info.getString("ats") ?: ""
                    val cardCategory = info.getInt("cardCategory")
                    
                    val cardInfo = HashMap<String, Any>()
                    cardInfo["cardType"] = getCardTypeName(cardType)
                    cardInfo["cardCategory"] = getCardCategoryName(cardCategory)
                    cardInfo["uuid"] = uuid
                    cardInfo["ats"] = ats
                    
                    mainHandler.post {
                        Log.i(TAG, "📱 Posting onCardDetected to main thread")
                        Log.d(TAG, "Card info being sent to Flutter: $cardInfo")
                        channel?.invokeMethod("onCardDetected", cardInfo)
                        Log.d(TAG, "✅ onCardDetected method invoked successfully")
                    }
                }

                @Throws(RemoteException::class)
                override fun onErrorEx(info: Bundle) {
                    Log.e(TAG, "❌ Card read error occurred!")
                    Log.d(TAG, "onErrorEx callback triggered from Binder thread with info: $info")
                    logBundleContents("Error", info)
                    val code = info.getInt("code")
                    val message = info.getString("message") ?: "未知错误"
                    
                    val errorInfo = HashMap<String, Any>()
                    errorInfo["code"] = code
                    errorInfo["message"] = message
                    
                    mainHandler.post {
                        Log.e(TAG, "📱 Posting onCardError to main thread")
                        Log.d(TAG, "Error info being sent to Flutter: $errorInfo")
                        channel?.invokeMethod("onCardError", errorInfo)
                        Log.d(TAG, "✅ onCardError method invoked successfully")
                    }
                }

                override fun findICCard(atr: String?) {
                    Log.i(TAG, "🔍 Legacy IC card callback: $atr")
                }
                override fun findRFCard(uuid: String?) {
                    Log.i(TAG, "🔍 Legacy RF card callback: $uuid")
                }
                override fun onError(code: Int, message: String?) {
                    Log.e(TAG, "❌ Legacy error callback: code=$code, message=$message")
                }
            }

            // 配置读卡参数
            val cardType = 16 or 1 or 4 or 8  // 支持所有卡片类型
            val timeout = 60  // 60秒超时

            Log.i(TAG, "🚀 Starting card detection...")
            Log.d(TAG, "Card type mask: $cardType (${getCardTypeMaskDescription(cardType)})")
            Log.d(TAG, "Timeout: ${timeout}s")
            Log.d(TAG, "Callback instance: $checkCardCallback")

            // 调用读卡方法
            Log.d(TAG, "Calling currentReadCardOptV2.checkCard()...")
            currentReadCardOptV2.checkCard(cardType, checkCardCallback, timeout)
            Log.i(TAG, "✅ checkCard() method called successfully")
            Log.i(TAG, "⏳ Waiting for card detection callbacks...")

            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "❌ Exception in startCheckNFCCard: ${e.message}", e)
            Log.e(TAG, "Exception type: ${e.javaClass.simpleName}")
            Log.e(TAG, "Stack trace: ${e.stackTrace.joinToString("\n")}")
            result.error("CHECK_CARD_ERROR", "检测NFC卡出错: ${e.message}", null)
        }
    }

    /**
     * 记录Bundle内容的辅助方法
     */
    private fun logBundleContents(prefix: String, bundle: Bundle?) {
        try {
            Log.d(TAG, "=== $prefix Bundle Contents ===")
            if (bundle == null) {
                Log.d(TAG, "Bundle is null")
                return
            }

            for (key in bundle.keySet()) {
                val value = bundle.get(key)
                Log.d(TAG, "$key: $value (${value?.javaClass?.simpleName})")
            }
            Log.d(TAG, "=== End $prefix Bundle ===")
        } catch (e: Exception) {
            Log.e(TAG, "Error logging bundle contents: ${e.message}")
        }
    }

    /**
     * 获取卡片类型掩码描述
     */
    private fun getCardTypeMaskDescription(cardType: Int): String {
        val types = mutableListOf<String>()
        if (cardType and 1 != 0) types.add("Magnetic")
        if (cardType and 4 != 0) types.add("IC")
        if (cardType and 8 != 0) types.add("NFC")
        if (cardType and 16 != 0) types.add("RF")
        return types.joinToString("|")
    }
    
    /**
     * 停止检测NFC卡
     */
    fun stopCheckNFCCard(result: MethodChannel.Result) {
        Log.i(TAG, "=== Stopping NFC Card Check ===")
        Log.d(TAG, "stopCheckNFCCard called from Flutter")

        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        Log.d(TAG, "ReadCardOptV2 instance: $currentReadCardOptV2")

        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "❌ readCardOptV2 is null when trying to stop NFC check")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null)
            return
        }

        try {
            Log.d(TAG, "Turning off card reader...")
            currentReadCardOptV2.cardOff(16)
            Log.d(TAG, "Card reader turned off")

            Log.d(TAG, "Cancelling card check...")
            currentReadCardOptV2.cancelCheckCard()
            Log.d(TAG, "Card check cancelled")

            Log.d(TAG, "Clearing callbacks...")
            checkCardCallback = null
            resultCallback = null
            Log.d(TAG, "Callbacks cleared")

            Log.i(TAG, "✅ NFC card check stopped successfully")
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error stopping NFC card check: ${e.message}", e)
            result.error("CANCEL_CHECK_CARD_ERROR", "停止检测NFC卡出错: ${e.message}", null)
        }
    }
    
    /**
     * M1卡认证
     */
    fun m1Auth(sector: Int, keyType: Int, key: String, result: MethodChannel.Result) {
        Log.d(TAG, "M1 auth - sector: $sector, keyType: $keyType, key: $key")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "readCardOptV2 is null when trying M1 auth.")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null)
            return
        }
        try {
            val keyBytes = hexStringToByteArray(key)
            val block = sector * 4
            val code = currentReadCardOptV2.mifareAuth(keyType, block, keyBytes) ?: -1
            
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> AidlErrorCodeV2.valueOf(code).msg
                }
                
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = "认证失败: $errorMsg"
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 auth: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "认证异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 读取M1卡数据块
     */
    fun m1ReadBlock(sector: Int, block: Int, result: MethodChannel.Result) {
        Log.d(TAG, "M1 read block - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val blockData = ByteArray(16)
            val code = currentReadCardOptV2.mifareReadBlock(blockIndex, blockData) ?: -1
            if (code >= 0 && code <= 16) {
                val hexStr = bytesToHexString(blockData.copyOf(code))
                val response = HashMap<String, Any>()
                response["success"] = true
                val data = HashMap<String, Any>()
                data["block$block"] = hexStr
                response["data"] = data
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "读取失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 read block: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "读取异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 写入M1卡数据块
     */
    fun m1WriteBlock(sector: Int, block: Int, data: String, result: MethodChannel.Result) {
        Log.d(TAG, "M1 write block - sector: $sector, block: $block, data: $data")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val blockData = hexStringToByteArray(data)
            val code = currentReadCardOptV2.mifareWriteBlock(blockIndex, blockData) ?: -1
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "写入失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 write block: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "写入异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 初始化M1卡钱包
     */
    fun m1InitWallet(sector: Int, block: Int, result: MethodChannel.Result) {
        Log.d(TAG, "M1 init wallet - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val initData = getInitWalletData(blockIndex)
            val code = currentReadCardOptV2.mifareWriteBlock(blockIndex, initData) ?: -1
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "初始化钱包失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 init wallet: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "初始化钱包异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 获取M1卡钱包余额
     */
    fun m1GetBalance(sector: Int, block: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 get balance - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val blockData = ByteArray(16)
            val code = currentReadCardOptV2.mifareReadBlock(blockIndex, blockData) ?: -1
            if (code >= 0 && code <= 16) {
                val balance = getInt32FromBytes(blockData, 0, true)
                val response = HashMap<String, Any>()
                response["success"] = true
                response["balance"] = balance
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "获取余额失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 get balance: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "获取余额异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 增加M1卡钱包金额
     */
    fun m1IncreaseValue(sector: Int, block: Int, amount: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 increase value - sector: $sector, block: $block, amount: $amount")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val amountData = int32ToBytes(amount, true)
            val code = currentReadCardOptV2.mifareIncValue(blockIndex, amountData) ?: -1
            if (code == 0) {
                m1GetBalance(sector, block, result)
                return
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "增加金额失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 increase value: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "增加金额异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 减少M1卡钱包金额
     */
    fun m1DecreaseValue(sector: Int, block: Int, amount: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 decrease value - sector: $sector, block: $block, amount: $amount")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val amountData = int32ToBytes(amount, true)
            val code = currentReadCardOptV2.mifareDecValue(blockIndex, amountData) ?: -1
            if (code == 0) {
                m1GetBalance(sector, block, result)
                return
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "减少金额失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 decrease value: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "减少金额异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * M1卡恢复操作
     */
    fun m1Restore(sector: Int, block: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 restore - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val code = currentReadCardOptV2.mifareRestore(blockIndex) ?: -1
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "恢复操作失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 restore: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "恢复操作异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 获取卡类型名称
     */
    private fun getCardTypeName(cardType: Int): String {
        return when (cardType) {
            1 -> "MIFARE" // AidlConstantsV2.CardType.MIFARE
            2 -> "MIFARE Ultralight" // AidlConstantsV2.CardType.MIFARE_UL
            4 -> "FeliCa" // AidlConstantsV2.CardType.FELICA
            8 -> "ISO15693" // AidlConstantsV2.CardType.ISO15693
            16 -> "NFC" // AidlConstantsV2.CardType.NFC
            else -> "未知卡类型($cardType)"
        }
    }
    
    /**
     * 获取卡类别名称
     */
    private fun getCardCategoryName(cardCategory: Int): String {
        return when (cardCategory.toChar()) {
            'A' -> "A"
            'B' -> "B"
            else -> "未知类别($cardCategory)"
        }
    }
    
    /**
     * 16进制字符串转字节数组
     */
    private fun hexStringToByteArray(hexString: String): ByteArray {
        val hexStr = hexString.replace(" ", "")
        val len = hexStr.length
        val result = ByteArray(len / 2)
        
        for (i in 0 until len step 2) {
            val high = Character.digit(hexStr[i], 16)
            val low = Character.digit(hexStr[i + 1], 16)
            result[i / 2] = ((high shl 4) or low).toByte()
        }
        
        return result
    }
    
    /**
     * 字节数组转16进制字符串
     */
    private fun bytesToHexString(bytes: ByteArray): String {
        val hexChars = CharArray(bytes.size * 2)
        
        for (i in bytes.indices) {
            val v = bytes[i].toInt() and 0xFF
            hexChars[i * 2] = "0123456789ABCDEF"[v ushr 4]
            hexChars[i * 2 + 1] = "0123456789ABCDEF"[v and 0x0F]
        }
        
        return String(hexChars)
    }
    
    /**
     * 获取初始化钱包数据
     */
    private fun getInitWalletData(blockIndex: Int): ByteArray {
        val result = byteArrayOf(
            0x00, 0x00, 0x00, 0x00,
            0xFF.toByte(), 0xFF.toByte(), 0xFF.toByte(), 0xFF.toByte(),
            0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00
        )
        
        result[12] = (blockIndex and 0xFF).toByte()
        result[13] = (blockIndex and 0xFF).inv().toByte()
        result[14] = (blockIndex and 0xFF).toByte()
        result[15] = (blockIndex and 0xFF).inv().toByte()
        
        return result
    }
    
    /**
     * 字节数组转32位整数
     */
    private fun getInt32FromBytes(bytes: ByteArray, offset: Int, littleEndian: Boolean): Int {
        var result = 0
        
        if (littleEndian) {
            result = result or (bytes[offset].toInt() and 0xFF)
            result = result or ((bytes[offset + 1].toInt() and 0xFF) shl 8)
            result = result or ((bytes[offset + 2].toInt() and 0xFF) shl 16)
            result = result or ((bytes[offset + 3].toInt() and 0xFF) shl 24)
        } else {
            result = result or ((bytes[offset].toInt() and 0xFF) shl 24)
            result = result or ((bytes[offset + 1].toInt() and 0xFF) shl 16)
            result = result or ((bytes[offset + 2].toInt() and 0xFF) shl 8)
            result = result or (bytes[offset + 3].toInt() and 0xFF)
        }
        
        return result
    }
    
    /**
     * 32位整数转字节数组
     */
    private fun int32ToBytes(value: Int, littleEndian: Boolean): ByteArray {
        val bytes = ByteArray(4)
        
        if (littleEndian) {
            bytes[0] = (value and 0xFF).toByte()
            bytes[1] = ((value ushr 8) and 0xFF).toByte()
            bytes[2] = ((value ushr 16) and 0xFF).toByte()
            bytes[3] = ((value ushr 24) and 0xFF).toByte()
        } else {
            bytes[0] = ((value ushr 24) and 0xFF).toByte()
            bytes[1] = ((value ushr 16) and 0xFF).toByte()
            bytes[2] = ((value ushr 8) and 0xFF).toByte()
            bytes[3] = (value and 0xFF).toByte()
        }
        
        return bytes
    }
} 