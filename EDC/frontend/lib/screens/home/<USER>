import 'package:flutter/material.dart';
import '../order/order_home_page.dart';
import '../settings/settings_home_page.dart';
import '../shift/shift_home_page.dart';
import 'transaction_preset_home_page_v3.dart';
import '../../widgets/safe_scaffold.dart';
import '../../constants/bp_colors.dart';
import '../../theme/app_theme.dart';

class MainNavigationPage extends StatefulWidget {
  const MainNavigationPage({super.key});

  @override
  State<MainNavigationPage> createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends State<MainNavigationPage> {
  int _currentIndex = 0;

  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pages = <Widget>[
      const TransactionPresetHomePageV3(),
      const OrderHomePage(),
      const ShiftHomePage(),
      const MockMarketingPage(),
      const SettingsHomePage(),
    ];
  }

  final List<String> _titles = <String>[
    'Fuel',
    'Receipts',
    'Shift',
    'Promotion',
    'Settings',
  ];

  final List<IconData> _icons = <IconData>[
    Icons.credit_card,
    Icons.receipt_long,
    Icons.schedule,
    Icons.local_offer,
    Icons.settings,
  ];

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) return;
      },
      child: SafeScaffold(
        body: _pages[_currentIndex],
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: (int index) {
              setState(() {
                _currentIndex = index;
              });
            },
            type: BottomNavigationBarType.fixed,
            selectedItemColor: BPColors.primary,
            unselectedItemColor: BPColors.neutral,
            backgroundColor: Colors.white,
            elevation: 0,
            selectedLabelStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.normal,
            ),
            iconSize: 28,
            items: List.generate(
              _titles.length,
              (int index) => BottomNavigationBarItem(
                icon: Icon(_icons[index]),
                label: _titles[index],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Mock Marketing Page with two promotions
class MockMarketingPage extends StatelessWidget {
  const MockMarketingPage({super.key});

  // Promotion data with exact content as specified
  static final List<Map<String, dynamic>> _mockPromotions =
      <Map<String, dynamic>>[
    // Free 1 Liter Promotion
    <String, dynamic>{
      'id': '1',
      'name': 'Free 1 Liter',
      'type': 'Direct Discount',
      'description': 'Minimum purchase 25 liters above can get discount 1 liter / transaction',
      'startDate': '1 Jan 2025',
      'endDate': '31 Dec 2025',
      'vehicleType': 'All type',
      'product': 'BP 92, BP Ultimate, BP Ultimate Diesel',
      'coverageSite': 'All Site',
      'minPurchase': '25 liters above',
      'discount': '1 liter / transaction',
      'icon': Icons.local_gas_station,
      'color': const Color(0xFF4CAF50), // Green for free promotions
    },
    // Promo IRIT
    <String, dynamic>{
      'id': '2',
      'name': 'Promo IRIT',
      'type': 'Tiered Discount',
      'description': 'Multiple promotion with different discount rates',
      'startDate': '1 Jul 2025',
      'endDate': '31 Jul 2025',
      'vehicleType': 'All type',
      'product': 'BP 92, BP Ultimate, BP Ultimate Diesel',
      'coverageSite': 'All Site',
      'minPurchase': 'Various',
      'discount': 'Multiple rates',
      'icon': Icons.layers,
      'color': const Color(0xFF2196F3), // Blue for tiered discounts
      'tieredRules': [
        {
          'name': 'Extra Hemat 28L – BP 92',
          'rule': 'Minimum purchase 28 Liter BP 92 gets discount Rp 15.000 / trx',
        },
        {
          'name': 'Extra Hemat 28L – BP Ultimate',
          'rule': 'Minimum purchase 28 Liter BP Ultimate gets discount Rp 20.000 / trx',
        },
        {
          'name': 'Extra Hemat 28L – BP Ultimate Diesel',
          'rule': 'Minimum purchase 28 Liter BP Ultimate Diesel gets discount Rp 20.000 / trx',
        },
        {
          'name': 'Extra Hemat 3L – BP 92',
          'rule': 'Minimum purchase 3 Liter BP 92 gets discount Rp 1.000 / trx',
        },
        {
          'name': 'Extra Hemat 3L – BP Ultimate',
          'rule': 'Minimum purchase 3 Liter BP Ultimate gets discount Rp 1.500 / trx',
        },
        {
          'name': 'Extra Hemat 3L – BP Ultimate Diesel',
          'rule': 'Minimum purchase 3 Liter BP Ultimate Diesel gets discount Rp 1.500 / trx',
        },
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    return SafeScaffold(
      appBar: AppBar(
        title: const Text(
          'Promotion',
          style: EDCTextStyles.appBarTitle,
        ),
        backgroundColor: BPColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: 64,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // Header section
          Container(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.withOpacity(0.1),
                  width: 1,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'Active Promotions',
                  style: EDCTextStyles.subTitle.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Current available fuel promotions',
                  style: EDCTextStyles.hintText.copyWith(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // Promotions list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
              itemCount: _mockPromotions.length,
              itemBuilder: (BuildContext context, int index) {
                final Map<String, dynamic> promotion = _mockPromotions[index];
                return _buildPromotionCard(context, promotion);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromotionCard(
      BuildContext context, Map<String, dynamic> promotion) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      color: Colors.white,
      child: InkWell(
        onTap: () {
          _showPromotionDetails(context, promotion);
        },
        borderRadius: BorderRadius.circular(8),
        child: Column(
          children: <Widget>[
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: (promotion['color'] as Color).withOpacity(0.08),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: <Widget>[
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: (promotion['color'] as Color).withOpacity(0.15),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      promotion['icon'] as IconData,
                      color: promotion['color'] as Color,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          promotion['name'] as String,
                          style: EDCTextStyles.bodyText.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: promotion['color'] as Color,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          promotion['type'] as String,
                          style: EDCTextStyles.hintText.copyWith(
                            fontSize: 12,
                            color:
                                (promotion['color'] as Color).withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: (promotion['color'] as Color).withOpacity(0.6),
                    size: 20,
                  ),
                ],
              ),
            ),

            // Content
            Container(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    promotion['description'] as String,
                    style: EDCTextStyles.bodyText.copyWith(
                      fontSize: 14,
                      height: 1.4,
                    ),
                    softWrap: true,
                    overflow: TextOverflow.visible,
                  ),
                  const SizedBox(height: 12),

                  // Details
                  _buildSimpleDetailRow('Period',
                      '${promotion['startDate'] as String} - ${promotion['endDate'] as String}'),
                  _buildSimpleDetailRow(
                      'Vehicle', promotion['vehicleType'] as String),
                  _buildSimpleDetailRow('Product', promotion['product'] as String),
                  _buildSimpleDetailRow(
                      'Coverage', promotion['coverageSite'] as String),

                  const SizedBox(height: 8),
                  _buildDiscountInfo(promotion['minPurchase'] as String,
                      promotion['discount'] as String),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            width: 70,
            child: Text(
              '$label:',
              style: EDCTextStyles.hintText.copyWith(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: EDCTextStyles.bodyText.copyWith(
                fontSize: 12,
                color: Colors.grey[800],
              ),
              softWrap: true,
              overflow: TextOverflow.visible,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDiscountInfo(String minPurchase, String discount) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: BPColors.primary.withOpacity(0.08),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: BPColors.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: <Widget>[
          const Icon(
            Icons.local_offer,
            color: BPColors.primary,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  'Min. $minPurchase',
                  style: EDCTextStyles.bodyText.copyWith(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: BPColors.primary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Get $discount',
                  style: EDCTextStyles.bodyText.copyWith(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: BPColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTieredDiscountInfo(List<Map<String, String>> tiers) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: BPColors.accent.withOpacity(0.08),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: BPColors.accent.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              const Icon(
                Icons.layers,
                color: BPColors.accent,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Tiered Discounts:',
                style: EDCTextStyles.bodyText.copyWith(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: BPColors.accent,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          ...tiers.map((Map<String, String> tier) => Padding(
                padding: const EdgeInsets.only(bottom: 2.0),
                child: Text(
                  '• ${tier['minPurchase']} → ${tier['discount']}',
                  style: EDCTextStyles.hintText.copyWith(
                    fontSize: 12,
                    color: BPColors.accent,
                  ),
                ),
              )),
        ],
      ),
    );
  }

  void _showPromotionDetails(
      BuildContext context, Map<String, dynamic> promotion) {
    // 确定表格颜色主题
    final bool isFreePromo = (promotion['name'] as String).contains('Free');
    final Color headerColor = isFreePromo ? const Color(0xFF4CAF50) : const Color(0xFF2196F3);
    final Color rowColor = isFreePromo ? const Color(0xFFE8F5E8) : const Color(0xFFE3F2FD);

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (BuildContext context) => _PromotionDetailPage(
          promotion: promotion,
          headerColor: headerColor,
          rowColor: rowColor,
        ),
      ),
    );
  }
}

class _PromotionDetailPage extends StatelessWidget {
  final Map<String, dynamic> promotion;
  final Color headerColor;
  final Color rowColor;

  const _PromotionDetailPage({
    required this.promotion,
    required this.headerColor,
    required this.rowColor,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        backgroundColor: headerColor,
        foregroundColor: Colors.white,
        elevation: 0,
        title: Row(
          children: <Widget>[
            Icon(
              promotion['icon'] as IconData,
              color: Colors.white,
              size: 24,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                promotion['name'] as String,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // 判断是否有 tieredRules
            if (promotion['tieredRules'] != null)
              _buildTieredRulesTable(
                (promotion['tieredRules'] as List)
                    .map((e) => Map<String, String>.from(e))
                    .toList(),
              )
            else
              // 原有的单行表格
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                child: Column(
                  children: <Widget>[
                    // 表头
                    Container(
                      decoration: BoxDecoration(
                        color: headerColor,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              child: const Text(
                                'Name of Promotion',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                          Container(
                            width: 1,
                            height: 50,
                            color: Colors.white.withOpacity(0.3),
                          ),
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              child: const Text(
                                'Promotion Rules',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 内容行
                    Container(
                      decoration: BoxDecoration(
                        color: rowColor,
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              child: Text(
                                '- ${promotion['name'] as String}',
                                style: const TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                          Container(
                            width: 1,
                            color: Colors.grey.shade300,
                          ),
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              child: Text(
                                promotion['description'] as String,
                                style: const TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                ),
                                softWrap: true,
                                overflow: TextOverflow.visible,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 24),
            _buildDetailTable(promotion),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailTable(Map<String, dynamic> promotion) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: Column(
        children: <Widget>[
          _buildDetailRow('Period', '${promotion['startDate']} – ${promotion['endDate']}', isFirst: true),
          _buildDetailRow('Vehicle', promotion['vehicleType'] as String),
          _buildDetailRow('Product', promotion['product'] as String),
          _buildDetailRow('Coverage', promotion['coverageSite'] as String, isLast: true),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isFirst = false, bool isLast = false}) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: isLast ? BorderSide.none : BorderSide(color: Colors.grey.shade300),
        ),
        borderRadius: BorderRadius.only(
          topLeft: isFirst ? const Radius.circular(12) : Radius.zero,
          topRight: isFirst ? const Radius.circular(12) : Radius.zero,
          bottomLeft: isLast ? const Radius.circular(12) : Radius.zero,
          bottomRight: isLast ? const Radius.circular(12) : Radius.zero,
        ),
      ),
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                border: Border(
                  right: BorderSide(color: Colors.grey.shade300),
                ),
              ),
              child: Text(
                label,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 15,
                ),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Text(
                value,
                style: const TextStyle(
                  fontSize: 15,
                ),
                softWrap: true,
                overflow: TextOverflow.visible,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTieredRulesTable(List<Map<String, String>> rules) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            blurRadius: 2,
          ),
        ],
      ),
      child: Column(
        children: [
          // 表头
          Container(
            decoration: BoxDecoration(
              color: Color(0xFF2196F3),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 4, // 40%
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      'Name of Promotion',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 6, // 60%
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      'Promotion Rules',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 内容行
          ...rules.asMap().entries.map((entry) {
            final idx = entry.key;
            final rule = entry.value;
            return Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Color(0xFFE3F2FD),
                    borderRadius: BorderRadius.only(
                      bottomLeft: idx == rules.length - 1 ? const Radius.circular(12) : Radius.zero,
                      bottomRight: idx == rules.length - 1 ? const Radius.circular(12) : Radius.zero,
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 4,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text(
                            '- ${rule['name']}',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 15,
                              color: Color(0xFF2196F3),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 6,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text(
                            rule['rule']!,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 15,
                              color: Color(0xFF2196F3),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // 行间白色分隔线，最后一行不加
                if (idx != rules.length - 1)
                  Container(
                    height: 1,
                    color: Colors.white,
                  ),
              ],
            );
          }),
        ],
      ),
    );
  }
}

// 占位组件用于未完成的页面
class PlaceholderWidget extends StatelessWidget {
  const PlaceholderWidget({super.key, required this.message});
  final String message;

  @override
  Widget build(BuildContext context) {
    return SafeScaffold(
      appBar: AppBar(
        title: Row(
          children: <Widget>[
            Image.asset(
              'assets/images/bp_logo.png',
              height: 36,
              fit: BoxFit.contain,
              errorBuilder:
                  (BuildContext context, Object error, StackTrace? stackTrace) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'BP',
                    style: TextStyle(
                      color: BPColors.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(width: 12),
            Text(
              _getTitleFromMessage(message),
              style: EDCTextStyles.appBarTitle,
            ),
          ],
        ),
        backgroundColor: BPColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: 64,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(
              Icons.construction,
              size: 72,
              color: BPColors.neutral,
            ),
            const SizedBox(height: 24),
            Text(
              message,
              style: EDCTextStyles.subTitle.copyWith(
                color: BPColors.neutral,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              'This feature will be available soon',
              style: EDCTextStyles.hintText,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('$message will be available soon'),
                    backgroundColor: BPColors.primary,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: BPColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                minimumSize: const Size(120, 48),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Coming Soon',
                style: EDCTextStyles.buttonText,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getTitleFromMessage(String message) {
    if (message.contains('Member')) {
      return 'Members';
    } else if (message.contains('Marketing')) {
      return 'Marketing';
    }
    return message.split(' ').first;
  }
}
