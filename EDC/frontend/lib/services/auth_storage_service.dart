// EDC/frontend/lib/services/auth_storage_service.dart

/// 认证数据安全存储服务
/// 负责缓存用户信息、令牌和权限数据

import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/auth_models.dart';
import 'log_service.dart';

/// 认证存储服务类
class AuthStorageService {
  AuthStorageService._();
  
  static final AuthStorageService _instance = AuthStorageService._();
  static AuthStorageService get instance => _instance;

  late SharedPreferences _prefs;
  bool _initialized = false;

  // 存储键名常量
  static const String _accessTokenKey = 'auth_access_token';
  static const String _refreshTokenKey = 'auth_refresh_token';
  static const String _tokenTypeKey = 'auth_token_type';
  static const String _expiresInKey = 'auth_expires_in';
  static const String _tokenTimestampKey = 'auth_token_timestamp';
  static const String _userDataKey = 'auth_user_data';
  static const String _systemAccessKey = 'auth_system_access';
  static const String _isLoggedInKey = 'auth_is_logged_in';
  static const String _encryptionKeyKey = 'auth_encryption_key';

  /// 初始化存储服务
  Future<void> init() async {
    if (_initialized) return;
    
    try {
      _prefs = await SharedPreferences.getInstance();
      _initialized = true;
      
      LogService.instance.info('AuthStorageService', '认证存储服务初始化成功');
    } catch (e) {
      LogService.instance.error('AuthStorageService', '认证存储服务初始化失败', e);
      rethrow;
    }
  }

  /// 确保已初始化
  void _ensureInitialized() {
    if (!_initialized) {
      throw StateError('AuthStorageService not initialized. Call init() first.');
    }
  }

  /// 生成简单的加密密钥
  String _generateEncryptionKey() {
    const String chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final Random random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(32, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// 获取或创建加密密钥
  String _getEncryptionKey() {
    String? key = _prefs.getString(_encryptionKeyKey);
    if (key == null) {
      key = _generateEncryptionKey();
      _prefs.setString(_encryptionKeyKey, key);
    }
    return key;
  }

  /// 简单的字符串加密（XOR）
  String _encrypt(String data) {
    final String key = _getEncryptionKey();
    final List<int> dataBytes = utf8.encode(data);
    final List<int> keyBytes = utf8.encode(key);
    final List<int> encrypted = [];

    for (int i = 0; i < dataBytes.length; i++) {
      encrypted.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return base64Encode(encrypted);
  }

  /// 简单的字符串解密（XOR）
  String _decrypt(String encryptedData) {
    try {
      final String key = _getEncryptionKey();
      final List<int> encryptedBytes = base64Decode(encryptedData);
      final List<int> keyBytes = utf8.encode(key);
      final List<int> decrypted = [];

      for (int i = 0; i < encryptedBytes.length; i++) {
        decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }

      return utf8.decode(decrypted);
    } catch (e) {
      LogService.instance.error('AuthStorageService', '解密数据失败', e);
      return '';
    }
  }

  /// 保存登录响应数据
  Future<void> saveLoginData(LoginResponseData loginData) async {
    _ensureInitialized();

    try {
      LogService.instance.info('AuthStorageService', '开始保存登录数据');

      // 保存令牌信息（加密存储）
      await _prefs.setString(_accessTokenKey, _encrypt(loginData.accessToken));
      await _prefs.setString(_refreshTokenKey, _encrypt(loginData.refreshToken));
      await _prefs.setString(_tokenTypeKey, loginData.tokenType);
      await _prefs.setInt(_expiresInKey, loginData.expiresIn);
      await _prefs.setInt(_tokenTimestampKey, DateTime.now().millisecondsSinceEpoch);

      // 保存用户信息（加密存储）
      final String userJson = jsonEncode(loginData.user.toJson());
      await _prefs.setString(_userDataKey, _encrypt(userJson));

      // 保存系统访问权限（加密存储）
      final String systemAccessJson = jsonEncode(loginData.systemAccess.toJson());
      await _prefs.setString(_systemAccessKey, _encrypt(systemAccessJson));

      // 设置登录状态
      await _prefs.setBool(_isLoggedInKey, true);

      LogService.instance.info('AuthStorageService', '登录数据保存成功 - userId: ${loginData.user.id}, username: ${loginData.user.username}, system: ${loginData.systemAccess.system}');
    } catch (e) {
      LogService.instance.error('AuthStorageService', '保存登录数据失败', e);
      rethrow;
    }
  }

  /// 获取访问令牌
  Future<String?> getAccessToken() async {
    _ensureInitialized();

    try {
      final String? encryptedToken = _prefs.getString(_accessTokenKey);
      if (encryptedToken == null) return null;

      final String token = _decrypt(encryptedToken);
      return token.isEmpty ? null : token;
    } catch (e) {
      LogService.instance.error('AuthStorageService', '获取访问令牌失败', e);
      return null;
    }
  }

  /// 获取刷新令牌
  Future<String?> getRefreshToken() async {
    _ensureInitialized();

    try {
      final String? encryptedToken = _prefs.getString(_refreshTokenKey);
      if (encryptedToken == null) return null;

      final String token = _decrypt(encryptedToken);
      return token.isEmpty ? null : token;
    } catch (e) {
      LogService.instance.error('AuthStorageService', '获取刷新令牌失败', e);
      return null;
    }
  }

  /// 检查令牌是否过期
  Future<bool> isTokenExpired() async {
    _ensureInitialized();

    try {
      final int? timestamp = _prefs.getInt(_tokenTimestampKey);
      final int? expiresIn = _prefs.getInt(_expiresInKey);

      if (timestamp == null || expiresIn == null) return true;

      final DateTime tokenTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final DateTime expiryTime = tokenTime.add(Duration(seconds: expiresIn));
      final DateTime now = DateTime.now();

      // 提前5分钟认为令牌过期，给刷新留出时间
      final DateTime bufferTime = expiryTime.subtract(const Duration(minutes: 5));

      return now.isAfter(bufferTime);
    } catch (e) {
      LogService.instance.error('AuthStorageService', '检查令牌过期状态失败', e);
      return true;
    }
  }

  /// 获取用户信息
  Future<AuthUser?> getUserData() async {
    _ensureInitialized();

    try {
      final String? encryptedUserData = _prefs.getString(_userDataKey);
      if (encryptedUserData == null) return null;

      final String userJson = _decrypt(encryptedUserData);
      if (userJson.isEmpty) return null;

      final Map<String, dynamic> userData = jsonDecode(userJson) as Map<String, dynamic>;
      return AuthUser.fromJson(userData);
    } catch (e) {
      LogService.instance.error('AuthStorageService', '获取用户数据失败', e);
      return null;
    }
  }

  /// 获取系统访问权限
  Future<SystemAccess?> getSystemAccess() async {
    _ensureInitialized();

    try {
      final String? encryptedSystemAccess = _prefs.getString(_systemAccessKey);
      if (encryptedSystemAccess == null) return null;

      final String systemAccessJson = _decrypt(encryptedSystemAccess);
      if (systemAccessJson.isEmpty) return null;

      final Map<String, dynamic> systemAccessData = jsonDecode(systemAccessJson) as Map<String, dynamic>;
      return SystemAccess.fromJson(systemAccessData);
    } catch (e) {
      LogService.instance.error('AuthStorageService', '获取系统访问权限失败', e);
      return null;
    }
  }

  /// 检查是否已登录
  Future<bool> isLoggedIn() async {
    _ensureInitialized();

    try {
      final bool loggedIn = _prefs.getBool(_isLoggedInKey) ?? false;
      if (!loggedIn) return false;

      // 检查是否有有效的访问令牌
      final String? accessToken = await getAccessToken();
      if (accessToken == null) {
        await setLoggedIn(false);
        return false;
      }

      return true;
    } catch (e) {
      LogService.instance.error('AuthStorageService', '检查登录状态失败', e);
      return false;
    }
  }

  /// 设置登录状态
  Future<void> setLoggedIn(bool loggedIn) async {
    _ensureInitialized();
    await _prefs.setBool(_isLoggedInKey, loggedIn);
  }

  /// 更新访问令牌
  Future<void> updateAccessToken(String accessToken, int expiresIn) async {
    _ensureInitialized();

    try {
      await _prefs.setString(_accessTokenKey, _encrypt(accessToken));
      await _prefs.setInt(_expiresInKey, expiresIn);
      await _prefs.setInt(_tokenTimestampKey, DateTime.now().millisecondsSinceEpoch);

      LogService.instance.info('AuthStorageService', '访问令牌更新成功');
    } catch (e) {
      LogService.instance.error('AuthStorageService', '更新访问令牌失败', e);
      rethrow;
    }
  }

  /// 清除所有认证数据
  Future<void> clearAuthData() async {
    _ensureInitialized();

    try {
      LogService.instance.info('AuthStorageService', '开始清除认证数据');

      await Future.wait([
        _prefs.remove(_accessTokenKey),
        _prefs.remove(_refreshTokenKey),
        _prefs.remove(_tokenTypeKey),
        _prefs.remove(_expiresInKey),
        _prefs.remove(_tokenTimestampKey),
        _prefs.remove(_userDataKey),
        _prefs.remove(_systemAccessKey),
        _prefs.remove(_isLoggedInKey),
      ]);

      LogService.instance.info('AuthStorageService', '认证数据清除成功');
    } catch (e) {
      LogService.instance.error('AuthStorageService', '清除认证数据失败', e);
      rethrow;
    }
  }

  /// 获取令牌剩余有效时间（秒）
  Future<int> getTokenRemainingTime() async {
    _ensureInitialized();

    try {
      final int? timestamp = _prefs.getInt(_tokenTimestampKey);
      final int? expiresIn = _prefs.getInt(_expiresInKey);

      if (timestamp == null || expiresIn == null) return 0;

      final DateTime tokenTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final DateTime expiryTime = tokenTime.add(Duration(seconds: expiresIn));
      final DateTime now = DateTime.now();

      final int remainingSeconds = expiryTime.difference(now).inSeconds;
      return remainingSeconds > 0 ? remainingSeconds : 0;
    } catch (e) {
      LogService.instance.error('AuthStorageService', '获取令牌剩余时间失败', e);
      return 0;
    }
  }
}
