import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../widgets/safe_scaffold.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// 定义NFC卡信息的数据模型
class NFCCardInfo {
  NFCCardInfo({
    required this.cardType,
    required this.cardCategory,
    required this.uuid,
    required this.ats,
  });

  factory NFCCardInfo.empty() {
    return NFCCardInfo(
      cardType: '未知',
      cardCategory: '未知',
      uuid: '未知',
      ats: '未知',
    );
  }
  final String cardType;
  final String cardCategory;
  final String uuid;
  final String ats;
}

// 创建NFC卡控制器，管理状态和与原生代码的通信
class NFCCardController extends StateNotifier<AsyncValue<NFCCardInfo>> {
  NFCCardController() : super(const AsyncValue.loading()) {
    _channel = const MethodChannel('com.example.edc_app/card');
    debugPrint('NFCCardController initialized.');
  }

  late final MethodChannel _channel;
  bool _isCheckingCard = false;
  int _totalCount = 0;
  int _successCount = 0;
  int _failCount = 0;

  int get totalCount => _totalCount;
  int get successCount => _successCount;
  int get failCount => _failCount;

  // 开始检测NFC卡
  Future<void> startCheckCard() async {
    debugPrint('startCheckCard called. _isCheckingCard: $_isCheckingCard');
    if (_isCheckingCard) return;

    _isCheckingCard = true;
    try {
      state = const AsyncValue.loading();
      debugPrint('Setting up MethodCallHandler...');
      // 监听卡检测结果 - 这个 Handler 会一直保持，直到 stopCheckCard 被调用
      _channel.setMethodCallHandler((MethodCall call) async {
        debugPrint('Received method call from native: ${call.method}');
        if (call.method == 'onCardDetected') {
          final Map<String, dynamic> cardInfo =
              Map<String, dynamic>.from(call.arguments);
          _handleCardInfo(cardInfo);
        } else if (call.method == 'onCardError') {
          final Map<String, dynamic> errorInfo =
              Map<String, dynamic>.from(call.arguments);
          _handleCardError(errorInfo);
        }
        return null; // 表示已处理
      });

      debugPrint('Invoking native startCheckNFCCard...');
      await _channel.invokeMethod('startCheckNFCCard');
      debugPrint('Initial startCheckNFCCard invoked successfully.');
    } catch (e, s) {
      debugPrint('Error starting NFC card check: $e\n$s');
      state = AsyncValue.error(e, s);
      _isCheckingCard = false;
    }
  }

  // 停止检测NFC卡
  Future<void> stopCheckCard() async {
    debugPrint('stopCheckCard called. _isCheckingCard: $_isCheckingCard');
    if (!_isCheckingCard) return;

    _isCheckingCard = false; // 立即设置标志位，防止在异步调用期间重启
    try {
      debugPrint('Invoking native stopCheckNFCCard...');
      await _channel.invokeMethod('stopCheckNFCCard');
      _channel.setMethodCallHandler(null); // 清除 Handler
      debugPrint(
          'Native stopCheckNFCCard invoked successfully and handler removed.');
    } catch (e) {
      debugPrint('Error stopping NFC card check: $e');
      // 即使停止失败，也保持 _isCheckingCard = false
    }
  }

  // 处理卡信息
  void _handleCardInfo(Map<String, dynamic> cardInfo) {
    debugPrint('Handling card info: $cardInfo');
    _totalCount++;
    _successCount++;

    final NFCCardInfo nfcCardInfo = NFCCardInfo(
      cardType: cardInfo['cardType'] ?? '未知',
      cardCategory: cardInfo['cardCategory'] ?? '未知',
      uuid: cardInfo['uuid'] ?? '未知',
      ats: cardInfo['ats'] ?? '未知',
    );

    state = AsyncValue.data(nfcCardInfo); // 更新 UI 显示卡信息
    _restartCheckCard(); // 处理完后重新开始检测
  }

  // 处理卡错误
  void _handleCardError(Map<String, dynamic> errorInfo) {
    debugPrint('Handling card error: $errorInfo');
    _totalCount++;
    _failCount++;

    final message = errorInfo['message'] ?? '未知错误';
    final code = errorInfo['code'] ?? -1;

    state =
        AsyncValue.error('卡片检测失败：$message (错误码: $code)', StackTrace.current);
    _restartCheckCard(); // 出错后也重新开始检测
  }

  // 重新开始检测
  Future<void> _restartCheckCard() async {
    debugPrint('_restartCheckCard called. _isCheckingCard: $_isCheckingCard');
    // 加一个短暂延迟，让UI有机会显示结果
    await Future.delayed(const Duration(milliseconds: 1000));

    // 再次检查标志位，确保在延迟期间没有调用 stopCheckCard
    if (!_isCheckingCard) {
      debugPrint('Skipping restart because _isCheckingCard is false.');
      return;
    }

    try {
      state = const AsyncValue.loading(); // 将状态设置回加载中
      debugPrint('Invoking native startCheckNFCCard for restart...');
      await _channel.invokeMethod('startCheckNFCCard');
      debugPrint('Restarted check successfully.');
    } catch (e, s) {
      debugPrint('Error restarting NFC card check: $e\n$s');
      state = AsyncValue.error('无法重新开始检测: $e', s);
      _isCheckingCard = false; // 如果重启失败，则停止检测
    }
  }

  @override
  void dispose() {
    debugPrint('NFCCardController disposing...');
    stopCheckCard(); // 确保停止检测
    super.dispose();
  }
}

// 创建自动销毁的Provider
final AutoDisposeStateNotifierProvider<NFCCardController,
        AsyncValue<NFCCardInfo>> nfcCardControllerProvider =
    AutoDisposeStateNotifierProvider<NFCCardController,
        AsyncValue<NFCCardInfo>>((AutoDisposeStateNotifierProviderRef<
            NFCCardController, AsyncValue<NFCCardInfo>>
        ref) {
  return NFCCardController();
});

class NFCPage extends ConsumerStatefulWidget {
  const NFCPage({super.key});

  @override
  ConsumerState<NFCPage> createState() => _NFCPageState();
}

class _NFCPageState extends ConsumerState<NFCPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(nfcCardControllerProvider.notifier).startCheckCard();
    });
  }

  @override
  void dispose() {
    // 不再需要在这里调用 stopCheckCard，Notifier 的 dispose 方法会处理
    // ref.read(nfcCardControllerProvider.notifier).stopCheckCard();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final AsyncValue<NFCCardInfo> nfcCardState =
        ref.watch(nfcCardControllerProvider);
    final NFCCardController controller =
        ref.read(nfcCardControllerProvider.notifier);

    return SafeScaffold(
      appBar: AppBar(
        title: const Text('NFC卡操作'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            _buildCounterRow(controller),
            const SizedBox(height: 24),
            _buildNFCCardStatus(nfcCardState),
            const SizedBox(height: 24),
            _buildCardInfoSection(nfcCardState),
          ],
        ),
      ),
    );
  }

  Widget _buildCounterRow(NFCCardController controller) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: <Widget>[
        _buildCounterItem('总计', controller.totalCount, Colors.blue),
        _buildCounterItem('成功', controller.successCount, Colors.green),
        _buildCounterItem('失败', controller.failCount, Colors.red),
      ],
    );
  }

  Widget _buildCounterItem(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: <Widget>[
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            count.toString(),
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNFCCardStatus(AsyncValue<NFCCardInfo> state) {
    return state.when(
      data: (_) => Card(
        color: Colors.green.shade50,
        child: const Padding(
          padding: EdgeInsets.all(16.0),
          child: Row(
            children: <Widget>[
              Icon(Icons.check_circle, color: Colors.green),
              SizedBox(width: 8),
              Text(
                '已检测到NFC卡',
                style: TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
      loading: () => Card(
        color: Colors.blue.shade50,
        child: const Padding(
          padding: EdgeInsets.all(16.0),
          child: Row(
            children: <Widget>[
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              ),
              SizedBox(width: 8),
              Text(
                '请将NFC卡片靠近设备...',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
      error: (Object error, StackTrace stack) => Card(
        color: Colors.red.shade50,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: <Widget>[
              const Icon(Icons.error, color: Colors.red),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  error.toString(),
                  style: const TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardInfoSection(AsyncValue<NFCCardInfo> state) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: state.when(
          data: (NFCCardInfo cardInfo) => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const Text(
                '卡片信息',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Divider(),
              _buildCardInfoItem('卡类型', cardInfo.cardType),
              _buildCardInfoItem('卡类别', cardInfo.cardCategory),
              _buildCardInfoItem('UUID', cardInfo.uuid),
              _buildCardInfoItem('ATS', cardInfo.ats),
            ],
          ),
          loading: () => const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('等待卡片信息...'),
            ),
          ),
          error: (_, __) => const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('无法获取卡片信息'),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCardInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey[700],
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
