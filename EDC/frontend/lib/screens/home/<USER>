import 'package:edc_app/models/member_model.dart';
import 'package:edc_app/services/api/employee_api.dart';
import 'package:edc_app/services/fcc_device_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

import '../../models/station_model.dart';
import '../../services/api/api_service.dart';

import '../../constants/bp_colors.dart';
import '../../theme/app_theme.dart';
import '../../models/dispenser_model.dart';
import '../../models/fuel_transaction.dart';
import '../../controllers/dispenser_controller.dart';
import '../../controllers/fcc_status_controller.dart';
import '../../widgets/safe_scaffold.dart';
import '../../widgets/dispenser_tab_selector.dart';
import '../../widgets/app_loading_indicator.dart';
import '../../widgets/compact_fcc_status_bar.dart';
import '../../services/bos_transaction_polling_service.dart';
import '../../services/transaction_state_sync_service.dart';
import '../../services/transaction_navigation_service.dart';
import '../../services/new_auth_service.dart';
import '../../models/auth_models.dart';
import '../../widgets/member_info_bottom_bar.dart';
import '../../widgets/shift_status_indicator.dart';
import '../../services/member_cache_service.dart';
import '../../services/shift_service.dart';
import '../../utils/fuel_transaction_sync_debugger.dart';
import '../../widgets/app_loading_indicator.dart'; // 导入新的加载组件

/// Fuel Control Home Page (Simplified Version)
/// Uses new service modules for better code organization
class TransactionPresetHomePageV3 extends ConsumerStatefulWidget {
  const TransactionPresetHomePageV3({super.key});

  @override
  ConsumerState<TransactionPresetHomePageV3> createState() =>
      _TransactionPresetHomePageV3State();
}

class _TransactionPresetHomePageV3State
    extends ConsumerState<TransactionPresetHomePageV3>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  BosTransactionPollingService? _bosPollingService;
  TransactionStateSyncService? _syncService;
  FuelTransactionSyncDebugger? _debugger;

  // 服务实例
  late final NewAuthService _authService;
  late final ApiService _apiService;

  // Station and user info
  String _stationName = 'BP Station Jakarta';
  String _employeeName = '';
  String _employeeNo = '';

  @override
  void initState() {
    super.initState();
    
    // 初始化服务实例
    _authService = ref.read(newAuthServiceProvider);
    // 确保ApiService已经被正确初始化
    _apiService = ApiService();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeServices();
      _loadStationAndUserInfo();
      _debugger = FuelTransactionSyncDebugger(ref);
    });
  }

  /// 加载站点和用户信息
  Future<void> _loadStationAndUserInfo() async {
    try {
      // 从新认证服务获取用户信息
      final AuthUser? currentUser = _authService.currentUser;

      if (currentUser != null) {
        setState(() {
          _employeeName = currentUser.fullName;
          _employeeNo = currentUser.username;
        });
        debugPrint('✅ 用户信息已加载: $_employeeName ($_employeeNo)');
      } else {
        // 如果新认证服务没有用户信息，从SharedPreferences获取
        final SharedPreferences prefs = await SharedPreferences.getInstance();
        final String employeeName = prefs.getString('employee_name') ?? '';
        final String employeeNo = prefs.getString('employee_no') ?? '';

        setState(() {
          _employeeName = employeeName;
          _employeeNo = employeeNo;
        });
        debugPrint('✅ 用户信息已从缓存加载: $_employeeName ($_employeeNo)');
      }

      // 获取站点信息 - 使用用户登录时返回的stationIds
      await _loadStationInfo();

      debugPrint('✅ 站点信息已加载: $_stationName');
    } catch (e) {
      debugPrint('❌ 加载用户/站点信息失败: $e');
    }
  }

  /// 加载站点信息
  Future<void> _loadStationInfo() async {
    try {
      debugPrint('🔍 开始加载站点信息...');
      
      // 获取当前用户的站点ID列表
      final List<int> stationIds = _authService.accessibleStationIds;
      debugPrint('🔍 获取到的stationIds: $stationIds');
      
      if (stationIds.isEmpty) {
        debugPrint('⚠️ 用户没有关联的站点，使用默认站点名称');
        setState(() {
          _stationName = 'BP Station Jakarta';
        });
        debugPrint('✅ 站点信息已加载: $_stationName (默认)');
        return;
      }
      
      // 使用第一个站点ID获取站点信息
      final int firstStationId = stationIds.first;
      debugPrint('🔍 使用第一个站点ID: $firstStationId');
      
      final Station? station = await _apiService.stationApi.getStationById(firstStationId);
      
      if (station != null) {
        setState(() {
          _stationName = station.siteName;
        });
        debugPrint('✅ 站点信息已加载: ${station.siteName}');
      } else {
        debugPrint('⚠️ 站点不存在，使用默认名称');
        setState(() {
          _stationName = 'BP Station $firstStationId';
        });
        debugPrint('✅ 站点信息已加载: $_stationName (默认ID)');
      }
      
    } catch (e) {
      debugPrint('❌ 加载站点信息失败: $e');
      setState(() {
        _stationName = 'BP Station Jakarta';
      });
      debugPrint('✅ 站点信息已加载: $_stationName (异常默认)');
    }
  }



  /// 初始化所有服务
  void _initializeServices() {
    try {
      // 初始化原有的设备控制器
      ref.read(dispenserControllerProvider.notifier).initialize();

      // 初始化班次服务
      _initializeShiftService();

      // 初始化FCC状态轮询
      _initializeFccPolling();

      // 初始化BOS交易轮询和状态同步
      _initializeBosServices();
    } catch (e) {
      debugPrint('❌ 服务初始化失败: $e');
    }
  }

  /// 初始化班次服务
  void _initializeShiftService() {
    try {
      final ShiftService shiftService = ShiftService();
      // 初始化班次服务，如果还未初始化
      shiftService.initialize();
      debugPrint('✅ 班次服务已初始化');
    } catch (e) {
      debugPrint('❌ 班次服务初始化失败: $e');
    }
  }

  /// 初始化FCC轮询
  void _initializeFccPolling() {
    try {
      ref.read(fccStatusControllerProvider.notifier).startPolling(
            interval: const Duration(seconds: 1), // 调整：FCC轮询间隔为1秒，实时响应
          );
      debugPrint('✅ FCC轮询已启动 (间隔: 1秒)');
    } catch (e) {
      debugPrint('❌ FCC轮询启动失败: $e');
    }
  }

  /// 初始化BOS服务
  void _initializeBosServices() {
    try {
      _bosPollingService = ref.read(bosTransactionPollingServiceProvider);

      // 使用共享的FCC设备服务实例
      debugPrint('🔍 尝试获取 fccDeviceServiceProvider...');
      try {
        final FCCDeviceService fccDeviceService =
            ref.read(fccDeviceServiceProvider);
        debugPrint('✅ 成功获取到FCCDeviceService: ${fccDeviceService.hashCode}');
        _syncService = TransactionStateSyncService(
          ref,
          fccDeviceService: fccDeviceService,
          pollingService: ref.read(fccPollingServiceProvider),
        );
        debugPrint('✅ TransactionStateSyncService 创建成功');
      } catch (e) {
        debugPrint('❌ 获取 fccDeviceServiceProvider 失败: $e');
        rethrow;
      }

      // 设置回调
      _bosPollingService!.onTransactionsFound =
          (List<FuelTransaction> transactions) {
        debugPrint('📋 找到 ${transactions.length} 条新交易，开始同步状态');
        _syncService!.syncTransactionStates(transactions);
      };

      _bosPollingService!.onTransactionsDisappeared = 
          (List<String> disappearedTransactionNumbers) {
        debugPrint('🔍 检测到 ${disappearedTransactionNumbers.length} 条交易消失，开始解锁状态');
        _syncService!.handleDisappearedTransactions(disappearedTransactionNumbers);
      };

      _bosPollingService!.onShiftStatusChanged = (ShiftInfo? shift) {
        debugPrint('📊 班次状态变化通知: ${shift?.shiftId ?? '无'} (${shift?.status.name ?? '无'})');
        // 班次状态变化会自动通过ShiftService的流传播到UI组件
        // 这里不需要额外操作，因为ShiftService内部已经处理了状态更新和通知
      };

      _bosPollingService!.onError = (String error) {
        debugPrint('⚠️ BOS轮询错误: $error');
      };

      // 启动轮询
      _bosPollingService!.startPolling();

      debugPrint('✅ BOS交易轮询已启动');
    } catch (e) {
      debugPrint('❌ BOS服务启动失败: $e');
    }
  }

  @override
  void dispose() {
    try {
      ref.read(fccStatusControllerProvider.notifier).stopPolling();
      debugPrint('⏹️ FCC轮询已停止');
    } catch (e) {
      debugPrint('❌ 停止FCC轮询失败: $e');
    }

    _bosPollingService?.stopPolling();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final DispenserState state = ref.watch(dispenserControllerProvider);

    return SafeScaffold(
      body: _buildFullHeightBody(state),
      bottomNavigationBar: MemberInfoBottomBar(
        onMemberEntry: _handleMemberEntry,
        onMemberRemove: _handleMemberRemove,
        onMemberTap: _handleMemberTap,
      ),
    );
  }

  Widget _buildFullHeightBody(DispenserState state) {
    if (state.isLoading && state.dispensers.isEmpty) {
      return _buildLoadingState();
    }

    if (state.errorMessage != null) {
      return _buildErrorState(state.errorMessage!);
    }

    if (state.dispensers.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: <Widget>[
        // 顶部站点和用户信息 - 不受 FCC 连接状态影响
        _buildHeaderSection(),

        // FCC状态显示组件 - 支持缓存数据显示
        // 当 FCC 连接错误时，不会影响整体布局，只显示缓存数据警告
        // Container(
        //   color: BPColors.primary.withOpacity(0.05),
        //   child: CompactFccStatusBar(
        //     showPollingControls: true,
        //     onTap: () => _showFccStatusDetail(context),
        //   ),
        // ),

        // Dispenser selector - 继续正常显示
        DispenserTabSelector(
          onDispenserSelected: _handleDispenserSelected,
        ),

        // Pump groups list - 主要内容区域
        // 即使 FCC 断连，也会显示缓存的设备数据
        Expanded(
          child: _buildCompactPumpGroupsList(state),
        ),
      ],
    );
  }

  /// 构建顶部标题栏
  Widget _buildHeaderSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: BPColors.primary,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            offset: const Offset(0, 1),
            blurRadius: 2,
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            // 第一行：站点名称
            Row(
              children: <Widget>[
                const Icon(
                  Icons.location_on,
                  color: Colors.white,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: GestureDetector(
                    onLongPress: _showDebugMenu,
                    child: Text(
                      _stationName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 4),

            // 第二行：员工信息和班次状态
            Row(
              children: <Widget>[
                Icon(
                  Icons.person,
                  color: Colors.white.withOpacity(0.9),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _employeeName.isNotEmpty ? _employeeName : 'No Staff Login',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // 新的响应式班次状态指示器
                const ShiftStatusIndicator(
                  compact: true,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建简单时间显示
  Widget _buildSimpleTime() {
    return StreamBuilder<DateTime>(
      stream:
          Stream.periodic(const Duration(seconds: 1), (_) => DateTime.now()),
      builder: (BuildContext context, AsyncSnapshot<DateTime> snapshot) {
        final DateTime now = snapshot.data ?? DateTime.now();
        final String timeString =
            '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

        return Text(
          timeString,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        );
      },
    );
  }

  Widget _buildCompactPumpGroupsList(DispenserState state) {
    if (state.selectedDispenser == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.local_gas_station_outlined,
              size: 64,
              color: BPColors.neutral.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            const Text(
              'Select a Dispenser',
              style: EDCTextStyles.subTitle,
            ),
          ],
        ),
      );
    }

    final List<PumpGroup> pumpGroups = state.currentPumpGroups;
    if (pumpGroups.isEmpty) {
      return const Center(
        child: Text(
          'No pump groups available',
          style: EDCTextStyles.bodyText,
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _handleRefresh,
      color: BPColors.primary,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 8),
        itemCount: pumpGroups.length,
        itemBuilder: (BuildContext context, int index) {
          final PumpGroup pumpGroup = pumpGroups[index];
          return _CompactPumpGroupCard(
            pumpGroup: pumpGroup,
            onNozzleTap: _handleNozzleTap,
            state: state,
          );
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          AppLoadingIndicator(),
          SizedBox(height: 24),
          Text(
            'Loading dispensers...',
            style: EDCTextStyles.hintText,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String errorMessage) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(
              Icons.error_outline,
              size: 64,
              color: BPColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Connection Error',
              style: EDCTextStyles.subTitle.copyWith(
                color: BPColors.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              style: EDCTextStyles.bodyText,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _handleRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: BPColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.local_gas_station_outlined,
            size: 64,
            color: BPColors.neutral.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'No Dispensers Available',
            style: EDCTextStyles.subTitle,
          ),
          const SizedBox(height: 8),
          const Text(
            'Please check connection',
            style: EDCTextStyles.hintText,
          ),
        ],
      ),
    );
  }

  // Event handlers
  void _handleDispenserSelected(String dispenserId) { // 优化：改为 String 类型
    // Handled by DispenserController
  }

  /// 处理客户信息录入按钮点击
  void _handleMemberEntry() {
    debugPrint('🎯 处理客户信息录入请求');
    memberCacheService.clearCache();
    context.push('/member/register');
  }

  /// 处理客户信息缓存删除
  void _handleMemberRemove() {
    debugPrint('🗑️ 客户信息缓存已删除');
    // 额外的清理逻辑可以在这里添加
  }

  /// 处理客户信息点击（查看详情）
  void _handleMemberTap() {
    final Member? cachedMember = memberCacheService.cachedMember;
    if (cachedMember != null) {
      debugPrint(
          '🎯 TransactionPresetHomePage: 点击会员信息区域，跳转到 member_registration_page');
      context.push('/member/register');
    }
  }

  /// 处理nozzle点击事件 - 使用导航服务
  Future<void> _handleNozzleTap(Nozzle nozzle) async {
    // 检查班次状态 - 在所有操作前进行检查
    final ShiftService shiftService = ShiftService();
    if (!shiftService.hasActiveShift && nozzle.status == NozzleStatus.idle) {
      _showErrorMessage(
          'Cannot authorize nozzle: No active shift. Please start a shift first.');
      debugPrint('❌ 操作被拒绝: 没有活跃班次');
      return;
    }

    final TransactionNavigationService navigationService =
        TransactionNavigationService(
      context: context,
      ref: ref,
    );

    await navigationService.handleNozzleTap(nozzle);
  }

  Future<void> _handleRefresh() async {
    await ref.read(dispenserControllerProvider.notifier).refreshNozzleStatus();
  }

  Future<void> _handleRetry() async {
    ref.read(dispenserControllerProvider.notifier).clearError();
    await ref.read(dispenserControllerProvider.notifier).initialize();
  }

  /// 显示FCC状态详情对话框
  void _showFccStatusDetail(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => const FccStatusDetailDialog(),
    );
  }

  /// 显示错误消息
  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: BPColors.error,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

}

/// Compact pump group card component
class _CompactPumpGroupCard extends StatelessWidget {
  const _CompactPumpGroupCard({
    required this.pumpGroup,
    required this.onNozzleTap,
    required this.state,
  });
  final PumpGroup pumpGroup;
  final Function(Nozzle nozzle) onNozzleTap;
  final DispenserState state;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      decoration: BoxDecoration(
        color: BPColors.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // Header
          Padding(
            padding: const EdgeInsets.fromLTRB(8, 6, 8, 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    Container(
                      width: 3,
                      height: 16,
                      decoration: BoxDecoration(
                        color: BPColors.primary,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      pumpGroup.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        color: BPColors.primary,
                      ),
                    ),
                  ],
                ),
                // 注释掉pump状态显示，但保留nozzle状态显示
                // _buildStatusBadge(),
              ],
            ),
          ),

          // Nozzle grid
          Padding(
            padding: const EdgeInsets.fromLTRB(6, 2, 6, 6),
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                mainAxisExtent: 125,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: pumpGroup.nozzles.length,
              itemBuilder: (BuildContext context, int index) {
                final Nozzle nozzle = pumpGroup.nozzles[index];
                final Nozzle latestNozzle =
                    state.getNozzle(nozzle.id) ?? nozzle;

                return _CompactNozzleCard(
                  nozzle: latestNozzle,
                  onTap: () => onNozzleTap(latestNozzle),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge() {
    final int activeCount = pumpGroup.activeTransactionCount;
    final int offlineCount = pumpGroup.offlineNozzleCount;

    Color color;
    String text;

    if (offlineCount > 0) {
      color = BPColors.error;
      text = '$offlineCount Offline';
    } else if (activeCount > 0) {
      color = BPColors.warning;
      text = '$activeCount Active';
    } else {
      color = BPColors.success;
      text = 'Ready';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.08),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Container(
            width: 5,
            height: 5,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 5),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

/// Enhanced nozzle card component with complete information
class _CompactNozzleCard extends StatelessWidget {
  const _CompactNozzleCard({
    required this.nozzle,
    this.onTap,
  });
  final Nozzle nozzle;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final Color statusColor = _getStatusColor(nozzle.status);
    final bool canInteract = _canInteract(nozzle.status);

    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8),
      elevation: 0,
      child: InkWell(
        onTap: canInteract ? onTap : null,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: statusColor,
              width: 1.5,
            ),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: statusColor.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              _buildHeader(statusColor),
              _buildBody(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(Color statusColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(6),
          topRight: Radius.circular(6),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: <Widget>[
              Text(
                '#',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 9,
                  color: statusColor.withOpacity(0.7),
                  height: 1.0,
                ),
              ),
              Text(
                nozzle.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12, // 减小字体以适应更长的名称
                  color: statusColor,
                  height: 1.0,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ],
          ),
          Text(
            _getStatusText(nozzle.status, nozzle.preauthInfo),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: statusColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    final bool isFuelling = nozzle.status == NozzleStatus.fuelling;
    final bool isPay = nozzle.status == NozzleStatus.complete;
    final bool hasActiveTransaction =
        nozzle.currentVolume > 0 || nozzle.currentAmount > 0;

    // 直接用时间判断是否有有效的预授权
    final bool hasValidPreauth = nozzle.preauthInfo != null &&
        !nozzle.preauthInfo!.isExpired;

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(6, 8, 6, 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // 根据状态显示不同内容
            if (isFuelling)
              // 加注中状态：显示升数和金额
              Expanded(
                child: _buildFuellingData(),
              )
            else if (isPay && hasActiveTransaction)
              // Pay状态：显示最终交易数据
              Expanded(
                child: _buildPayData(),
              )
            else if (hasValidPreauth)
              // 有有效预授权信息：显示预设值（不依赖status）
              Expanded(
                child: _buildAuthData(),
              )
            else ...<Widget>[
              // 其他状态：显示油品名称
              SizedBox(
                height: 36,
                child: Text(
                  nozzle.fuelType,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                    height: 1.3,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              const Spacer(),

              // 底部显示价格或交易数据
              if (hasActiveTransaction) _buildTransactionData()
              // else
              //   _buildPriceInfo(), // 不显示单价
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionData() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        if (nozzle.currentAmount > 0)
          Text.rich(
            TextSpan(
              children: <InlineSpan>[
                TextSpan(
                  text: 'IDR ',
                  style: TextStyle(
                    fontSize: 9,
                    fontWeight: FontWeight.w500,
                    color: BPColors.primary.withValues(alpha: 0.7),
                  ),
                ),
                TextSpan(
                  text: _formatPrice(nozzle.currentAmount),
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w700,
                    color: BPColors.primary,
                  ),
                ),
              ],
            ),
          ),
        if (nozzle.currentVolume > 0)
          Text(
            '${nozzle.currentVolume.toStringAsFixed(3)} L',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
      ],
    );
  }



  Widget _buildFuellingData() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 金额显示 - 主要信息
        if (nozzle.currentAmount > 0)
          Text.rich(
            TextSpan(
              children: <InlineSpan>[
                TextSpan(
                  text: 'IDR ',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: BPColors.primary.withValues(alpha: 0.7),
                    height: 1.2,
                  ),
                ),
                TextSpan(
                  text: _formatPrice(nozzle.currentAmount),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: BPColors.primary,
                    height: 1.2,
                  ),
                ),
              ],
            ),
          ),

        const SizedBox(height: 4),

        // 升数显示 - 辅助信息
        if (nozzle.currentVolume > 0)
          Text(
            '${nozzle.currentVolume.toStringAsFixed(3)} L',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
              height: 1.2,
            ),
          ),

        // 如果没有数据，显示加注中提示
        if (nozzle.currentAmount <= 0 && nozzle.currentVolume <= 0)
          const Text(
            'Fuelling...',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2196F3),
              height: 1.2,
            ),
          ),
      ],
    );
  }

  Widget _buildPayData() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 金额显示 - 突出显示最终金额
        if (nozzle.currentAmount > 0)
          Text.rich(
            TextSpan(
              children: <InlineSpan>[
                TextSpan(
                  text: 'IDR ',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: BPColors.primary.withValues(alpha: 0.7),
                    height: 1.2,
                  ),
                ),
                TextSpan(
                  text: _formatPrice(nozzle.currentAmount),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: BPColors.primary,
                    height: 1.2,
                  ),
                ),
              ],
            ),
          ),

        const SizedBox(height: 4),

        // 升数显示 - 最终升数
        if (nozzle.currentVolume > 0)
          Text(
            '${nozzle.currentVolume.toStringAsFixed(3)} L',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
              height: 1.2,
            ),
          ),

        const SizedBox(height: 2),

        // Pay 状态提示
        Text(
          'Tap to Pay',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: BPColors.primary.withValues(alpha: 0.8),
            height: 1.2,
          ),
        ),
      ],
    );
  }

  Widget _buildAuthData() {
    final PreauthInfo? preauth = nozzle.preauthInfo;
    if (preauth == null || preauth.isExpired) {
      return const Text(
        'Ready',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Color(0xFF757575),
          height: 1.2,
        ),
      );
    }

    final int remainingSeconds = preauth.expiresAt.difference(DateTime.now()).inSeconds;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 显示预设值
        Text(
          preauth.displayValue,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Color(0xFFFF9800), // 橙色表示预授权状态
            height: 1.2,
          ),
        ),

        const SizedBox(height: 4),

        // 显示预授权状态提示
        Text(
          'Pre-authorized',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: const Color(0xFFFF9800).withValues(alpha: 0.8),
            height: 1.2,
          ),
        ),

        // 显示剩余时间（如果少于30秒则显示倒计时）
        if (remainingSeconds > 0 && remainingSeconds < 30) ...<Widget>[
          const SizedBox(height: 2),
          Text(
            'Expires in ${remainingSeconds}s',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: remainingSeconds < 10
                  ? BPColors.error.withValues(alpha: 0.8)
                  : const Color(0xFFFF9800).withValues(alpha: 0.6),
              height: 1.2,
            ),
          ),
        ],
      ],
    );
  }

  String _formatPrice(double price) {
    return price.toStringAsFixed(0).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
  }

  Color _getStatusColor(NozzleStatus status) {
    switch (status) {
      case NozzleStatus.idle:
      case NozzleStatus.complete:

        return const Color(0xFF757575); // 灰色
      case NozzleStatus.auth:
        return BPColors.warning; // 橙色
      case NozzleStatus.fuelling:
        return const Color(0xFF2196F3); // 蓝色
      case NozzleStatus.pay:
        return BPColors.primary; // BP绿色
      case NozzleStatus.offline:
        return BPColors.error; // 红色
    }
  }

  String _getStatusText(NozzleStatus status, [PreauthInfo? preauthInfo]) {
    // 优先检查预授权时间有效性，不依赖status
    if (preauthInfo != null && !preauthInfo.isExpired) {
      switch (preauthInfo.type) {
        case 'preset_amount':
          return 'PRESET \$';
        case 'preset_volume':
          return 'PRESET L';
        case 'full_tank':
          return 'FULL TANK';
        default:
          return 'PRESET';
      }
    }

    // 如果没有有效预授权，使用常规status显示
    switch (status) {
      case NozzleStatus.idle:
        return 'READY';
      case NozzleStatus.auth:
        return 'AUTH';
      case NozzleStatus.fuelling:
        return 'ACTIVE';
      case NozzleStatus.complete:
        return 'READY';
      case NozzleStatus.pay:
        return 'PAY';
      case NozzleStatus.offline:
        return 'OFFLINE';
    }
  }

  bool _canInteract(NozzleStatus status) {
    // 所有状态都可以交互，包括离线状态（显示错误信息）
    return true;
  }

  /// 显示调试菜单（长按站点名称触发）
  void _showDebugMenu() {
    if (!mounted) return;

    showDialog<void>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('🔧 调试工具'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            ListTile(
              leading: const Icon(Icons.bug_report),
              title: const Text('完整诊断'),
              subtitle: const Text('运行完整的同步诊断'),
              onTap: () {
                Navigator.of(context).pop();
                _runFullDiagnosis();
              },
            ),
            ListTile(
              leading: const Icon(Icons.speed),
              title: const Text('快速检查'),
              subtitle: const Text('快速状态检查'),
              onTap: () {
                Navigator.of(context).pop();
                _runQuickCheck();
              },
            ),
            ListTile(
              leading: const Icon(Icons.refresh),
              title: const Text('手动轮询'),
              subtitle: const Text('手动触发BOS轮询'),
              onTap: () {
                Navigator.of(context).pop();
                _triggerManualPolling();
              },
            ),
          ],
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 运行完整诊断
  Future<void> _runFullDiagnosis() async {
    debugPrint('🔧 用户触发完整诊断');
    if (_debugger != null) {
      await _debugger!.runFullDiagnosis();
    } else {
      debugPrint('❌ 调试器未初始化');
    }
  }

  /// 运行快速检查
  void _runQuickCheck() {
    debugPrint('🔧 用户触发快速检查');
    if (_debugger != null) {
      _debugger!.quickStatusCheck();
    } else {
      debugPrint('❌ 调试器未初始化');
    }
  }

  /// 手动触发轮询
  Future<void> _triggerManualPolling() async {
    debugPrint('🔧 用户触发手动轮询');
    if (_bosPollingService != null) {
      await _bosPollingService!.pollOnce();
      debugPrint('✅ 手动轮询完成');
    } else {
      debugPrint('❌ BOS轮询服务未初始化');
    }
  }


}
