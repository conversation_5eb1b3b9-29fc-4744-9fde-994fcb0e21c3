// EDC/frontend/test/customer_type_test.dart

import 'package:flutter_test/flutter_test.dart';
import '../lib/constants/customer_type_constants.dart';

void main() {
  group('CustomerType Tests', () {
    test('CustomerType enum values', () {
      expect(CustomerType.b2b.value, 'B2B');
      expect(CustomerType.b2c.value, 'B2C');
    });

    test('CustomerType.fromString', () {
      expect(CustomerType.fromString('B2B'), CustomerType.b2b);
      expect(CustomerType.fromString('b2b'), CustomerType.b2b);
      expect(CustomerType.fromString('B2C'), CustomerType.b2c);
      expect(CustomerType.fromString('b2c'), CustomerType.b2c);
      expect(CustomerType.fromString('invalid'), null);
      expect(CustomerType.fromString(null), null);
      expect(CustomerType.fromString(''), null);
    });

    test('CustomerType.isB2B', () {
      expect(CustomerType.isB2B('B2B'), true);
      expect(CustomerType.isB2B('b2b'), true);
      expect(CustomerType.isB2B('B2C'), false);
      expect(CustomerType.isB2B('b2c'), false);
      expect(CustomerType.isB2B('invalid'), false);
      expect(CustomerType.isB2B(null), false);
    });

    test('CustomerType.isB2C', () {
      expect(CustomerType.isB2C('B2C'), true);
      expect(CustomerType.isB2C('b2c'), true);
      expect(CustomerType.isB2C('B2B'), false);
      expect(CustomerType.isB2C('b2b'), false);
      expect(CustomerType.isB2C('invalid'), false);
      expect(CustomerType.isB2C(null), false);
    });
  });

  group('CustomerTypeUtils Tests', () {
    test('detectCustomerType with valid customerType field', () {
      final Map<String, dynamic> metadata = {'customerType': 'B2B'};
      expect(CustomerTypeUtils.detectCustomerType(metadata), CustomerType.b2b);

      metadata['customerType'] = 'B2C';
      expect(CustomerTypeUtils.detectCustomerType(metadata), CustomerType.b2c);
    });

    test('detectCustomerType with invalid customerType field', () {
      final Map<String, dynamic> metadata = {'customerType': 'invalid'};
      expect(CustomerTypeUtils.detectCustomerType(metadata), CustomerType.b2c);
    });

    test('detectCustomerType with missing customerType field', () {
      final Map<String, dynamic> metadata = {'other': 'value'};
      expect(CustomerTypeUtils.detectCustomerType(metadata), CustomerType.b2c);
    });

    test('detectCustomerType with empty metadata', () {
      final Map<String, dynamic> metadata = {};
      expect(CustomerTypeUtils.detectCustomerType(metadata), CustomerType.b2c);
    });

    test('isB2BCustomer', () {
      final Map<String, dynamic> b2bMetadata = {'customerType': 'B2B'};
      expect(CustomerTypeUtils.isB2BCustomer(b2bMetadata), true);

      final Map<String, dynamic> b2cMetadata = {'customerType': 'B2C'};
      expect(CustomerTypeUtils.isB2BCustomer(b2cMetadata), false);

      final Map<String, dynamic> emptyMetadata = {};
      expect(CustomerTypeUtils.isB2BCustomer(emptyMetadata), false);
    });

    test('isB2CCustomer', () {
      final Map<String, dynamic> b2cMetadata = {'customerType': 'B2C'};
      expect(CustomerTypeUtils.isB2CCustomer(b2cMetadata), true);

      final Map<String, dynamic> b2bMetadata = {'customerType': 'B2B'};
      expect(CustomerTypeUtils.isB2CCustomer(b2bMetadata), false);

      final Map<String, dynamic> emptyMetadata = {};
      expect(CustomerTypeUtils.isB2CCustomer(emptyMetadata), true); // 默认为B2C
    });

    test('setCustomerType', () {
      final Map<String, dynamic> metadata = {};
      
      CustomerTypeUtils.setCustomerType(metadata, CustomerType.b2b);
      expect(metadata['customerType'], 'B2B');

      CustomerTypeUtils.setCustomerType(metadata, CustomerType.b2c);
      expect(metadata['customerType'], 'B2C');
    });

    test('getDisplayText', () {
      expect(CustomerTypeUtils.getDisplayText(CustomerType.b2b), 'Business Customer');
      expect(CustomerTypeUtils.getDisplayText(CustomerType.b2c), 'Individual Customer');
    });

    test('getAllValues', () {
      final List<String> values = CustomerTypeUtils.getAllValues();
      expect(values, ['B2B', 'B2C']);
    });
  });

  group('Integration Tests', () {
    test('Complete workflow: set and detect customer type', () {
      // 模拟B2B客户注册流程
      final Map<String, dynamic> b2bMetadata = {};
      CustomerTypeUtils.setCustomerType(b2bMetadata, CustomerType.b2b);
      
      // 验证设置成功
      expect(b2bMetadata['customerType'], 'B2B');
      
      // 验证检测成功
      final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(b2bMetadata);
      expect(detectedType, CustomerType.b2b);
      expect(CustomerTypeUtils.isB2BCustomer(b2bMetadata), true);
      expect(CustomerTypeUtils.isB2CCustomer(b2bMetadata), false);
    });

    test('Complete workflow: B2C customer', () {
      // 模拟B2C客户注册流程
      final Map<String, dynamic> b2cMetadata = {};
      CustomerTypeUtils.setCustomerType(b2cMetadata, CustomerType.b2c);
      
      // 验证设置成功
      expect(b2cMetadata['customerType'], 'B2C');
      
      // 验证检测成功
      final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(b2cMetadata);
      expect(detectedType, CustomerType.b2c);
      expect(CustomerTypeUtils.isB2BCustomer(b2cMetadata), false);
      expect(CustomerTypeUtils.isB2CCustomer(b2cMetadata), true);
    });
  });
}
