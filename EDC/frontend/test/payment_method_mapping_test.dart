import 'package:flutter_test/flutter_test.dart';
import 'package:edc_app/models/shift_attendant_model.dart';

void main() {
  group('Payment Method Mapping Tests', () {
    test('PaymentMethodData should map Chinese payment methods to English', () {
      // 测试数据 - 模拟API返回的中文支付方式
      final Map<String, dynamic> testData = {
        'payment_method': 'cash',
        'payment_method_name': '现金',
        'total_amount': 100000.0,
        'transaction_count': 5,
        'percentage': 50.0,
      };

      // 创建PaymentMethodData实例
      final PaymentMethodData paymentMethod = PaymentMethodData.fromJson(testData);

      // 验证映射结果
      expect(paymentMethod.paymentMethod, equals('cash'));
      expect(paymentMethod.paymentMethodName, equals('Cash')); // 应该映射为英文
      expect(paymentMethod.totalAmount, equals(100000.0));
      expect(paymentMethod.transactionCount, equals(5));
      expect(paymentMethod.percentage, equals(50.0));
    });

    test('PaymentMethodData should handle various Chinese payment methods', () {
      final List<Map<String, dynamic>> testCases = [
        {
          'input': '现金',
          'expected': 'Cash',
        },
        {
          'input': '银行卡',
          'expected': 'Bank Card',
        },
        {
          'input': '信用卡',
          'expected': 'Credit Card',
        },
        {
          'input': '借记卡',
          'expected': 'Debit Card',
        },
        {
          'input': '电子钱包',
          'expected': 'E-Wallet',
        },
        {
          'input': '代金券',
          'expected': 'Voucher',
        },
        {
          'input': '车队卡',
          'expected': 'Fleet Card',
        },
        {
          'input': 'Mandiri', // 英文支付方式应该保持不变
          'expected': 'Mandiri',
        },
        {
          'input': 'Unknown Payment', // 未知支付方式应该保持不变
          'expected': 'Unknown Payment',
        },
      ];

      for (final testCase in testCases) {
        final Map<String, dynamic> testData = {
          'payment_method': 'test',
          'payment_method_name': testCase['input'],
          'total_amount': 100000.0,
          'transaction_count': 1,
          'percentage': 100.0,
        };

        final PaymentMethodData paymentMethod = PaymentMethodData.fromJson(testData);
        
        expect(
          paymentMethod.paymentMethodName,
          equals(testCase['expected']),
          reason: 'Failed to map ${testCase['input']} to ${testCase['expected']}',
        );
      }
    });

    test('PaymentMethodData should handle null and empty values', () {
      final Map<String, dynamic> testData = {
        'payment_method': 'test',
        'payment_method_name': '', // 空字符串
        'total_amount': 0.0,
        'transaction_count': 0,
        'percentage': 0.0,
      };

      final PaymentMethodData paymentMethod = PaymentMethodData.fromJson(testData);
      
      expect(paymentMethod.paymentMethodName, equals(''));
    });
  });
} 