import 'package:edc_app/models/fcc_device.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/fuel_transaction.dart';
import '../models/dispenser_model.dart';
import '../controllers/dispenser_controller.dart';
import 'fcc_device_service.dart';
import 'fcc_status_polling_service.dart';
import 'adapters/fcc_device_adapter_v2.dart';

/// 交易状态同步服务 - V2版本
/// 使用V2适配器进行无硬编码映射的BOS交易状态同步
class TransactionStateSyncService {
  TransactionStateSyncService(
    this._ref, {
    required FCCDeviceService fccDeviceService,
    required FccStatusPollingService pollingService,
  })  : _fccDeviceService = fccDeviceService,
        _pollingService = pollingService {
    debugPrint(
        '🔧 TransactionStateSyncService (V2): 使用FCCDeviceService实例: ${_fccDeviceService.hashCode}');
    debugPrint(
        '🔧 TransactionStateSyncService (V2): 使用FccStatusPollingService实例: ${_pollingService.hashCode}');
  }
  final WidgetRef _ref;
  final FCCDeviceService _fccDeviceService;
  final FccStatusPollingService _pollingService;

  /// 维护nozzle与交易的映射关系，用于状态解锁
  final Map<String, String> _nozzleTransactionMap = <String, String>{}; // nozzleId -> transactionNumber

  /// 调试计数器
  int _syncCallCount = 0;
  int _successfulSyncCount = 0;
  int _failedSyncCount = 0;

  /// 同步交易状态到本地nozzle状态
  Future<void> syncTransactionStates(
      List<FuelTransaction> pendingTransactions) async {
    _syncCallCount++;
    try {
      if (pendingTransactions.isEmpty) {
        debugPrint('📭 TransactionStateSyncService: 没有待处理交易');
        return;
      }

      debugPrint('🔄 TransactionStateSyncService: 处理 ${pendingTransactions.length} 条BOS交易 (调用次数: $_syncCallCount)');

      for (final FuelTransaction transaction in pendingTransactions) {
        // 使用V2适配器根据pump_id和nozzle_id找到对应的EDC nozzle ID
        final String nozzleId = transaction.nozzleId;
        debugPrint('🎯 处理交易: ${transaction.transactionNumber} -> nozzle $nozzleId');
        await _updateNozzleFromTransaction(nozzleId, transaction);

        // 维护nozzle与交易的映射关系
        _nozzleTransactionMap[nozzleId] = transaction.transactionNumber;
      }

      _successfulSyncCount++;
      debugPrint('✅ TransactionStateSyncService: 成功同步 ${pendingTransactions.length} 条交易');
    } catch (e) {
      _failedSyncCount++;
      debugPrint('❌ TransactionStateSyncService: 同步交易状态失败 (失败次数: $_failedSyncCount): $e');
    }
  }

  /// 处理pending交易消失，解锁相关nozzle状态
  Future<void> handleDisappearedTransactions(List<String> disappearedTransactionNumbers) async {
    try {
      if (disappearedTransactionNumbers.isEmpty) return;

      debugPrint('🔓 处理 ${disappearedTransactionNumbers.length} 条消失的交易，解锁相关nozzle状态');

      final DispenserController dispenserController =
          _ref.read(dispenserControllerProvider.notifier);

      // 查找需要解锁的nozzle
      final List<String> nozzlesToUnlock = <String>[];
      
      for (final MapEntry<String, String> entry in _nozzleTransactionMap.entries) {
        if (disappearedTransactionNumbers.contains(entry.value)) {
          nozzlesToUnlock.add(entry.key);
        }
      }

      if (nozzlesToUnlock.isNotEmpty) {
        debugPrint('🔓 需要解锁的nozzle: $nozzlesToUnlock');

        for (final String nozzleId in nozzlesToUnlock) {
          // 清除该nozzle的完成交易
          dispenserController.clearCompletedTransaction(nozzleId);
          debugPrint('✅ 已解锁nozzle $nozzleId 状态，恢复FCC轮询控制');
          
          // 移除映射关系
          _nozzleTransactionMap.remove(nozzleId);
        }
        
        // 手动刷新FCC状态，让nozzle恢复正常状态
        await dispenserController.refreshNozzleStatus();
        debugPrint('🔄 已刷新FCC状态，nozzle状态已恢复');
      }

      // 清理已消失的交易映射
      for (final String transactionNumber in disappearedTransactionNumbers) {
        _nozzleTransactionMap.removeWhere((String key, String value) => value == transactionNumber);
      }

    } catch (e) {
      debugPrint('❌ 处理消失交易失败: $e');
    }
  }

  /// 验证EDC nozzle是否存在
  bool _verifyEdcNozzleExists(DispenserState state, String edcNozzleId) {
    for (final PumpGroup pumpGroup in state.currentPumpGroups) {
      for (final Nozzle nozzle in pumpGroup.nozzles) {
        if (nozzle.id == edcNozzleId) {
          return true;
        }
      }
    }
    return false;
  }

  /// 从EDC nozzle获取对应的FCC设备ID
  /// 使用V2适配器进行反向查找（简化版本）
  String extractPumpIdFromNozzle(Nozzle nozzle) {
    try {
      // 使用V2适配器的反向查找功能，现在直接返回FCCDevice对象
      final FCCDevice? fccDevice =
          FCCDeviceAdapterV2.getFccInfoByEdcId(nozzle.id);

      if (fccDevice != null) {
        debugPrint('✅ V2反向查找成功: EDC ${nozzle.id} → FCC ${fccDevice.id}');
        return fccDevice.id;
      }

      // 如果V2适配器中没有找到，使用兜底策略
      debugPrint('⚠️ V2适配器中未找到EDC ${nozzle.id}的映射，使用兜底策略');
      return 'device_com${nozzle.pumpGroupId}_pump01';
    } catch (e) {
      debugPrint('❌ V2反向查找失败: $e');
      // 兜底策略
      return 'device_com${nozzle.pumpGroupId}_pump01';
    }
  }

  /// 从EDC nozzle获取对应的FCC nozzle ID
  /// 使用V2适配器进行反向查找（简化版本）
  String extractNozzleIdFromNozzle(Nozzle nozzle) {
    try {
      // 使用V2适配器的反向查找功能，现在直接返回FCCDevice和FCCNozzle对象
      final FCCDevice? fccDevice =
          FCCDeviceAdapterV2.getFccInfoByEdcId(nozzle.id);
      final FCCNozzle? fccNozzle =
          FCCDeviceAdapterV2.getFccNozzleByEdcId(nozzle.id);

      if (fccDevice != null && fccNozzle != null) {
        final String fullNozzleId =
            '${fccDevice.id}-nozzle-${fccNozzle.number}';
        debugPrint('✅ V2 nozzle反向查找成功: EDC ${nozzle.id} → FCC $fullNozzleId');
        return fullNozzleId;
      }

      // 如果V2适配器中没有找到，使用兜底策略
      debugPrint('⚠️ V2适配器中未找到EDC ${nozzle.id}的映射，使用兜底策略');
      final String deviceId = extractPumpIdFromNozzle(nozzle);
      final int nozzleNumber = nozzle.number; // 使用新增的number字段替代id % 10
      return '$deviceId-nozzle-$nozzleNumber';
    } catch (e) {
      debugPrint('❌ V2 nozzle反向查找失败: $e');
      // 兜底策略
      final String deviceId = extractPumpIdFromNozzle(nozzle);
      final int nozzleNumber = nozzle.number; // 使用新增的number字段替代id % 10
      return '$deviceId-nozzle-$nozzleNumber';
    }
  }

  /// 根据BOS交易更新本地nozzle状态
  Future<void> _updateNozzleFromTransaction(
      String nozzleId, FuelTransaction transaction) async {
    try {
      final DispenserController dispenserController =
          _ref.read(dispenserControllerProvider.notifier);

      // 优先根据FCC状态判断，然后才看BOS状态
      final NozzleStatus newStatus = _determineNozzleStatus(transaction);

      debugPrint('🎯 状态判断结果: ${newStatus.name}');
      debugPrint('   BOS状态: ${transaction.status}');
      debugPrint(
          '   FCC状态: ${transaction.metadata['fcc_status'] ?? 'unknown'}');
      debugPrint(
          '   FCC完成时间: ${transaction.metadata['fcc_completed_at'] ?? 'none'}');

      // 如果状态没有改变，跳过处理
      final Nozzle? currentNozzle = _getCurrentNozzle(nozzleId);
      if (currentNozzle?.status == newStatus) {
        debugPrint('⏩ Nozzle $nozzleId 状态无变化 (${newStatus.name})，跳过处理');
        return;
      }

      // 创建或更新授权信息
      final AuthorizationRequest authRequest = AuthorizationRequest(
        nozzleId: nozzleId,
        mode: _getAuthModeFromTransaction(transaction),
        value: _getAuthValueFromTransaction(transaction),
        staffId: transaction.employeeId?.toString() ?? 'BOS_AUTO',
        requestTime: transaction.createdAt,
      );

      // 根据交易状态进行相应操作
      switch (newStatus) {
        case NozzleStatus.auth:
          if (currentNozzle?.status == NozzleStatus.idle) {
            debugPrint('📤 执行授权操作 - nozzle $nozzleId');
            await dispenserController.authorizeNozzle(nozzleId, authRequest);
          } else {
            debugPrint('⏩ Nozzle $nozzleId 已经是授权状态，跳过重复授权');
          }
          break;

        case NozzleStatus.complete:
          debugPrint('🎉 设置nozzle $nozzleId 为完成状态 - 可进入结算');

          // 保存完成的交易信息，供点击时直接使用
          dispenserController.saveCompletedTransaction(nozzleId, transaction);

          break;

        case NozzleStatus.fuelling:
          debugPrint('⛽ 更新nozzle $nozzleId 为加油中状态');
          await dispenserController.refreshNozzleStatus();
          break;

        case NozzleStatus.idle:
          debugPrint('🛑 停止nozzle $nozzleId');
          await dispenserController.stopNozzle(nozzleId);
          break;

        default:
          debugPrint('⚠️ 状态 ${newStatus.name} 暂未实现自动更新');
          break;
      }

      debugPrint(
          '✅ 更新nozzle $nozzleId 状态: ${newStatus.name} (基于BOS交易: ${transaction.transactionNumber})');
    } catch (e) {
      debugPrint('❌ 更新nozzle状态失败: $e');
    }
  }

  /// 智能判断Nozzle状态
  NozzleStatus _determineNozzleStatus(FuelTransaction transaction) {
    final String? fccStatus = transaction.metadata['fcc_status']?.toString();
    final String? fccCompleted =
        transaction.metadata['fcc_completed_at']?.toString();
    final bool? fuellingStarted =
        transaction.metadata['fuelling_started'] as bool?;

    // 最高优先级：只要存在pending交易，就强制锁定为complete状态
    // 不论FCC状态如何，都以BOS交易状态为准
    switch (transaction.status) {
      case 'pending':
        debugPrint('🔒 强制锁定: 检测到pending交易，无视FCC状态，设置为complete');
        debugPrint('   交易数据: volume=${transaction.volume}L, amount=${transaction.amount}');
        debugPrint('   FCC状态: ${fccStatus ?? 'unknown'} (被忽略)');
        return NozzleStatus.complete;
      case 'processed':
        return NozzleStatus.complete;
      case 'cancelled':
        return NozzleStatus.idle;
      default:
        // 只有在没有BOS交易状态或状态未知时，才参考FCC状态
        debugPrint('⚠️ 未知BOS状态: ${transaction.status}，回退到FCC状态判断');
        
        if (fccStatus == 'completed' || fccCompleted != null) {
          return NozzleStatus.complete;
        }

        if (fccStatus == 'in_progress' || fuellingStarted == true) {
          return NozzleStatus.fuelling;
        }

        if (fccStatus == 'authorized' || fccStatus == 'preset') {
          return NozzleStatus.auth;
        }

        return NozzleStatus.idle;
    }
  }

  /// 从交易中推断授权模式
  AuthMode _getAuthModeFromTransaction(FuelTransaction transaction) {
    final Map<String, dynamic> metadata = transaction.metadata;

    if (metadata['auth_mode'] != null) {
      switch (metadata['auth_mode'] as String) {
        case 'amount':
          return AuthMode.amount;
        case 'volume':
          return AuthMode.volume;
        case 'full':
          return AuthMode.full;
      }
    }

    if (transaction.amount > 0 && transaction.volume > 0) {
      final double volumeRemainder = transaction.volume % 1;
      if (volumeRemainder < 0.1 || volumeRemainder > 0.9) {
        return AuthMode.volume;
      }
      final double amountRemainder = transaction.amount % 1000;
      if (amountRemainder < 100) {
        return AuthMode.amount;
      }
    }

    return AuthMode.amount;
  }

  /// 从交易中获取授权值
  double? _getAuthValueFromTransaction(FuelTransaction transaction) {
    final AuthMode authMode = _getAuthModeFromTransaction(transaction);

    switch (authMode) {
      case AuthMode.amount:
        return transaction.amount;
      case AuthMode.volume:
        return transaction.volume;
      case AuthMode.full:
        return null;
    }
  }

  /// 获取当前nozzle对象
  Nozzle? _getCurrentNozzle(String nozzleId) {
    try {
      final DispenserState state = _ref.read(dispenserControllerProvider);
      for (final PumpGroup pumpGroup in state.currentPumpGroups) {
        for (final Nozzle nozzle in pumpGroup.nozzles) {
          if (nozzle.id == nozzleId) {
            return nozzle;
          }
        }
      }
      return null;
    } catch (e) {
      debugPrint('❌ 获取nozzle失败: $e');
      return null;
    }
  }

  /// 获取同步服务状态信息（用于调试）
  Map<String, dynamic> getServiceStatus() {
    return <String, dynamic>{
      'syncCallCount': _syncCallCount,
      'successfulSyncCount': _successfulSyncCount,
      'failedSyncCount': _failedSyncCount,
      'nozzleTransactionMapSize': _nozzleTransactionMap.length,
      'fccDeviceServiceHashCode': _fccDeviceService.hashCode,
      'pollingServiceHashCode': _pollingService.hashCode,
    };
  }

  /// 打印详细的调试信息
  void printDebugInfo() {
    final Map<String, dynamic> status = getServiceStatus();
    debugPrint('📊 TransactionStateSyncService 状态:');
    status.forEach((String key, dynamic value) {
      debugPrint('   $key: $value');
    });

    debugPrint('🗺️ Nozzle交易映射:');
    _nozzleTransactionMap.forEach((String nozzleId, String transactionNumber) {
      debugPrint('   $nozzleId -> $transactionNumber');
    });
  }
}

// Note: TransactionStateSyncService is created directly where needed
// to avoid ref type conflicts between ProviderRef and WidgetRef
