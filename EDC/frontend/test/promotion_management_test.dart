import 'package:flutter_test/flutter_test.dart';

/// Promotion Management 页面测试
/// 
/// 验证活动数据的正确性：
/// 1. 所有活动的车辆类型都应该是 "All Type"
/// 2. BP 品牌名称应该是大写
/// 3. 活动时间应该是 7月1-31号
void main() {
  group('Promotion Management 测试', () {
    
    // 活动数据（按照指定的确切内容）
    final List<Map<String, dynamic>> mockPromotions = [
      {
        'id': '1',
        'name': 'Free 1 Liter',
        'type': 'Direct Discount',
        'description': 'Minimum purchase 25 liters above can get discount 1 liter / transaction',
        'startDate': '1 Jan 2025',
        'endDate': '31 Dec 2025',
        'vehicleType': 'All type',
        'product': 'BP 92, BP Ultimate, BP Ultimate Diesel',
        'coverageSite': 'All Site',
        'minPurchase': '25 liters above',
        'discount': '1 liter / transaction',
      },
      {
        'id': '2',
        'name': 'Promo IRIT',
        'type': 'Tiered Discount',
        'description': 'Multiple promotion with different discount rates',
        'startDate': '1 Jul 2025',
        'endDate': '31 Jul 2025',
        'vehicleType': 'All type',
        'product': 'BP 92, BP Ultimate, BP Ultimate Diesel',
        'coverageSite': 'All Site',
        'minPurchase': 'Various',
        'discount': 'Multiple rates',
      },
    ];
    
    test('所有活动的车辆类型都应该是 All type', () {
      for (final promotion in mockPromotions) {
        expect(promotion['vehicleType'], equals('All type'));
      }
    });

    test('应该只有2个核心活动', () {
      expect(mockPromotions.length, equals(2));
    });

    test('所有活动的产品都应该包含BP燃油类型', () {
      for (final promotion in mockPromotions) {
        final String productName = promotion['product'] as String;
        expect(productName, equals('BP 92, BP Ultimate, BP Ultimate Diesel'));
      }
    });
    
    test('活动时间应该正确', () {
      // Free 1 Liter: 全年活动
      expect(mockPromotions[0]['startDate'], equals('1 Jan 2025'));
      expect(mockPromotions[0]['endDate'], equals('31 Dec 2025'));

      // Promo IRIT: 7月活动
      expect(mockPromotions[1]['startDate'], equals('1 Jul 2025'));
      expect(mockPromotions[1]['endDate'], equals('31 Jul 2025'));
    });

    test('活动名称应该匹配指定内容', () {
      final List<String> expectedNames = ['Free 1 Liter', 'Promo IRIT'];

      for (int i = 0; i < mockPromotions.length; i++) {
        final String name = mockPromotions[i]['name'] as String;
        expect(name, equals(expectedNames[i]));
      }
    });

    test('活动类型应该正确', () {
      final List<String> expectedTypes = ['Direct Discount', 'Tiered Discount'];

      for (int i = 0; i < mockPromotions.length; i++) {
        final String type = mockPromotions[i]['type'] as String;
        expect(type, equals(expectedTypes[i]));
      }
    });
    
    test('活动描述应该匹配指定内容', () {
      final List<String> expectedDescriptions = [
        'Minimum purchase 25 liters above can get discount 1 liter / transaction',
        'Multiple promotion with different discount rates'
      ];

      for (int i = 0; i < mockPromotions.length; i++) {
        final String description = mockPromotions[i]['description'] as String;
        expect(description, equals(expectedDescriptions[i]));
      }
    });
    
    test('活动覆盖范围应该是 All Site', () {
      for (final promotion in mockPromotions) {
        expect(promotion['coverageSite'], equals('All Site'));
      }
    });
  });
}
