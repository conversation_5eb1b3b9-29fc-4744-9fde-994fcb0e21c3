import 'package:flutter_test/flutter_test.dart';

/// 客户信息一致性测试
/// 
/// 验证创建订单时和小票显示时的客户信息默认值设置是否一致
void main() {
  group('客户信息一致性测试', () {
    
    test('TERA支付方式 - 创建订单和小票显示应该使用相同的默认值', () {
      // 模拟创建订单时的逻辑（cash_payment_page.dart）
      String? customerNameForOrder;
      String? customerPhone;
      final String paymentMethodName = 'TERA';
      
      // 创建订单时的逻辑
      final bool isTeraPayment = paymentMethodName.toUpperCase() == 'TERA';
      
      if (isTeraPayment) {
        customerNameForOrder = 'ANONIM';
        customerPhone = '1010101010';
      } else {
        if ((customerNameForOrder == null || customerNameForOrder.trim().isEmpty) &&
            (customerPhone == null || customerPhone.trim().isEmpty)) {
          customerNameForOrder = 'ANONIM';
          customerPhone = '1010101010';
        }
      }
      
      // 模拟小票显示时的逻辑（auto_print_service.dart）
      String customerName = '';
      String customerPhoneDisplay = '';
      
      // 假设从订单中获取到了上面设置的值
      if (customerNameForOrder != null && customerNameForOrder.isNotEmpty) {
        customerName = customerNameForOrder;
      }
      if (customerPhone != null && customerPhone.isNotEmpty) {
        customerPhoneDisplay = customerPhone;
      }
      
      // 如果客户名字和电话都为空，根据支付方式设置默认值
      if (customerName.isEmpty && customerPhoneDisplay.isEmpty) {
        final String paymentMethod = paymentMethodName.toLowerCase();
        
        if (paymentMethod.contains('tera')) {
          customerName = 'ANONIM';
          customerPhoneDisplay = '1010101010';
        } else {
          customerName = 'ANONIM';
          customerPhoneDisplay = '1010101010';
        }
      }
      
      // 验证一致性
      expect(customerNameForOrder, equals('ANONIM'));
      expect(customerPhone, equals('1010101010'));
      expect(customerName, equals('ANONIM'));
      expect(customerPhoneDisplay, equals('1010101010'));
    });
    
    test('非TERA支付方式 - 空客户信息应设置相同的默认值', () {
      // 模拟创建订单时的逻辑
      String? customerNameForOrder;
      String? customerPhone;
      final String paymentMethodName = 'CASH';
      
      // 创建订单时的逻辑
      final bool isTeraPayment = paymentMethodName.toUpperCase() == 'TERA';
      
      if (isTeraPayment) {
        customerNameForOrder = 'ANONIM';
        customerPhone = '1010101010';
      } else {
        if ((customerNameForOrder == null || customerNameForOrder.trim().isEmpty) &&
            (customerPhone == null || customerPhone.trim().isEmpty)) {
          customerNameForOrder = 'ANONIM';
          customerPhone = '1010101010';
        }
      }
      
      // 模拟小票显示时的逻辑
      String customerName = '';
      String customerPhoneDisplay = '';
      
      // 假设从订单中获取到了上面设置的值
      if (customerNameForOrder != null && customerNameForOrder.isNotEmpty) {
        customerName = customerNameForOrder;
      }
      if (customerPhone != null && customerPhone.isNotEmpty) {
        customerPhoneDisplay = customerPhone;
      }
      
      // 如果客户名字和电话都为空，根据支付方式设置默认值
      if (customerName.isEmpty && customerPhoneDisplay.isEmpty) {
        final String paymentMethod = paymentMethodName.toLowerCase();
        
        if (paymentMethod.contains('tera')) {
          customerName = 'ANONIM';
          customerPhoneDisplay = '1010101010';
        } else {
          customerName = 'ANONIM';
          customerPhoneDisplay = '1010101010';
        }
      }
      
      // 验证一致性
      expect(customerNameForOrder, equals('ANONIM'));
      expect(customerPhone, equals('1010101010'));
      expect(customerName, equals('ANONIM'));
      expect(customerPhoneDisplay, equals('1010101010'));
    });
    
    test('小票显示 - ANONIM和默认电话号码不应该显示在小票上', () {
      // 模拟小票显示逻辑
      final String customerName = 'ANONIM';
      final String customerPhone = '1010101010';
      
      // 检查是否应该显示客户姓名
      final bool shouldShowName = customerName.isNotEmpty && customerName.toUpperCase() != 'ANONIM';
      
      // 检查是否应该显示客户电话
      final bool shouldShowPhone = customerPhone.isNotEmpty && customerPhone != '1010101010';
      
      // 验证默认值不会显示在小票上
      expect(shouldShowName, isFalse);
      expect(shouldShowPhone, isFalse);
    });
    
    test('小票显示 - 真实客户信息应该正常显示', () {
      // 模拟真实客户信息
      final String customerName = 'John Doe';
      final String customerPhone = '081234567890';
      
      // 检查是否应该显示客户姓名
      final bool shouldShowName = customerName.isNotEmpty && customerName.toUpperCase() != 'ANONIM';
      
      // 检查是否应该显示客户电话
      final bool shouldShowPhone = customerPhone.isNotEmpty && customerPhone != '1010101010';
      
      // 验证真实信息会显示在小票上
      expect(shouldShowName, isTrue);
      expect(shouldShowPhone, isTrue);
    });
    
    test('边界情况 - 空字符串和null值处理', () {
      // 测试空值情况，这些情况应该设置默认值
      final List<String?> emptyNames = [null, '', '   '];
      final List<String?> emptyPhones = [null, '', '   '];

      for (final String? testName in emptyNames) {
        for (final String? testPhone in emptyPhones) {
          // 模拟创建订单时的逻辑
          String? customerNameForOrder = testName;
          String? customerPhone = testPhone;
          final String paymentMethodName = 'CASH';

          final bool isTeraPayment = paymentMethodName.toUpperCase() == 'TERA';

          if (isTeraPayment) {
            customerNameForOrder = 'ANONIM';
            customerPhone = '1010101010';
          } else {
            if ((customerNameForOrder == null || customerNameForOrder.trim().isEmpty) &&
                (customerPhone == null || customerPhone.trim().isEmpty)) {
              customerNameForOrder = 'ANONIM';
              customerPhone = '1010101010';
            }
          }

          // 验证空值情况都设置了默认值
          expect(customerNameForOrder, equals('ANONIM'));
          expect(customerPhone, equals('1010101010'));
        }
      }

      // 测试有效值情况，这些情况应该保持原值
      String? customerNameForOrder = 'John Doe';
      String? customerPhone = '081234567890';
      final String paymentMethodName = 'CASH';

      final bool isTeraPayment = paymentMethodName.toUpperCase() == 'TERA';

      if (isTeraPayment) {
        customerNameForOrder = 'ANONIM';
        customerPhone = '1010101010';
      } else {
        if ((customerNameForOrder == null || customerNameForOrder.trim().isEmpty) &&
            (customerPhone == null || customerPhone.trim().isEmpty)) {
          customerNameForOrder = 'ANONIM';
          customerPhone = '1010101010';
        }
      }

      // 验证有效值保持不变
      expect(customerNameForOrder, equals('John Doe'));
      expect(customerPhone, equals('081234567890'));
    });
  });
}
