import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';
import '../../constants/bp_colors.dart';
import '../../services/api/employee_api.dart';
import '../auth/auth_service.dart';
import '../../services/api/employee_api.dart';
import '../../widgets/safe_scaffold.dart';
import '../../theme/app_theme.dart';
import '../../services/timezone_service.dart';
import 'timezone_settings_page.dart';

class SettingsHomePage extends ConsumerStatefulWidget {
  const SettingsHomePage({super.key});

  @override
  ConsumerState<SettingsHomePage> createState() => _SettingsHomePageState();
}

class _SettingsHomePageState extends ConsumerState<SettingsHomePage> {
  PackageInfo? _packageInfo;
  String _deviceInfo = 'Loading...';
  String _deviceId = 'Loading...';
  DateTime? _loginTime;

  @override
  void initState() {
    super.initState();
    _loadAppInfo();
    _loadLoginTime();
  }

  Future<void> _loadAppInfo() async {
    try {
      final PackageInfo packageInfo = await PackageInfo.fromPlatform();
      final String deviceInfo = await _getDeviceInfo();
      final String deviceId = await _getDeviceId();

      if (mounted) {
        setState(() {
          _packageInfo = packageInfo;
          _deviceInfo = deviceInfo;
          _deviceId = deviceId;
        });
      }
    } catch (e) {
      debugPrint('加载应用信息失败: $e');
      if (mounted) {
        setState(() {
          _deviceInfo = 'Unknown Device';
          _deviceId = 'Unknown ID';
        });
      }
    }
  }

  Future<String> _getDeviceInfo() async {
    try {
      final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        final AndroidDeviceInfo androidInfo =
            await deviceInfoPlugin.androidInfo;
        return '${androidInfo.brand} ${androidInfo.model}';
      } else if (Platform.isIOS) {
        final IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;
        return '${iosInfo.name} ${iosInfo.model}';
      }
      return 'Unknown Device';
    } catch (e) {
      return 'Unknown Device';
    }
  }

  Future<String> _getDeviceId() async {
    try {
      final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        final AndroidDeviceInfo androidInfo =
            await deviceInfoPlugin.androidInfo;
        return androidInfo.id ?? 'Unknown ID';
      } else if (Platform.isIOS) {
        final IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;
        return iosInfo.identifierForVendor ?? 'Unknown ID';
      }
      return 'Unknown ID';
    } catch (e) {
      return 'Unknown ID';
    }
  }

  Future<void> _loadLoginTime() async {
    setState(() {
      _loginTime = DateTime.now();
    });
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return 'Unknown';
    final TimezoneService timezoneService = ref.read(timezoneServiceProvider);
    return timezoneService.formatDateTime(dateTime);
  }

  String _getRoleDisplayName(Employee? employee) {
    if (employee == null) return 'Unknown';
    return 'Employee';
  }

  // 显示账户信息对话框
  void _showAccountInfoDialog(Employee? employee) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: const Row(
          children: <Widget>[
            Icon(Icons.person, color: BPColors.primary, size: 22),
            SizedBox(width: 8),
            Text('Account Information',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: BPColors.primary,
                )),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _buildDialogInfoRow('Username', employee?.name ?? 'Loading...'),
            const SizedBox(height: 10),
            _buildDialogInfoRow(
                'Employee ID', employee?.employeeNo ?? 'Loading...'),
            const SizedBox(height: 10),
            _buildDialogInfoRow('Role', _getRoleDisplayName(employee)),
            const SizedBox(height: 10),
            _buildDialogInfoRow('Login Time', _formatDateTime(_loginTime)),
          ],
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close',
                style: TextStyle(
                  fontSize: 16,
                  color: BPColors.primary,
                  fontWeight: FontWeight.w600,
                )),
          ),
        ],
      ),
    );
  }

  // 显示设备信息对话框
  void _showDeviceInfoDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: const Row(
          children: <Widget>[
            Icon(Icons.phone_android, color: BPColors.primary, size: 22),
            SizedBox(width: 8),
            Text('Device Information',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: BPColors.primary,
                )),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _buildDialogInfoRow('Device Model', _deviceInfo),
            const SizedBox(height: 10),
            _buildDialogInfoRow('Device ID', _deviceId),
          ],
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close',
                style: TextStyle(
                  fontSize: 16,
                  color: BPColors.primary,
                  fontWeight: FontWeight.w600,
                )),
          ),
        ],
      ),
    );
  }



  // 显示应用信息对话框
  void _showAppInfoDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: const Row(
          children: <Widget>[
            Icon(Icons.info, color: BPColors.primary, size: 22),
            SizedBox(width: 8),
            Text('Application Information',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: BPColors.primary,
                )),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _buildDialogInfoRow('App Name', _packageInfo?.appName ?? 'BP EDC'),
            const SizedBox(height: 10),
            _buildDialogInfoRow(
                'Version', _packageInfo?.version ?? 'Loading...'),
            const SizedBox(height: 10),
            _buildDialogInfoRow(
                'Build', _packageInfo?.buildNumber ?? 'Loading...'),
          ],
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close',
                style: TextStyle(
                  fontSize: 16,
                  color: BPColors.primary,
                  fontWeight: FontWeight.w600,
                )),
          ),
        ],
      ),
    );
  }



  // 处理退出登录
  Future<void> _handleLogout() async {
    final bool confirmed = await showDialog<bool>(
          context: context,
          builder: (BuildContext context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            title: const Row(
              children: <Widget>[
                Icon(Icons.logout, color: BPColors.error, size: 22),
                SizedBox(width: 8),
                Text('Confirm Logout',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: BPColors.error,
                    )),
              ],
            ),
            content: const Text(
              'Are you sure you want to logout?',
              style: TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel',
                    style: TextStyle(
                      fontSize: 16,
                      color: BPColors.neutral,
                      fontWeight: FontWeight.w600,
                    )),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Logout',
                    style: TextStyle(
                      fontSize: 16,
                      color: BPColors.error,
                      fontWeight: FontWeight.w600,
                    )),
              ),
            ],
          ),
        ) ??
        false;

    if (confirmed && mounted) {
      await ref.read(authServiceProvider).logout();
    }
  }

  @override
  Widget build(BuildContext context) {
    final AuthService authService = ref.watch(authServiceProvider);
    final Employee? currentEmployee = authService.currentEmployee;

    return SafeScaffold(
      appBar: AppBar(
        title: const Text(
          'Settings',
          style: EDCTextStyles.appBarTitle,
        ),
        backgroundColor: BPColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: 64,
      ),
      body: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          children: <Widget>[
            Expanded(
              child: ListView(
                children: <Widget>[
                  // 账户信息菜单项
                  _buildMenuTile(
                    icon: Icons.person,
                    title: 'Account Information',
                    onTap: () => _showAccountInfoDialog(currentEmployee),
                  ),

                  const SizedBox(height: 8),

                  // 设备信息菜单项
                  _buildMenuTile(
                    icon: Icons.phone_android,
                    title: 'Device Information',
                    onTap: _showDeviceInfoDialog,
                  ),

                  const SizedBox(height: 8),

                  // 时区设置菜单项
                  _buildMenuTile(
                    icon: Icons.access_time,
                    title: 'Timezone Settings',
                    onTap: () => Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const TimezoneSettingsPage(),
                      ),
                    ),
                  ),

                  const SizedBox(height: 8),

                  // 应用信息菜单项
                  _buildMenuTile(
                    icon: Icons.info,
                    title: 'Application Information',
                    onTap: _showAppInfoDialog,
                  ),
                ],
              ),
            ),

            // 弱化的退出登录按钮
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 14),
              child: TextButton.icon(
                onPressed: _handleLogout,
                icon:
                    const Icon(Icons.logout, size: 18, color: BPColors.neutral),
                label: const Text(
                  'Logout',
                  style: TextStyle(
                    fontSize: 16,
                    color: BPColors.neutral,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(
                        color: BPColors.neutral.withOpacity(0.3), width: 1),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建菜单项
  Widget _buildMenuTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: BPColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: BPColors.primary, size: 22),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 17,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: BPColors.neutral,
          size: 16,
        ),
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 6),
        dense: true,
      ),
    );
  }

  // 构建对话框信息行
  Widget _buildDialogInfoRow(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          label,
          style: const TextStyle(
            fontSize: 15,
            color: BPColors.neutral,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 3),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black87,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }


}
