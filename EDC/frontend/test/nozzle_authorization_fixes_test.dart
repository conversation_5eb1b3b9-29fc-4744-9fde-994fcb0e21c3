import 'package:flutter_test/flutter_test.dart';
import 'package:edc_app/models/dispenser_model.dart';
import 'package:edc_app/services/api/fcc_device_api.dart';

void main() {
  group('Nozzle Authorization Fixes Tests', () {
    
    group('Default Price for Fuel Grades', () {
      double getDefaultPriceForFuelGrade(String fuelGrade) {
        final grade = fuelGrade.toLowerCase();
        
        if (grade.contains('92') || grade.contains('pertalite')) {
          return 12370.0;
        } else if (grade.contains('95') || grade.contains('pertamax')) {
          return 12840.0;  
        } else if (grade.contains('98') || grade.contains('turbo')) {
          return 13850.0;
        } else if (grade.contains('diesel') || grade.contains('dex') || grade.contains('solar')) {
          return 13250.0;
        } else {
          return 12500.0;
        }
      }

      test('Default prices for common fuel grades', () {
        // 测试常见油品的默认价格
        expect(getDefaultPriceForFuelGrade('92'), equals(12370.0));
        expect(getDefaultPriceForFuelGrade('Pertalite'), equals(12370.0));
        expect(getDefaultPriceForFuelGrade('95'), equals(12840.0));
        expect(getDefaultPriceForFuelGrade('Pertamax'), equals(12840.0));
        expect(getDefaultPriceForFuelGrade('98'), equals(13850.0));
        expect(getDefaultPriceForFuelGrade('Turbo'), equals(13850.0));
        expect(getDefaultPriceForFuelGrade('Diesel'), equals(13250.0));
        expect(getDefaultPriceForFuelGrade('Solar'), equals(13250.0));
        expect(getDefaultPriceForFuelGrade('Unknown'), equals(12500.0));
      });

      test('Case insensitive fuel grade matching', () {
        expect(getDefaultPriceForFuelGrade('BP 92'), equals(12370.0));
        expect(getDefaultPriceForFuelGrade('bp pertamax'), equals(12840.0));
        expect(getDefaultPriceForFuelGrade('BP Ultimate Diesel'), equals(13250.0));
      });
    });

    group('Preset Value Formatting', () {
      test('Amount values maintain precision', () {
        const testAmounts = [50000.0, 100000.5, 200000.25, 500000.99];
        
        for (final amount in testAmounts) {
          // 修复后：使用toString()而不是toInt().toString()
          final formattedCorrect = amount.toString();
          final formattedWrong = amount.toInt().toString();
          
          // 验证正确的格式保持精度
          expect(double.parse(formattedCorrect), equals(amount));
          
          // 验证错误的格式丢失精度（对于有小数的值）
          if (amount % 1 != 0) {
            expect(double.parse(formattedWrong), isNot(equals(amount)));
          }
        }
      });

      test('Volume values maintain precision', () {
        const testVolumes = [10.0, 20.5, 30.75, 50.99];
        
        for (final volume in testVolumes) {
          final formatted = volume.toString();
          expect(double.parse(formatted), equals(volume));
        }
      });
    });

    group('Error Handling', () {
      test('FCCApiException types', () {
        final notFoundError = FCCApiException('Device not found', 404);
        final networkError = FCCApiException('Connection failed', 500);
        final clientError = FCCApiException('Bad request', 400);

        expect(notFoundError.isNotFound, isTrue);
        expect(notFoundError.isNetworkError, isFalse);
        expect(notFoundError.isClientError, isTrue);

        expect(networkError.isNotFound, isFalse);
        expect(networkError.isNetworkError, isTrue);
        expect(networkError.isClientError, isFalse);

        expect(clientError.isNotFound, isFalse);
        expect(clientError.isNetworkError, isFalse);
        expect(clientError.isClientError, isTrue);
      });
    });

    group('Estimation Calculations', () {
      test('Amount to volume estimation', () {
        const amount = 100000.0; // 100,000 IDR
        const price = 12370.0;   // 12,370 IDR per liter
        
        final estimatedVolume = amount / price;
        expect(estimatedVolume, closeTo(8.09, 0.01)); // ~8.09 liters
      });

      test('Volume to amount estimation', () {
        const volume = 10.0;     // 10 liters
        const price = 12370.0;   // 12,370 IDR per liter
        
        final estimatedAmount = volume * price;
        expect(estimatedAmount, equals(123700.0)); // 123,700 IDR
      });

      test('Zero value handling', () {
        const price = 12370.0;
        
        // 零值应该产生零估算
        expect(0.0 / price, equals(0.0));
        expect(0.0 * price, equals(0.0));
      });
    });

    group('Device Mapping Edge Cases', () {
      test('Fallback device ID generation', () {
        // 测试回退设备ID生成
        for (int pumpGroup = 1; pumpGroup <= 8; pumpGroup++) {
          final fallbackDeviceId = 'device_com${pumpGroup}_pump01';
          expect(fallbackDeviceId, matches(RegExp(r'device_com\d+_pump01')));
        }
      });

      test('Nozzle ID validation ranges', () {
        // 测试有效的nozzle ID范围
        const validRanges = [
          {'pumpGroup': 1, 'nozzle': 1, 'edcId': 11},
          {'pumpGroup': 8, 'nozzle': 9, 'edcId': 89},
          {'pumpGroup': 4, 'nozzle': 5, 'edcId': 45},
        ];

        for (final range in validRanges) {
          final pumpGroup = range['pumpGroup'] as int;
          final nozzle = range['nozzle'] as int;
          final expectedId = range['edcId'] as int;
          
          // 验证ID计算
          final calculatedId = pumpGroup * 10 + nozzle;
          expect(calculatedId, equals(expectedId));
          
          // 验证反向计算
          final extractedPumpGroup = calculatedId ~/ 10;
          final extractedNozzle = calculatedId % 10;
          expect(extractedPumpGroup, equals(pumpGroup));
          expect(extractedNozzle, equals(nozzle));
          
          // 验证有效性
          expect(pumpGroup >= 1 && pumpGroup <= 8, isTrue);
          expect(nozzle >= 1 && nozzle <= 9, isTrue);
        }
      });
    });

    group('UI Text Overflow Prevention', () {
      test('Long price text handling', () {
        const longPrices = [
          12370000.0,  // Very high price
          1.0,         // Very low price
          999999999.0, // Maximum realistic price
        ];

        for (final price in longPrices) {
          // 模拟格式化逻辑
          String formatRupiah(double amount) {
            if (amount.isNaN || amount.isInfinite) return 'N/A';
            if (amount == 0) return '0';
            
            final intAmount = amount.toInt();
            String str = intAmount.toString();
            if (str.length <= 3) return str;
            
            String result = '';
            int counter = 0;
            for (int i = str.length - 1; i >= 0; i--) {
              if (counter == 3) {
                result = '.$result';
                counter = 0;
              }
              result = str[i] + result;
              counter++;
            }
            return result;
          }

          final formatted = 'Rp ${formatRupiah(price)}/L';
          
          // 验证格式化不产生异常
          expect(formatted, isNotEmpty);
          expect(formatted, contains('Rp'));
          expect(formatted, contains('/L'));
        }
      });
    });
  });
} 