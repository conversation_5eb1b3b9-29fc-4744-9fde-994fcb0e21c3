import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

class FuelTransaction extends Equatable {
  const FuelTransaction({
    required this.id,
    required this.transactionNumber,
    required this.stationId,
    required this.pumpId,
    required this.nozzleId,
    required this.fuelType,
    required this.fuelGrade,
    required this.unitPrice,
    required this.volume,
    required this.amount,
    required this.status,
    this.memberCardId,
    this.memberId,
    this.employeeId,
    this.fccTransactionId,
    this.posTerminalId,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.processedAt,
    this.cancelledAt,
  });

  factory FuelTransaction.fromJson(Map<String, dynamic> json) {
    final double rawUnitPrice = (json['unit_price'] as num).toDouble();
    final double rawAmount = (json['amount'] as num).toDouble();

    return FuelTransaction(
      id: json['id'] as String,
      transactionNumber: json['transaction_number'] as String,
      stationId: json['station_id'] as int,
      pumpId: json['pump_id'] as String,
      nozzleId: json['nozzle_id'] as String,
      fuelType: json['fuel_type'] as String,           // 油品 ID, 1;2;3
      fuelGrade: json['fuel_grade']?.toString() ?? '', // 油品名称，如 BP_92; BP Ultimate; BP Ultimate Diesel
      unitPrice: rawUnitPrice,
      volume: (json['volume'] as num).toDouble(),
      amount: rawAmount,
      status: json['status'] as String,
      memberCardId: json['member_card_id'] as String?,
      memberId: _safeParseInt(json['member_id']),
      employeeId: _safeParseInt(json['user_id'] ?? json['employee_id']),  // 优先使用 user_id
      fccTransactionId: json['fcc_transaction_id'] as String?,
      posTerminalId: json['pos_terminal_id'] as String?,
      metadata:
          (json['metadata'] as Map<String, dynamic>?) ?? <String, dynamic>{},
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      processedAt: json['processed_at'] != null
          ? DateTime.parse(json['processed_at'] as String)
          : null,
      cancelledAt: json['cancelled_at'] != null
          ? DateTime.parse(json['cancelled_at'] as String)
          : null,
    );
  }

  /// 安全地解析整数字段
  /// 支持 String 和 int 类型的输入
  static int? _safeParseInt(dynamic value) {
    if (value == null) return null;
    
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      if (value.isEmpty || value == 'null') return null;
      return int.tryParse(value);
    }
    
    // 尝试转换其他类型
    try {
      if (value is num) {
        return value.toInt();
      }
      // 尝试转换为字符串再解析
      final String stringValue = value.toString();
      if (stringValue.isEmpty || stringValue == 'null') return null;
      return int.tryParse(stringValue);
    } catch (e) {
      // 转换失败，返回 null
      return null;
    }
  }
  final String id;
  final String transactionNumber;
  final int stationId;
  final String pumpId;
  final String nozzleId;
  final String fuelType;
  final String fuelGrade;
  final double unitPrice;
  final double volume;
  final double amount;
  final String status;
  final String? memberCardId;
  final int? memberId;
  final int? employeeId;
  final String? fccTransactionId;
  final String? posTerminalId;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? processedAt;
  final DateTime? cancelledAt;

  @override
  List<Object?> get props => <Object?>[
        id,
        transactionNumber,
        stationId,
        pumpId,
        nozzleId,
        fuelType,
        fuelGrade,
        unitPrice,
        volume,
        amount,
        status,
        memberCardId,
        memberId,
        employeeId,
        fccTransactionId,
        posTerminalId,
        metadata,
        createdAt,
        updatedAt,
        processedAt,
        cancelledAt,
      ];

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'transaction_number': transactionNumber,
      'station_id': stationId,
      'pump_id': pumpId,
      'nozzle_id': nozzleId,
      'fuel_type': fuelType,
      'fuel_grade': fuelGrade,
      'unit_price': unitPrice,
      'volume': volume,
      'amount': amount,
      'status': status,
      'member_card_id': memberCardId,
      'member_id': memberId,
              'user_id': employeeId,  // 使用 user_id 字段名
      'fcc_transaction_id': fccTransactionId,
      'pos_terminal_id': posTerminalId,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'processed_at': processedAt?.toIso8601String(),
      'cancelled_at': cancelledAt?.toIso8601String(),
    };
  }
}
// 分页响应模型
class FuelTransactionResponse {
  const FuelTransactionResponse({
    required this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPage,
  });

  factory FuelTransactionResponse.fromJson(Map<String, dynamic> json) {
    // debugPrint('正在解析燃油交易响应数据: ${json.keys.toList()}');

    // 获取items数组
    final List? itemsList = json['items'] as List<dynamic>?;
    if (itemsList == null) {
      debugPrint('警告: 响应中没有items字段');
      return const FuelTransactionResponse(
        items: <FuelTransaction>[],
        total: 0,
        page: 1,
        pageSize: 10,
        totalPage: 0,
      );
    }

    // 解析items数组
    final List<FuelTransaction> items = <FuelTransaction>[];
    for (int i = 0; i < itemsList.length; i++) {
      try {
        final Map<String, dynamic> item = itemsList[i] as Map<String, dynamic>;
        items.add(FuelTransaction.fromJson(item));
      } catch (e) {
        debugPrint('警告: 解析第${i + 1}个交易记录失败: $e');
        debugPrint('问题数据: ${itemsList[i]}');
        // 继续处理其他记录，不因为单个记录错误而失败
      }
    }

    debugPrint('成功解析${items.length}/${itemsList.length}条交易记录');

    return FuelTransactionResponse(
      items: items,
      total: (json['total'] as int?) ?? items.length,
      page: (json['page'] as int?) ?? 1,
      pageSize: (json['page_size'] as int?) ?? items.length,
      totalPage: (json['total_page'] as int?) ?? 1,
    );
  }
  final List<FuelTransaction> items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPage;
}

// 查询参数模型
class FuelTransactionQueryParams {
  const FuelTransactionQueryParams({
    this.stationId,
    this.status,
    this.pumpId,
    this.memberId,
    this.dateFrom,
    this.dateTo,
    this.transactionNumber,
    this.fuelType,
    this.fuelGrade,
    this.page = 1,
    this.limit = 10,
    this.sortBy = 'created_at',
    this.sortDir = 'desc',
  });
  final int? stationId;
  final String? status;
  final String? pumpId;
  final int? memberId;
  final String? dateFrom;
  final String? dateTo;
  final String? transactionNumber;
  final String? fuelType;
  final String? fuelGrade;
  final int page;
  final int limit;
  final String sortBy;
  final String sortDir;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> params = <String, dynamic>{
      'page': page,
      'limit': limit,
      'sort_by': sortBy,
      'sort_dir': sortDir,
    };

    if (stationId != null) params['station_id'] = stationId;
    if (status != null) params['status'] = status;
    if (pumpId != null) params['pump_id'] = pumpId;
    if (memberId != null) params['member_id'] = memberId;
    if (dateFrom != null) params['date_from'] = dateFrom;
    if (dateTo != null) params['date_to'] = dateTo;
    if (transactionNumber != null)
      params['transaction_number'] = transactionNumber;
    if (fuelType != null) params['fuel_type'] = fuelType;
    if (fuelGrade != null) params['fuel_grade'] = fuelGrade;

    return params;
  }
}
