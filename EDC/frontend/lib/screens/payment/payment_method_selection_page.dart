import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../models/payment_method.dart';
import '../../models/payment_transaction_data.dart';
import '../../services/api/api_service.dart';
import '../../services/member_cache_service.dart';
import '../../widgets/safe_scaffold.dart';
import '../../widgets/app_loading_indicator.dart';
import '../../widgets/bp_app_bar.dart';
import '../../widgets/member_info_bottom_bar.dart';
import '../../constants/bp_colors.dart';
import '../../constants/customer_type_constants.dart';

class PaymentMethodSelectionPage extends StatefulWidget {
  const PaymentMethodSelectionPage({super.key, this.paymentData});
  final PaymentTransactionData? paymentData;

  @override
  State<PaymentMethodSelectionPage> createState() =>
      _PaymentMethodSelectionPageState();
}

class _PaymentMethodSelectionPageState
    extends State<PaymentMethodSelectionPage> {
  // 支付方式列表
  List<PaymentMethod>? _paymentMethods;

  // 加载状态
  bool _isLoading = true;

  // 错误信息
  String? _errorMessage;

  // Member cache service
  late final MemberCacheService _memberCacheService;

  @override
  void initState() {
    super.initState();
    _memberCacheService = memberCacheService; // 使用全局单例

    debugPrint('🚀 [PaymentMethodSelection] initState 开始');
    debugPrint('   paymentData: ${widget.paymentData != null ? "存在" : "不存在"}');
    debugPrint('   hasCachedMember: ${_memberCacheService.hasCachedMember}');

    // 检查是否为 B2B 客户，如果是则自动跳过支付方式选择
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint('🔄 [PaymentMethodSelection] PostFrameCallback 执行');
      _checkAndHandleB2BCustomer();
    });

    _loadPaymentMethods();
  }

  @override
  void dispose() {
    // 不需要dispose单例服务，它由应用程序管理
    super.dispose();
  }

  // 加载支付方式列表
  Future<void> _loadPaymentMethods() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 直接从PaymentTransactionData获取站点ID
      final int? stationId = widget.paymentData?.stationId;

      // 调用API获取支付方式列表
      final PaymentMethodResponse response =
          await ApiService().paymentApi.getPaymentMethods(
                enabled: true,
                stationId: stationId,
              );

      if (response.success && response.data.isNotEmpty) {
        // 获取API返回的支付方式
        final List<PaymentMethod> apiMethods = response.data;

        // 为银行卡支付方式设置业务规则限制
        _applyBusinessRules(apiMethods);

        // 添加额外的支付方式（voucher和tera）
        apiMethods.addAll(_getAdditionalPaymentMethods());

        // 根据客户类型过滤支付方式
        final List<PaymentMethod> filteredMethods = _filterPaymentMethodsByCustomerType(apiMethods);

        setState(() {
          _paymentMethods = filteredMethods;
          _isLoading = false;
        });

        // 调试信息：打印支付方式列表
        print('💳 Payment methods loaded: ${apiMethods.length}');
        for (PaymentMethod method in apiMethods) {
          print(
              '  - ${method.displayName} (${method.type}) - Group: ${method.groupName}');
        }
        print(
            '👤 Current member cache status: ${_memberCacheService.hasCachedMember ? "Has member" : "No member"}');
      } else {
        // 如果API没有返回数据，至少显示我们的模拟支付方式
        final List<PaymentMethod> additionalMethods =
            _getAdditionalPaymentMethods();

        // 根据客户类型过滤支付方式
        final List<PaymentMethod> filteredMethods = _filterPaymentMethodsByCustomerType(additionalMethods);

        setState(() {
          _paymentMethods = filteredMethods;
          _isLoading = false;
        });
        print('💳 Using fallback payment methods: ${filteredMethods.length}');
      }
    } catch (e) {
      // 如果API调用失败，仍然显示我们的模拟支付方式
      final List<PaymentMethod> additionalMethods =
          _getAdditionalPaymentMethods();

      // 根据客户类型过滤支付方式
      final List<PaymentMethod> filteredMethods = _filterPaymentMethodsByCustomerType(additionalMethods);

      setState(() {
        _paymentMethods = filteredMethods;
        _isLoading = false;
        _errorMessage = null; // 清除错误，因为我们有备用数据
      });
      print(
          '💳 API failed, using fallback payment methods: ${filteredMethods.length}');
      print('🚨 Error: $e');
    }
  }

  /// 根据客户类型过滤支付方式
  List<PaymentMethod> _filterPaymentMethodsByCustomerType(List<PaymentMethod> methods) {
    // 检测当前客户类型
    CustomerType currentCustomerType = CustomerType.b2c; // 默认为 B2C

    if (_memberCacheService.hasCachedMember) {
      final member = _memberCacheService.cachedMember;
      if (member != null) {
        currentCustomerType = CustomerTypeUtils.detectCustomerType(
          member.metadata,
          memberId: member.id
        );
      }
    }

    debugPrint('🔍 [支付方式过滤] 当前客户类型: ${currentCustomerType.value}');
    debugPrint('   原始支付方式数量: ${methods.length}');

    // 过滤支付方式
    final List<PaymentMethod> filteredMethods = methods.where((method) {
      // 检查是否为 B2B 专用支付方式
      final bool isB2BPaymentMethod = _isB2BPaymentMethod(method);

      if (isB2BPaymentMethod) {
        debugPrint('   🏢 发现 B2B 支付方式: ${method.displayName}');
        // 如果是 B2B 支付方式，只有 B2B 客户才能看到
        final bool shouldShow = currentCustomerType == CustomerType.b2b;
        debugPrint('     ${shouldShow ? "✅ 显示" : "❌ 隐藏"} (客户类型: ${currentCustomerType.value})');
        return shouldShow;
      } else {
        debugPrint('   🏠 普通支付方式: ${method.displayName} - ✅ 显示');
        // 普通支付方式，所有客户都可以看到
        return true;
      }
    }).toList();

    debugPrint('   过滤后支付方式数量: ${filteredMethods.length}');

    return filteredMethods;
  }

  /// 检查是否为 B2B 专用支付方式
  bool _isB2BPaymentMethod(PaymentMethod method) {
    // 根据支付方式的特征判断是否为 B2B 专用
    // 可以根据以下条件判断：
    // 1. 支付方式名称包含 "B2B" 或 "Business"
    // 2. 支付方式类型为特定的 B2B 类型
    // 3. 支付方式配置中标记为 B2B 专用

    final String displayName = method.displayName.toLowerCase();
    final String type = method.type.toLowerCase();

    // 检查名称中是否包含 B2B 相关关键词
    if (displayName.contains('b2b') ||
        displayName.contains('business') ||
        displayName.contains('corporate') ||
        displayName.contains('company')) {
      return true;
    }

    // 检查类型是否为 B2B 专用类型
    if (type.contains('b2b') ||
        type.contains('business') ||
        type.contains('corporate')) {
      return true;
    }

    // 检查配置中是否标记为 B2B 专用
    if (method.gatewayConfig != null) {
      final dynamic isB2BOnly = method.gatewayConfig!['isB2BOnly'];
      if (isB2BOnly == true || isB2BOnly == 'true') {
        return true;
      }
    }

    return false;
  }

  // 获取额外的支付方式
  List<PaymentMethod> _getAdditionalPaymentMethods() {
    final String now = DateTime.now().toIso8601String();
    return <PaymentMethod>[
      PaymentMethod(
        id: 999901,
        type: 'VOUCHER',
        name: 'voucher_payment',
        displayName: 'Voucher',
        description: 'Voucher',
        gatewayType: 'internal',
        enabled: true,
        sortOrder: 100,
        groupName: 'others',
        createdAt: now,
        updatedAt: now,
      ),
      // 删除TERA支付方式，不再写死在代码中
    ];
  }

  // 处理支付方式选择
  void _handlePaymentMethodSelected(PaymentMethod method) {
    if (widget.paymentData == null) return;

    // 获取当前交易金额
    final double currentAmount = widget.paymentData!.totalAmount;
    
    // 检查支付方式的金额限制
    if (method.minAmount != null && currentAmount < method.minAmount!) {
      _showAmountLimitDialog(method, currentAmount);
      return;
    }
    
    if (method.maxAmount != null && currentAmount > method.maxAmount!) {
      _showAmountLimitDialog(method, currentAmount, isMaxLimit: true);
      return;
    }

    // 检查是否需要用户信息验证
    // Tera 支付方式跳过用户信息验证
    final bool isTeraPayment = method.displayName.toUpperCase() == 'TERA' || 
                               method.name.toUpperCase() == 'TERA' ||
                               method.type.toUpperCase() == 'TERA';
    
    debugPrint('🔍 支付方式验证检查:');
    debugPrint('   是否为 Tera 支付: $isTeraPayment');
    debugPrint('   当前会员缓存状态: ${_memberCacheService.hasCachedMember}');
    
    if (!isTeraPayment && !_memberCacheService.hasCachedMember) {
      debugPrint('❌ 非 Tera 支付且无会员信息，显示用户信息必填对话框');
      _showCustomerInfoRequiredDialog(method);
      return;
    } else if (isTeraPayment) {
      debugPrint('✅ Tera 支付方式，跳过用户信息验证');
    } else {
      debugPrint('✅ 已有会员信息，继续支付流程');
    }

    // 创建包含支付方式ID、名称和类型的新PaymentTransactionData
    debugPrint('🏦 选择的支付方式:');
    debugPrint('   ID: ${method.id}');
    debugPrint('   名称: ${method.displayName}');
    debugPrint('   类型: ${method.type}');
    debugPrint('   最低金额: ${method.minAmount}');
    debugPrint('   最高金额: ${method.maxAmount}');
    debugPrint('   当前金额: $currentAmount');
    debugPrint('   配置: ${method.gatewayConfig}');
    
    final PaymentTransactionData paymentDataWithMethod =
        widget.paymentData!.copyWith(
      paymentMethodId: method.id,
      paymentMethodName: method.displayName,
      paymentMethodType: method.type, // 传递支付方式类型
      paymentMethodConfig: method.gatewayConfig, // 传递支付方式配置信息
    );

    // 根据支付方式类型导航到对应的支付页面
    switch (method.type) {
      case 'CASH':
        context.push('/payment/cash', extra: paymentDataWithMethod);
        break;
      case 'VOUCHER':
        // Voucher支付暂时不支持
        _showVoucherUnsupportedDialog();
        break;
      case 'BANK_CARD':
      case 'POINTS':
      case 'TERA':
        // 所有 non-cash 支付方式都进入 cash_payment 页面，通过 paymentMethodId 区分
        // Tera 支付方式已跳过用户信息验证
        context.push('/payment/cash', extra: paymentDataWithMethod);
        break;
      default:
        // 对于其他支付方式，检查是否是 Tera（通过名称识别）
        if (isTeraPayment) {
          context.push('/payment/cash', extra: paymentDataWithMethod);
        } else {
          // 默认显示一个提示，表示该支付方式尚未实现
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  '${method.displayName} payment method not implemented yet'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        break;
    }
  }

  /// 显示Voucher不支持对话框
  void _showVoucherUnsupportedDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Row(
            children: <Widget>[
              Icon(
                Icons.info_outline,
                color: Colors.orange,
                size: 28,
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Payment Method Not Available',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const Text(
                'Voucher payment is temporarily not supported.',
                style: TextStyle(
                  fontSize: 16,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: const Row(
                  children: <Widget>[
                    Icon(
                      Icons.payment,
                      color: Colors.orange,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Please choose another payment method to complete your transaction.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF1D1D1F),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'OK',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 显示金额限制对话框
  void _showAmountLimitDialog(PaymentMethod method, double currentAmount, {bool isMaxLimit = false}) {
    final NumberFormat currencyFormatter = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    final String limitAmount = isMaxLimit
        ? currencyFormatter.format(method.maxAmount!)
        : currencyFormatter.format(method.minAmount!);
    
    final String currentAmountStr = currencyFormatter.format(currentAmount);
    
    final String title = isMaxLimit ? 'Amount Too High' : 'Amount Too Low';
    final String message = isMaxLimit
        ? 'The maximum amount for ${method.displayName} is $limitAmount.\nCurrent amount: $currentAmountStr'
        : 'The minimum amount for ${method.displayName} is $limitAmount.\nCurrent amount: $currentAmountStr';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(message),
              const SizedBox(height: 16),
              Text(
                'Please choose a different payment method or adjust the transaction amount.',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  // Member interaction handlers
  void _handleMemberEntry() {
    // 清除缓存确保进入新增模式
    _memberCacheService.clearCache();
    context.push('/member/register');
  }

  void _handleMemberRemove() {
    _memberCacheService.clearCache();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Customer information removed'),
        backgroundColor: BPColors.success,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _handleMemberTap() {
    // 有会员信息时，直接进入编辑页面（页面会自动检测缓存）
    context.push('/member/register');
  }

  // 显示客户信息必填对话框
  void _showCustomerInfoRequiredDialog(PaymentMethod selectedMethod) {
    showDialog(
      context: context,
      barrierDismissible: false, // 防止点击外部关闭
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Row(
            children: <Widget>[
              Icon(
                Icons.person_add,
                color: BPColors.primary,
                size: 28,
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Customer Info Required',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                'To proceed with ${selectedMethod.displayName} payment, please add customer information first.',
                style: const TextStyle(
                  fontSize: 16,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: BPColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: BPColors.primary.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: const Row(
                  children: <Widget>[
                    Icon(
                      Icons.info_outline,
                      color: BPColors.primary,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Customer info is required for all payments to ensure proper transaction tracking.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF1D1D1F),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            // 取消按钮
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[600],
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: const Text(
                'Cancel',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            // 添加客户信息按钮
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 导航到客户信息添加页面
                _navigateToCustomerInfo(selectedMethod);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: BPColors.primary,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Add Customer Info',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // 导航到客户信息页面
  void _navigateToCustomerInfo(PaymentMethod selectedMethod) async {
    // 清除缓存确保进入新增模式，然后导航到会员注册页面
    _memberCacheService.clearCache();
    final Object? result = await context.push('/member/register');

    // 如果用户成功添加了客户信息，自动继续支付流程
    if (result == true && _memberCacheService.hasCachedMember) {
      // 延迟一点时间确保UI更新完成
      await Future.delayed(const Duration(milliseconds: 300));

      // 重新执行支付方式选择
      _handlePaymentMethodSelected(selectedMethod);

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Customer info added successfully. Proceeding with ${selectedMethod.displayName} payment.'),
            backgroundColor: BPColors.success,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// 检查并处理 B2B 客户的自动跳过逻辑
  void _checkAndHandleB2BCustomer() {
    // 检查是否有缓存的会员信息
    if (!_memberCacheService.hasCachedMember) {
      debugPrint('🔍 [B2B检查] 没有缓存的会员信息，继续正常流程');
      return;
    }

    final member = _memberCacheService.cachedMember;
    if (member == null) {
      debugPrint('🔍 [B2B检查] 缓存会员为空，继续正常流程');
      return;
    }

    // 使用 CustomerTypeUtils 检测客户类型
    final CustomerType customerType = CustomerTypeUtils.detectCustomerType(
      member.metadata,
      memberId: member.id
    );

    debugPrint('🔍 [B2B检查] 检测到客户类型: ${customerType.value}');
    debugPrint('   会员ID: ${member.id}');
    debugPrint('   会员姓名: ${member.name}');

    // 如果是 B2B 客户，自动跳过支付方式选择
    if (customerType == CustomerType.b2b) {
      debugPrint('🏢 [B2B自动跳过] 检测到B2B客户，自动跳过支付方式选择');
      _handleB2BAutoPayment();
    } else {
      debugPrint('🏠 [B2B检查] B2C客户，继续正常支付方式选择流程');
    }
  }

  /// 处理 B2B 客户的自动支付流程
  void _handleB2BAutoPayment() {
    if (widget.paymentData == null) {
      debugPrint('❌ [B2B自动支付] PaymentData 为空，无法继续');
      return;
    }

    debugPrint('🏢 [B2B自动支付] 开始B2B自动支付流程');

    // 创建 B2B 固定支付方式（使用 CASH 类型）
    final PaymentTransactionData b2bPaymentData = widget.paymentData!.copyWith(
      paymentMethodId: 999999, // B2B 专用支付方式ID
      paymentMethodName: 'B2B Payment',
      paymentMethodType: 'CASH', // B2B 使用 CASH 类型
      paymentMethodConfig: <String, dynamic>{
        'isB2BPayment': true,
        'description': 'B2B Business Payment',
      },
    );

    debugPrint('🏢 [B2B自动支付] 支付数据配置:');
    debugPrint('   支付方式ID: ${b2bPaymentData.paymentMethodId}');
    debugPrint('   支付方式名称: ${b2bPaymentData.paymentMethodName}');
    debugPrint('   支付方式类型: ${b2bPaymentData.paymentMethodType}');
    debugPrint('   交易金额: ${b2bPaymentData.totalAmount}');

    // 直接导航到 Cash Payment 页面
    context.push('/payment/cash', extra: b2bPaymentData);

    debugPrint('✅ [B2B自动支付] 已导航到 Cash Payment 页面');
  }

  @override
  Widget build(BuildContext context) {
    final bool isPaymentFlow = widget.paymentData != null;

    return PopScope(
      canPop: !isPaymentFlow,
      onPopInvoked: (bool didPop) {
        if (didPop) return;
        if (isPaymentFlow) {
          // 使用原生导航返回，而不是go_router
          Navigator.of(context).pop(null); // 返回null表示用户取消了支付
        } else {
          context.go('/');
        }
      },
      child: SafeScaffold(
        appBar: BPAppBar(
          title: 'Select Payment Method',
          showLogo: false,
          actions: <Widget>[
            // 调试模式下显示清除会员缓存按钮
            if (kDebugMode) ...<Widget>[
              IconButton(
                icon: Icon(
                  _memberCacheService.hasCachedMember
                      ? Icons.person
                      : Icons.person_outline,
                  color: BPColors.primary,
                ),
                onPressed: () {
                  if (_memberCacheService.hasCachedMember) {
                    _memberCacheService.clearCache();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Debug: Member cache cleared'),
                        backgroundColor: Colors.orange,
                        duration: Duration(seconds: 2),
                      ),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Debug: No member cache to clear'),
                        backgroundColor: Colors.grey,
                        duration: Duration(seconds: 2),
                      ),
                    );
                  }
                },
                tooltip: _memberCacheService.hasCachedMember
                    ? 'Debug: Clear member cache'
                    : 'Debug: No member cache',
              ),
            ],
          ],
          onBack: () {
            // 如果是支付流程，返回上一页，否则返回主页
            if (isPaymentFlow) {
              // 这里不能用pop，因为我们是通过replace来的，没有上一页
              // 应该返回到发起支付的页面
              Navigator.of(context).pop(null); // 返回null表示用户取消了支付
            } else {
              context.go('/');
            }
          },
        ),
        body: _buildBody(),
        bottomNavigationBar: MemberInfoBottomBar(
          onMemberEntry: _handleMemberEntry,
          onMemberRemove: _handleMemberRemove,
          onMemberTap: _handleMemberTap,
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: AppLoadingIndicator(loadingText: 'Loading payment methods...'),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadPaymentMethods,
              style: ElevatedButton.styleFrom(
                backgroundColor: BPColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return _buildPaymentMethodsList();
  }

  Widget _buildPaymentMethodsList() {
    // 直接从PaymentTransactionData获取交易金额
    final double totalAmount = widget.paymentData?.totalAmount ?? 0.0;
    final ThemeData theme = Theme.of(context);
    final TextTheme textTheme = theme.textTheme;

    // 格式化金额 - 不含小数
    final NumberFormat currencyFormatter = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    // 分离现金支付方式和其他支付方式
    final List<PaymentMethod> cashMethods = <PaymentMethod>[];
    final Map<String, List<PaymentMethod>> otherGroupedMethods =
        <String, List<PaymentMethod>>{};

    for (PaymentMethod method in _paymentMethods!) {
      if (method.type == 'CASH') {
        cashMethods.add(method);
      } else {
        final String groupKey = method.groupName ?? 'other';
        if (!otherGroupedMethods.containsKey(groupKey)) {
          otherGroupedMethods[groupKey] = <PaymentMethod>[];
        }
        otherGroupedMethods[groupKey]!.add(method);
      }
    }

    // 组名的显示名称映射 - 只合并"other"和"others"为"Others"
    final Map<String, String> groupDisplayNames = <String, String>{
      'physical': 'Physical Payment',
      'electronic': 'Electronic Payment',
      'mobile': 'Mobile Payment',
      'loyalty': 'Loyalty Payment',
      'other': 'Others',
      'others': 'Others',
    };

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          // 简化的交易金额显示
          Container(
            padding:
                const EdgeInsets.symmetric(vertical: 14.0, horizontal: 18.0),
            decoration: BoxDecoration(
              color: BPColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: BPColors.primary.withValues(alpha: 0.2),
                width: 1.5,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    const Icon(Icons.receipt_long,
                        color: BPColors.primary, size: 20),
                    const SizedBox(width: 10),
                    Text(
                      'Amount Due',
                      style: textTheme.titleSmall?.copyWith(
                        color: BPColors.primary,
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                Text(
                  currencyFormatter.format(totalAmount),
                  style: textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: BPColors.primary,
                    fontSize: 22,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),

          // 支付方式列表
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  // 现金支付方式（全宽度，无组标题）
                  if (cashMethods.isNotEmpty) ...<Widget>[
                    ...cashMethods.map(_buildFullWidthPaymentMethod),
                    const SizedBox(height: 6),
                  ],

                  // 其他支付方式组 - 按显示名称合并
                  ...() {
                    // 按显示名称重新分组
                    final Map<String, List<PaymentMethod>> displayGroupedMethods = <String, List<PaymentMethod>>{};
                    
                    for (final MapEntry<String, List<PaymentMethod>> entry in otherGroupedMethods.entries) {
                      final String groupName = entry.key;
                      final List<PaymentMethod> methods = entry.value;
                      final String displayName = groupDisplayNames[groupName] ?? groupName;
                      
                      if (!displayGroupedMethods.containsKey(displayName)) {
                        displayGroupedMethods[displayName] = <PaymentMethod>[];
                      }
                      displayGroupedMethods[displayName]!.addAll(methods);
                    }
                    
                    return displayGroupedMethods.entries.map((MapEntry<String, List<PaymentMethod>> entry) {
                      final String displayName = entry.key;
                      final List<PaymentMethod> methods = entry.value;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: <Widget>[
                          // 全宽度组标题
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 3),
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(5),
                            ),
                            child: Text(
                              displayName,
                              style: textTheme.labelMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Colors.grey[700],
                                fontSize: 12,
                              ),
                            ),
                          ),
                          const SizedBox(height: 5),
                          // 响应式网格布局显示支付方式
                          _buildResponsivePaymentGrid(methods),
                          const SizedBox(height: 6),
                        ],
                      );
                    });
                  }(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建响应式支付方式网格
  Widget _buildResponsivePaymentGrid(List<PaymentMethod> methods) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4, // 强制4列
        crossAxisSpacing: 6,
        mainAxisSpacing: 6,
        mainAxisExtent: 75, // 适当放大按钮高度
      ),
      itemCount: methods.length,
      itemBuilder: (BuildContext context, int methodIndex) {
        return _buildPaymentMethodItem(methods[methodIndex]);
      },
    );
  }

  /// 构建全宽度支付方式卡片（用于现金支付）
  Widget _buildFullWidthPaymentMethod(PaymentMethod method) {
    return Container(
      height: 68,
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
        border: Border.all(
          color: BPColors.primary.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: BPColors.primary.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _handlePaymentMethodSelected(method),
          borderRadius: BorderRadius.circular(16),
          splashColor: BPColors.primary.withOpacity(0.1),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: <Widget>[
                // 简洁的BP色彩点
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: BPColors.primary,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        method.displayName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF1D1D1F),
                          letterSpacing: 0.2,
                        ),
                      ),
                      if (method.description != null &&
                          method.description!.isNotEmpty) ...<Widget>[
                        const SizedBox(height: 2),
                        Text(
                          method.description!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: BPColors.primary,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentMethodItem(PaymentMethod method) {
    final double currentAmount = widget.paymentData?.totalAmount ?? 0.0;
    final bool isAmountValid = _isPaymentMethodAmountValid(method, currentAmount);
    
    // BP品牌风格设计 - 所有支付方式统一样式
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: <Color>[
            isAmountValid ? Colors.white : Colors.grey.shade200,
            isAmountValid ? Colors.grey.shade50 : Colors.grey.shade300,
            isAmountValid ? Colors.grey.shade100.withOpacity(0.8) : Colors.grey.shade400.withOpacity(0.8),
          ],
        ),
        border: Border.all(
          color: isAmountValid ? BPColors.primary.withOpacity(0.15) : Colors.grey.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: isAmountValid ? BPColors.primary.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _handlePaymentMethodSelected(method),
          borderRadius: BorderRadius.circular(16),
          splashColor: isAmountValid ? BPColors.primary.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                // BP风格顶部装饰线或警告指示器
                Container(
                  width: 28,
                  height: 2,
                  decoration: BoxDecoration(
                    gradient: isAmountValid 
                      ? const LinearGradient(
                          colors: <Color>[BPColors.primary, BPColors.secondary],
                        )
                      : LinearGradient(
                          colors: <Color>[Colors.grey.shade400, Colors.grey.shade500],
                        ),
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
                const SizedBox(height: 6),

                // 主要支付方式名称
                Expanded(
                  child: Center(
                    child: Text(
                      method.displayName,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w700,
                        color: isAmountValid ? const Color(0xFF1D1D1F) : Colors.grey.shade600,
                        height: 1.1,
                        letterSpacing: 0.1,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),

                // 金额限制或描述信息
                if (!isAmountValid && (method.minAmount != null || method.maxAmount != null)) ...<Widget>[
                  const SizedBox(height: 2),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: Text(
                      _getAmountLimitText(method),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 7,
                        fontWeight: FontWeight.w500,
                        color: Colors.red,
                        height: 1.0,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ] else if (method.description != null && method.description!.isNotEmpty) ...<Widget>[
                  const SizedBox(height: 2),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
                    decoration: BoxDecoration(
                      color: BPColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      method.description!,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 8,
                        fontWeight: FontWeight.w500,
                        color: BPColors.primary,
                        height: 1.1,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 检查支付方式的金额是否有效
  bool _isPaymentMethodAmountValid(PaymentMethod method, double amount) {
    if (method.minAmount != null && amount < method.minAmount!) {
      return false;
    }
    if (method.maxAmount != null && amount > method.maxAmount!) {
      return false;
    }
    return true;
  }

  /// 获取金额限制文本
  String _getAmountLimitText(PaymentMethod method) {
    final NumberFormat formatter = NumberFormat.currency(
      locale: 'id_ID',
      symbol: '',
      decimalDigits: 0,
    );
    
    if (method.minAmount != null && method.maxAmount != null) {
      return '${formatter.format(method.minAmount!)}-${formatter.format(method.maxAmount!)}';
    } else if (method.minAmount != null) {
      return 'Min ${formatter.format(method.minAmount!)}';
    } else if (method.maxAmount != null) {
      return 'Max ${formatter.format(method.maxAmount!)}';
    }
    return '';
  }

  /// 应用业务规则，为特定支付方式设置金额限制
  void _applyBusinessRules(List<PaymentMethod> methods) {
    for (int i = 0; i < methods.length; i++) {
      final PaymentMethod method = methods[i];
      PaymentMethod? updatedMethod;

      // 根据支付方式类型和名称应用业务规则
      if (method.type == 'BANK_CARD') {
        // 所有银行卡支付都有10,000印尼盾的最低限额（根据后端业务规则）
        if (method.minAmount == null || method.minAmount! < 10000.0) {
          updatedMethod = PaymentMethod(
            id: method.id,
            type: method.type,
            name: method.name,
            displayName: method.displayName,
            description: method.description,
            icon: method.icon,
            gatewayType: method.gatewayType,
            gatewayConfig: method.gatewayConfig,
            enabled: method.enabled,
            minAmount: 10000.0, // 统一设置银行卡最低金额限制为10,000印尼盾
            maxAmount: method.maxAmount,
            dailyLimit: method.dailyLimit,
            feeType: method.feeType,
            feeValue: method.feeValue,
            availableTime: method.availableTime,
            allowedStations: method.allowedStations,
            sortOrder: method.sortOrder,
            groupName: method.groupName,
            createdAt: method.createdAt,
            updatedAt: method.updatedAt,
            deletedAt: method.deletedAt,
          );
        }
      }

      // 如果需要更新，替换原来的方法
      if (updatedMethod != null) {
        methods[i] = updatedMethod;
        debugPrint('💳 应用业务规则 - ${method.displayName}: 最低金额设为 ${updatedMethod.minAmount}');
      }
    }
  }
}
