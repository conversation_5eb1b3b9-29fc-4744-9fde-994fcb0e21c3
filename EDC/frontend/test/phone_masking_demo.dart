import 'package:flutter_test/flutter_test.dart';
import 'package:edc_app/services/auto_print_service.dart';

void main() {
  group('Phone Number Masking Demo', () {
    test('demonstrate phone number masking examples', () {
      print('=== 电话号码脱敏演示 ===\n');
      
      // 测试用例集合
      final List<String> testPhones = [
        '081234567890',           // 标准印尼手机号
        '08123456789',            // 11位号码
        '0812345678901',          // 13位号码
        '+62-812-3456-7890',      // 国际格式
        '(081) 234-567-890',      // 带括号格式
        '081 234 567 890',        // 带空格格式
        '12345',                  // 5位号码
        '1234',                   // 4位号码
        '123',                    // 3位号码
        '',                       // 空号码
        'abc-def-ghi',           // 无数字字符
        '123abc456def789',       // 混合字符
      ];
      
      print('原始电话号码 → 脱敏后显示');
      print('─'.padRight(40, '─'));
      
      for (final String phone in testPhones) {
        final String masked = AutoPrintService.formatPhoneNumber(phone);
        final String displayPhone = phone.isEmpty ? '(空)' : phone;
        final String displayMasked = masked.isEmpty ? '(不显示)' : masked;
        
        print('${displayPhone.padRight(20)} → $displayMasked');
      }
      
      print('\n=== 小票打印效果示例 ===\n');
      
      // 模拟小票打印效果
      final List<Map<String, String>> customers = [
        {
          'name': 'John Doe',
          'phone': '081234567890',
          'vehicle': 'B1234XYZ'
        },
        {
          'name': 'Jane Smith',
          'phone': '08123456789',
          'vehicle': 'D5678ABC'
        },
        {
          'name': 'Bob Wilson',
          'phone': '+62-812-3456-7890',
          'vehicle': 'F9012DEF'
        },
        {
          'name': 'Alice Brown',
          'phone': '',
          'vehicle': 'G3456HIJ'
        },
        {
          'name': '',
          'phone': '081234567890',
          'vehicle': 'H7890KLM'
        },
      ];
      
      for (int i = 0; i < customers.length; i++) {
        final Map<String, String> customer = customers[i];
        final String name = customer['name']!;
        final String phone = customer['phone']!;
        final String vehicle = customer['vehicle']!;
        
        print('客户 ${i + 1}:');
        print('Invoice No: INV-2024-000${i + 1}');
        print('Date: 2024-01-15 10:3${i}:00');
        print('Pump No: ${i + 1}');
        
        // 按照新的输出顺序：名字 → 电话 → 车牌
        if (name.isNotEmpty) {
          print(name);
        }
        
        if (phone.isNotEmpty) {
          final String maskedPhone = AutoPrintService.formatPhoneNumber(phone);
          print('Telepon: $maskedPhone');
        }
        
        if (vehicle.isNotEmpty) {
          print('NomorKendaraan: $vehicle');
        }
        
        print('--------------------------------');
        print('');
      }
      
      print('=== 隐私保护说明 ===');
      print('✅ 电话号码前5位用*代替，保护客户隐私');
      print('✅ 只显示后5位数字，便于识别');
      print('✅ 自动处理各种格式的电话号码');
      print('✅ 空号码不显示，避免空行');
      print('✅ 符合数据保护法规要求');
      
      // 验证所有测试用例
      expect(AutoPrintService.formatPhoneNumber('081234567890'), equals('*****67890'));
      expect(AutoPrintService.formatPhoneNumber('08123456789'), equals('*****56789'));
      expect(AutoPrintService.formatPhoneNumber('+62-812-3456-7890'), equals('*****67890'));
      expect(AutoPrintService.formatPhoneNumber(''), equals(''));
      
      print('\n✅ 所有测试用例验证通过！');
    });
  });
} 