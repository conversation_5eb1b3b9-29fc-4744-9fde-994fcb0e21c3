# 客户类型字段标准化重构

## 概述

本文档描述了客户类型字段的标准化重构，旨在解决代码库中客户类型字段命名不一致、检测逻辑复杂等问题。

## 问题分析

### 重构前的问题

1. **字段命名不一致**：
   - 会员注册使用：`'customerType'` (驼峰命名)
   - B2B检测使用：`'customer_type'` 和 `'customerType'` (混用)
   - 会员数据服务错误使用：`'customer_type': cachedMember.levelDisplayName` (错误映射)

2. **缺乏标准化的枚举或常量**：
   - 硬编码字符串 `'B2B'` 和 `'B2C'`
   - 没有统一的客户类型定义

3. **检测逻辑复杂**：
   - 需要检查多个字段名
   - 代码重复且难以维护

## 解决方案

### 1. 创建统一的客户类型常量和工具类

**文件**: `lib/constants/customer_type_constants.dart`

#### 核心组件：

- **CustomerType 枚举**: 定义 B2B 和 B2C 两种客户类型
- **CustomerTypeFields 类**: 定义标准字段名和兼容字段名
- **CustomerTypeUtils 工具类**: 提供统一的检测和设置方法

#### 主要功能：

```dart
// 客户类型检测
CustomerType detectedType = CustomerTypeUtils.detectCustomerType(metadata, memberId: memberId);

// B2B检测
bool isB2B = CustomerTypeUtils.isB2BCustomer(metadata, memberId: memberId);

// 设置客户类型
CustomerTypeUtils.setCustomerType(metadata, CustomerType.b2b);
```

### 2. 更新会员注册页面

**文件**: `lib/screens/member/member_registration_page.dart`

#### 主要变更：

- 使用 `CustomerTypeUtils.getAllValues()` 获取客户类型选项
- 使用 `CustomerType.isB2B()` 进行B2B检测
- 使用 `CustomerTypeUtils.setCustomerType()` 设置metadata
- 使用 `CustomerTypeUtils.detectCustomerType()` 填充表单

### 3. 简化支付页面B2B检测逻辑

**文件**: `lib/screens/payment/cash_payment_page.dart`

#### 重构前：
```dart
final bool isB2BByType1 = memberMetadata['customer_type']?.toString().toUpperCase() == 'B2B';
final bool isB2BByType2 = memberMetadata['customerType']?.toString().toUpperCase() == 'B2B';
final bool isB2BByFlag = memberMetadata['is_b2b'] == true || memberMetadata['is_b2b']?.toString().toLowerCase() == 'true';
isB2BCustomer = isB2BByType1 || isB2BByType2 || isB2BByFlag;
```

#### 重构后：
```dart
final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(memberMetadata, memberId: cachedMember.id);
isB2BCustomer = detectedType == CustomerType.b2b;
```

### 4. 更新其他相关组件

- **会员信息底部栏**: 使用统一的检测逻辑
- **会员数据服务**: 修复错误的客户类型映射
- **营销折扣服务**: 简化B2B检测逻辑

## 向后兼容性

### 支持的字段名

重构后的检测逻辑支持以下字段名：

1. `customerType` (标准字段名，推荐使用)
2. `customer_type` (兼容旧字段名)
3. `is_b2b` (布尔标志字段)
4. 会员ID前缀检测 (`B2B_` 或 `B2C_`)

### 迁移策略

1. **新数据**: 使用标准字段名 `customerType`
2. **旧数据**: 自动检测并兼容多种字段名
3. **数据清理**: 设置新值时会清理旧字段名以避免冲突

## 使用指南

### 检测客户类型

```dart
import '../../constants/customer_type_constants.dart';

// 检测客户类型
CustomerType type = CustomerTypeUtils.detectCustomerType(metadata, memberId: memberId);

// 检查是否为B2B
bool isB2B = CustomerTypeUtils.isB2BCustomer(metadata, memberId: memberId);

// 检查是否为B2C
bool isB2C = CustomerTypeUtils.isB2CCustomer(metadata, memberId: memberId);
```

### 设置客户类型

```dart
// 设置为B2B客户
CustomerTypeUtils.setCustomerType(metadata, CustomerType.b2b);

// 设置为B2C客户
CustomerTypeUtils.setCustomerType(metadata, CustomerType.b2c);
```

### 获取显示文本

```dart
// 获取客户类型显示文本
String displayText = CustomerTypeUtils.getDisplayText(CustomerType.b2b);
// 返回: "Business Customer"

// 获取所有客户类型值
List<String> allTypes = CustomerTypeUtils.getAllValues();
// 返回: ["B2B", "B2C"]
```

## 测试验证

### 验证步骤

1. **会员注册**: 验证B2B和B2C客户注册功能
2. **客户类型检测**: 验证支付页面的B2B检测逻辑
3. **数据兼容性**: 验证旧数据的兼容性
4. **UI显示**: 验证会员信息显示的正确性

### 预期结果

- B2B客户正确识别并使用B2B支付类型
- B2C客户正确识别并使用原始支付类型
- 旧数据能够正确迁移和识别
- 新数据使用标准化字段名

## 影响的文件

### 新增文件
- `lib/constants/customer_type_constants.dart`

### 修改的文件
- `lib/screens/member/member_registration_page.dart`
- `lib/screens/payment/cash_payment_page.dart`
- `lib/widgets/member_info_bottom_bar.dart`
- `lib/services/shared/member_data_service.dart`
- `lib/services/marketing_discount_service.dart`

## 总结

通过这次重构，我们实现了：

1. **统一的字段命名**: 使用标准的 `customerType` 字段名
2. **简化的检测逻辑**: 单一方法替代复杂的多重检查
3. **类型安全**: 使用枚举替代硬编码字符串
4. **向后兼容**: 支持旧数据格式的自动检测
5. **可维护性**: 集中管理客户类型相关逻辑

这些改进将显著提高代码的可读性、可维护性和可靠性。
