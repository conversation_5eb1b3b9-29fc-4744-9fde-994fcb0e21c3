import 'dart:async';
import 'package:flutter/foundation.dart';

import '../models/fcc_device.dart';
import '../models/dispenser_model.dart';
import 'api/fcc_device_api.dart';
import 'adapters/fcc_device_adapter_v2.dart';

/// FCC Device Service - V2版本
///
/// 完全使用V2适配器的无硬编码依赖版本
/// 提供统一的FCC设备管理和EDC格式转换
class FCCDeviceService {
  FCCDeviceService({FCCDeviceApi? api}) : _api = api ?? FCCDeviceApi() {
    debugPrint('🔨 FCCDeviceService实例创建 (V2核心): $hashCode');
  }

  /// 工厂构造函数，使用指定的baseUrl创建实例
  factory FCCDeviceService.withBaseUrl(String baseUrl) {
    final FCCDeviceApi api = FCCDeviceApi(baseUrl: baseUrl);
    return FCCDeviceService(api: api);
  }

  final FCCDeviceApi _api;

  // === Device Discovery and Management ===

  /// Get all FCC devices and convert to EDC Dispensers
  Future<List<Dispenser>> getDispensers({
    String? stationId,
    String? islandId,
  }) async {
    try {
      final List<FCCDevice> fccDevices = await _api.getDevices(
        stationId: stationId,
        islandId: islandId,
      );

      // Query nozzles for devices that need them
      final List<FCCDevice> devicesWithNozzles = <FCCDevice>[];
      for (final FCCDevice device in fccDevices) {
        if (device.isPump && device.nozzles.isEmpty) {
          try {
            final List<FCCNozzle> nozzles =
                await _api.getDeviceNozzles(device.id);
            final FCCDevice deviceWithNozzles =
                device.copyWith(nozzles: nozzles);
            devicesWithNozzles.add(deviceWithNozzles);
          } catch (e) {
            devicesWithNozzles.add(device);
          }
        } else {
          devicesWithNozzles.add(device);
        }
      }

      // Filter valid devices and convert to dispensers using V2
      final List<FCCDevice> validDevices = devicesWithNozzles
          .where(FCCDeviceAdapterV2.isValidForMapping)
          .toList();

      return FCCDeviceAdapterV2.fccDevicesToDispensers(validDevices);
    } catch (e) {
      debugPrint('❌ FCCDeviceService: Error getting dispensers: $e');
      rethrow;
    }
  }

  /// Get device nozzles with real-time status
  Future<List<FCCNozzle>> getDeviceNozzles(String deviceId) async {
    try {
      return await _api.getDeviceNozzles(deviceId);
    } catch (e) {
      debugPrint(
          '❌ FCCDeviceService: Error getting nozzles for device $deviceId: $e');
      rethrow;
    }
  }

  /// Get raw FCC devices for polling service
  /// Returns the original FCC device objects without EDC conversion
  Future<List<FCCDevice>> getFccDevices({
    String? stationId,
    String? islandId,
  }) async {
    try {
      final List<FCCDevice> fccDevices = await _api.getDevices(
        stationId: stationId,
        islandId: islandId,
      );

      // Query nozzles for devices that need them
      final List<FCCDevice> devicesWithNozzles = <FCCDevice>[];
      for (final FCCDevice device in fccDevices) {
        if (device.isPump && device.nozzles.isEmpty) {
          try {
            final List<FCCNozzle> nozzles =
                await _api.getDeviceNozzles(device.id);
            final FCCDevice deviceWithNozzles =
                device.copyWith(nozzles: nozzles);
            devicesWithNozzles.add(deviceWithNozzles);
          } catch (e) {
            devicesWithNozzles.add(device);
          }
        } else {
          devicesWithNozzles.add(device);
        }
      }

      // Return only valid devices
      return devicesWithNozzles
          .where(FCCDeviceAdapterV2.isValidForMapping)
          .toList();
    } catch (e) {
      debugPrint('❌ FCCDeviceService: Error getting FCC devices: $e');
      rethrow;
    }
  }

  /// Test connection to FCC service
  Future<bool> testConnection() async {
    try {
      debugPrint('🔌 FCCDeviceService: Testing connection...');

      final Map<String, dynamic> health = await _api.getDeviceHealth();
      final bool isHealthy = health['status'] == 'healthy';

      debugPrint(isHealthy
          ? '✅ FCCDeviceService: Connection test successful'
          : '⚠️  FCCDeviceService: Connection test warning: ${health['status']}');

      return isHealthy;
    } catch (e) {
      debugPrint('❌ FCCDeviceService: Connection test failed: $e');
      return false;
    }
  }

  // === V2 Nozzle Operations ===

  /// Authorize EDC nozzle using V2 adapter for mapping
  Future<void> authorizeNozzle(
    String nozzleId, {
    required String mode, // 'amount', 'volume', 'full'
    double? value,
    String? employeeId,
  }) async {
    try {
      // 使用V2适配器的反向查找功能
      final FCCNozzle? fccInfo =
          FCCDeviceAdapterV2.getFccNozzleByEdcId(nozzleId);

      if (fccInfo == null) {
        throw Exception(
            'No FCC mapping found for EDC nozzle $nozzleId in V2 adapter');
      }

      final String deviceId = fccInfo.deviceId;
      final int nozzleNumber = fccInfo.number;

      final String employeeIdToUse =
          employeeId ?? 'EDC-AUTO-${DateTime.now().millisecondsSinceEpoch}';

      debugPrint(
          '预设+授权: EDC Nozzle $nozzleId → FCC $deviceId:$nozzleNumber');

      // Set preset if needed
      if (value != null && value > 0 && mode != 'full') {
        await _api.presetNozzle(
          deviceId,
          nozzleId,
          presetType: mode,
          presetValue: value,
          employeeId: employeeIdToUse,
        );
      }


    } catch (e) {
      debugPrint(
          '❌ FCCDeviceService: Error authorizing nozzle $nozzleId: $e');
      rethrow;
    }
  }

  // === Device Operations ===

  /// Reset FCC device or specific nozzle
  Future<bool> resetDevice(
    String deviceId, {
    int? nozzleNumber,
    String? employeeId,
  }) async {
    try {
      debugPrint(
          '🔄 FCCDeviceService: Resetting device $deviceId${nozzleNumber != null ? ' nozzle $nozzleNumber' : ''}');

      final Map<String, dynamic> result = await _api.resetDevice(
        deviceId,
        nozzleNumber: nozzleNumber,
        employeeId:
            employeeId ?? 'EDC-RESET-${DateTime.now().millisecondsSinceEpoch}',
      );

      debugPrint('✅ FCCDeviceService: Device reset successful: $result');
      return true;
    } catch (e) {
      debugPrint('❌ FCCDeviceService: Error resetting device $deviceId: $e');
      return false;
    }
  }

  /// Reset EDC nozzle using V2 adapter mapping
  Future<bool> resetNozzle(String edcNozzleId, {String? employeeId}) async {
    try {
      debugPrint('🔄 FCCDeviceService: 开始重置 EDC Nozzle $edcNozzleId (V2)');

      // 使用V2适配器的反向查找
      final FCCNozzle? fccInfo =
          FCCDeviceAdapterV2.getFccNozzleByEdcId(edcNozzleId);

      if (fccInfo == null) {
        debugPrint('❌ FCCDeviceService: V2适配器中未找到 EDC nozzle $edcNozzleId 的映射');
        debugPrint('   可用的映射统计: ${FCCDeviceAdapterV2.getMappingStatistics()}');
        return false;
      }

      final String deviceId = fccInfo.deviceId;
      final int nozzleNumber = fccInfo.number;

      debugPrint(
          '✅ FCCDeviceService: V2映射成功 - EDC:$edcNozzleId → FCC:$deviceId:$nozzleNumber');

      return await resetDevice(
        deviceId,
        nozzleNumber: nozzleNumber,
        employeeId: employeeId,
      );
    } catch (e) {
      debugPrint('❌ FCCDeviceService: 重置异常: $e');
      return false;
    }
  }

  /// Cancel preauth for EDC nozzle using V2 adapter mapping
  Future<void> cancelNozzlePreauth(String edcNozzleId, {String? employeeId}) async {
    try {
      debugPrint('🔄 FCCDeviceService: 开始取消预授权 EDC Nozzle $edcNozzleId (V2)');

      // 使用V2适配器的反向查找
      final FCCNozzle? fccInfo =
          FCCDeviceAdapterV2.findFCCNozzleByEDCId(edcNozzleId);

      if (fccInfo == null) {
        throw Exception('无法找到EDC Nozzle $edcNozzleId 对应的FCC设备信息');
      }

      final String deviceId = fccInfo.deviceId;
      final String nozzleId = fccInfo.id;

      debugPrint(
          '✅ FCCDeviceService: V2映射成功 - EDC:$edcNozzleId → FCC:$deviceId:$nozzleId');

      await _api.cancelPreauth(
        deviceId,
        nozzleId,
        employeeId: employeeId ?? 'EDC-CANCEL-${DateTime.now().millisecondsSinceEpoch}',
        reason: 'User cancelled from EDC interface',
      );

      debugPrint('✅ FCCDeviceService: 预授权取消成功');
    } catch (e) {
      debugPrint('❌ FCCDeviceService: Error cancelling preauth for nozzle $edcNozzleId: $e');
      rethrow;
    }
  }

  /// Cleanup resources
  void dispose() {
    _api.dispose();
  }
}
