import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:edc_app/services/log_service.dart';
import 'dart:io';

void main() {
  group('LogService Tests', () {
    late LogService logService;

    setUp(() {
      logService = LogService.instance;
    });

    test('LogService should be a singleton', () {
      final instance1 = LogService.instance;
      final instance2 = LogService.instance;
      expect(identical(instance1, instance2), true);
    });

    test('LogService should initialize successfully', () async {
      await logService.initialize();
      expect(logService.logFilePath, isNotNull);
    });

    test('LogService should record different log levels', () async {
      await logService.initialize();
      
      // Test different log levels
      logDebug('Test', 'Debug message');
      logInfo('Test', 'Info message');
      logWarning('Test', 'Warning message');
      logError('Test', 'Error message');
      
      // Wait a bit for async operations
      await Future.delayed(const Duration(milliseconds: 100));
      
      // Check if log file exists and has content
      final logFileSize = await logService.getLogFileSize();
      expect(logFileSize, greaterThan(0));
    });

    test('LogService should handle API logs', () async {
      await logService.initialize();
      
      logService.apiRequest('GET', '/test', {'param': 'value'});
      logService.apiResponse('GET', '/test', 200, {'data': 'response'});
      logService.apiError('GET', '/test', Exception('Test error'));
      
      await Future.delayed(const Duration(milliseconds: 100));
      
      final logFileSize = await logService.getLogFileSize();
      expect(logFileSize, greaterThan(0));
    });

    test('LogService should handle business logs', () async {
      await logService.initialize();
      
      logService.transaction('CREATE', 'TX123', 'Transaction created');
      logService.printer('PRINT', 'Receipt printed');
      logService.device('Scanner', 'SCAN', 'QR code scanned');
      
      await Future.delayed(const Duration(milliseconds: 100));
      
      final logFileSize = await logService.getLogFileSize();
      expect(logFileSize, greaterThan(0));
    });

    test('LogService should retrieve recent logs', () async {
      await logService.initialize();
      
      // Add some test logs
      logInfo('Test', 'Test log 1');
      logInfo('Test', 'Test log 2');
      logInfo('Test', 'Test log 3');
      
      await Future.delayed(const Duration(milliseconds: 100));
      
      final recentLogs = await logService.getRecentLogs(lines: 10);
      expect(recentLogs, isNotEmpty);
      expect(recentLogs, contains('Test log 1'));
      expect(recentLogs, contains('Test log 2'));
      expect(recentLogs, contains('Test log 3'));
    });

    test('LogService should clear logs', () async {
      await logService.initialize();
      
      // Add some logs
      logInfo('Test', 'Before clear');
      await Future.delayed(const Duration(milliseconds: 100));
      
      final sizeBefore = await logService.getLogFileSize();
      expect(sizeBefore, greaterThan(0));
      
      // Clear logs
      await logService.clearLogs();
      await Future.delayed(const Duration(milliseconds: 100));
      
      final sizeAfter = await logService.getLogFileSize();
      expect(sizeAfter, equals(0));
    });

    test('debugPrint override should work', () async {
      await logService.initialize();
      
      // Test that debugPrint calls are captured
      debugPrint('Test debugPrint message');
      
      await Future.delayed(const Duration(milliseconds: 100));
      
      final recentLogs = await logService.getRecentLogs(lines: 5);
      expect(recentLogs, contains('Test debugPrint message'));
    });

    tearDown(() async {
      // Clean up after each test
      if (logService.logFilePath != null) {
        try {
          await logService.clearLogs();
        } catch (e) {
          // Ignore cleanup errors
        }
      }
    });
  });

  group('DebugPrint Override Tests', () {
    test('debugPrint should still work in tests', () async {
      await LogService.instance.initialize();
      
      // This should not throw an error
      expect(() => debugPrint('Test message'), returnsNormally);
    });

    test('debugPrint with wrapWidth should work', () async {
      await LogService.instance.initialize();
      
      // This should not throw an error
      expect(() => debugPrint('Long test message that might need wrapping', wrapWidth: 20), returnsNormally);
    });
  });
} 