import 'package:edc_app/services/api/order_api.dart';
import 'package:edc_app/services/fcc_device_service.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Import API providers
import '../../models/create_order_request.dart';
import '../../models/payment_transaction_data.dart';
import '../../models/auth_models.dart';
import '../../services/api/api_service.dart';
import '../../services/auth_providers.dart';
// Import promotion components

import '../../models/fuel_transaction.dart';
import '../../models/order.dart';
import '../../models/member_model.dart';
import '../../models/promotion_request.dart';
import '../../models/promotion_response.dart';
import '../../services/shared/erp_data_service.dart';
import '../../services/shared/member_data_service.dart';
import '../../services/member_cache_service.dart';
import '../../services/auto_print_service.dart';
import '../../widgets/bp_app_bar.dart';
import '../../constants/bp_colors.dart';
import '../../constants/customer_type_constants.dart';
import '../../controllers/fcc_status_controller.dart';
import '../../widgets/order_detail_widget.dart';
import '../../widgets/enhanced_payment_summary.dart';
import '../../widgets/cash_calculator_dialog.dart';
import '../../widgets/tera_password_dialog.dart';
import '../../theme/app_theme.dart';

class CashPaymentPage extends ConsumerStatefulWidget {
  const CashPaymentPage({super.key, this.paymentData});
  final PaymentTransactionData? paymentData;

  @override
  ConsumerState<CashPaymentPage> createState() => _CashPaymentPageState();
}

class _CashPaymentPageState extends ConsumerState<CashPaymentPage> {
  final AutoPrintService _autoPrintService = AutoPrintService();
  PromotionResponse? _promotionResponse;
  bool _isLoadingPromotion = false;

  /// Safe amount rounding method (handles decimal precision issues)
  /// Uses rounding to cents (Indonesian Rupiah smallest unit)
  static double _roundAmount(double amount) {
    // Indonesian Rupiah is usually accurate to the unit, but for safety, keep 2 decimal places
    return double.parse(amount.toStringAsFixed(2));
  }

  /// Safe number parsing method
  static double? _safeParseDouble(dynamic value) {
    if (value == null) return null;

    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }

    // If it's another type, try to convert to num then to double
    try {
      if (value is num) {
        return value.toDouble();
      }
    } catch (e) {
      // Conversion failed, return null
      return null;
    }

    return null;
  }

  /// Safe integer parsing method
  static int? _safeParseInt(dynamic value) {
    if (value == null) return null;

    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value);
    }

    // If it's another type, try to convert to num then to int
    try {
      if (value is num) {
        return value.toInt();
      }
    } catch (e) {
      // Conversion failed, return null
      return null;
    }

    return null;
  }

  @override
  void initState() {
    super.initState();
    _loadPromotionData();
  }

  /// Load promotion data using the new API
  Future<void> _loadPromotionData() async {
    if (widget.paymentData == null) return;

    // Tera 不享受优惠
    if (widget.paymentData?.paymentMethodName?.toUpperCase() == 'TERA') {
      return;
    }

    // B2B客户跳过优惠计算
    if (_checkIfB2BCustomer()) {
      debugPrint('🏢 B2B客户，跳过优惠计算');
      return;
    }

    setState(() {
      _isLoadingPromotion = true;
    });



    try {
      // 获取会员信息以确定车辆类型
      final Member? cachedMember = memberCacheService.cachedMember;
      String vehicleType = 'CAR'; // 默认车辆类型
      
      if (cachedMember != null) {
        // 从会员注册页面获取的车辆类型，并转换为API所需的格式
        final String? memberVehicleType = cachedMember.metadata['vehicle'] as String?;
        if (memberVehicleType != null && memberVehicleType.isNotEmpty) {
          // 将会员注册页面的车辆类型转换为API所需的格式
          switch (memberVehicleType) {
            case 'Motorbike':
              vehicleType = 'MOTORBIKE';
              break;
            case 'Car':
              vehicleType = 'CAR';
              break;
            case 'Truck':
              vehicleType = 'TRUCK';
              break;
            default:
              vehicleType = 'CAR'; // 默认为汽车
          }
          debugPrint('🚗 会员车辆类型: $memberVehicleType -> API格式: $vehicleType');
        }
      }

      // Create promotion request from payment data
      final PromotionRequest request = PromotionRequest(
        orderId: widget.paymentData!.transactionId,
        userId: widget.paymentData!.memberInfo?.id.toString() ?? 'anonymous',
        orderAmount: widget.paymentData!.totalAmount.toInt(), // 印尼盾原始金额，不需要转换
        orderTime: DateTime.now().toUtc(),
        vehicleType: vehicleType, // 使用从会员信息获取的车辆类型
        items: [
          OrderItem(
            itemId: widget.paymentData!.fuelType, // 使用 fuelType（油品 ID）而不是 fuelGrade（油品名称）
            name: 'Fuel',
            category: widget.paymentData!.fuelType, // 使用 fuelType（油品 ID）而不是 fuelGrade（油品名称）
            categoryIds: [widget.paymentData!.fuelType, 'fuel', vehicleType], // 使用 fuelType（油品 ID）
            price: widget.paymentData!.unitPrice.toInt(), // 印尼盾单价，不需要转换
            quantity: widget.paymentData!.volume.toInt(), // 升数，不需要转换为毫升
            attributes: {
              'fuel_volume': widget.paymentData!.volume,
            },
          ),
        ],
      );

      debugPrint('🎁 Calling promotion API with request: ${request.toJson()}');

      // Call the new promotion API using the correct method
      // 直接使用构造好的 PromotionRequest 对象，避免重新构造导致参数丢失
      final response = await ApiService().promotionApi.calculateFuelDiscountFromRequest(request);

      final promotionResponse = response;
      
      if (!mounted) return;
      setState(() {
        _promotionResponse = promotionResponse;
        _isLoadingPromotion = false;
      });

      debugPrint('✅ Promotion calculation successful');
      debugPrint('   Original Amount: ${promotionResponse.originalAmount}');
      debugPrint('   Discounted Amount: ${promotionResponse.discountedAmount}');
      debugPrint('   Discount Amount: ${promotionResponse.discountAmount}');
      debugPrint('   Applied Promotions: ${promotionResponse.appliedPromotions.length}');

    } catch (e) {
      debugPrint('❌ Failed to load promotion data: $e');
      if (!mounted) return;
      setState(() {
        _isLoadingPromotion = false;
      });
    }
  }

  /// Asynchronously reset FCC device status
  /// Ensures device is in clean state after transaction completion
  Future<void> _resetFccNozzle() async {
    try {
      if (widget.paymentData?.nozzleId == null) {
        debugPrint('⚠️ Cash Payment: Unable to reset FCC device - nozzleId is null');
        return;
      }

      final String nozzleId = widget.paymentData!.nozzleId;
      debugPrint('🎯 Cash Payment: Starting FCC device status reset');
      debugPrint('   Target Nozzle: ID=$nozzleId');
      debugPrint('   PumpId: ${widget.paymentData!.pumpId}');
      debugPrint('   Transaction: ${widget.paymentData!.transactionId}');

      final FCCDeviceService fccDeviceService =
          ref.read(fccDeviceServiceProvider);
      final bool success = await fccDeviceService.resetNozzle(
        nozzleId,
        employeeId: 'EDC-CASH-RESET-${DateTime.now().millisecondsSinceEpoch}',
      );

      if (success) {
        debugPrint('✅ FCC device reset successful (cash payment)');
      } else {
        debugPrint('⚠️ FCC device reset failed, but does not affect payment process (cash payment)');
        debugPrint('   Possible causes: device mapping issues, network issues, or device offline');
      }
    } catch (e) {
      debugPrint('❌ FCC device reset exception (cash payment): $e');
      debugPrint('   Exception type: ${e.runtimeType}');
      // Don't block payment process, reset failure doesn't affect user experience
    }
  }

  // Confirm payment logic
  void _confirmPayment() async {
    debugPrint('🚀 =================================');
    debugPrint('🚀 _confirmPayment 方法被调用了！');
    debugPrint('🚀 =================================');

    // If it's Tera payment, password verification is required first
    if (widget.paymentData?.paymentMethodName?.toUpperCase() == 'TERA') {
      final bool isVerified = await showTeraPasswordDialog(context);
      if (!isVerified) {
        // User cancelled verification or verification failed
        return;
      }
    }
    
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(BPColors.primary),
          ),
        );
      },
    );

    try {
      // Get necessary data from PaymentTransactionData to build order request
      final String fuelTransactionId = widget.paymentData?.transactionId ?? '';
      final int stationId = widget.paymentData?.stationId ?? 0;
      final int? paymentMethodId = widget.paymentData?.paymentMethodId;

      // Print Json format transaction data
      debugPrint('Payment data: ${widget.paymentData?.toJson()}');

      if (fuelTransactionId == '' || stationId <= 0) {
        // Close loading indicator
        if (context.mounted) Navigator.pop(context);

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Transaction data is incomplete, unable to create order!'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
        return;
      }

      // Get employee information directly from SharedPreferences
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String? userId = prefs.getString('user_id');
      final String? currentEmployeeNo = prefs.getString('employee_no');

      // Use fuel transaction's employee info instead of current logged-in user
      final String? transactionEmployeeId = widget.paymentData?.employeeId;
      final String? transactionEmployeeNo = widget.paymentData?.employeeNo;
      
      // Determine which employee_no to use for the order
      // Priority: transaction's employeeNo > transaction's employeeId > current user's employeeNo
      final String? employeeNo = transactionEmployeeNo?.isNotEmpty == true 
          ? transactionEmployeeNo
          : (transactionEmployeeId?.isNotEmpty == true 
              ? transactionEmployeeId 
              : currentEmployeeNo);

      // Safely handle employee ID, ensure correct type
      debugPrint('🔍 Employee information check:');
      debugPrint('   Current userId: $userId (${userId.runtimeType})');
      debugPrint('   Current employeeNo: $currentEmployeeNo');
      debugPrint('   Transaction employeeId: $transactionEmployeeId');
      debugPrint('   Transaction employeeNo: $transactionEmployeeNo');
      debugPrint('   Final employeeNo for order: $employeeNo');

      // Get current user from auth provider (cached)
      final AuthUser? currentUser = ref.read(currentUserProvider);

      // Get FCC status controller for nozzle information
      final FccStatusController fccStatusController = ref.read(fccStatusControllerProvider.notifier);

      // Get member data with cache priority
      debugPrint('🔍 开始获取会员数据...');
      debugPrint('   PaymentData中的memberInfo: ${widget.paymentData?.memberInfo?.toJson()}');
      debugPrint('   PaymentData中的metadata: ${widget.paymentData?.metadata}');

      final Map<String, dynamic> memberData =
          await getMemberDataWithCachePriority(widget.paymentData?.toJson());
      debugPrint('💳 获取到的会员数据: $memberData');
      debugPrint('   会员数据是否为空: ${memberData.isEmpty}');
      debugPrint('   会员数据来源: ${memberData['is_from_cache'] == true ? '缓存' : 'API'}');

      // Get cached member information for consumption
      final Member? cachedMember;
      // Tera 不命中会员缓存
      if (widget.paymentData?.paymentMethodName?.toUpperCase() == 'TERA') {
        cachedMember = null;
      } else {
        cachedMember = memberCacheService.cachedMember;
      }

      // Get ERP system required redundant data
      final Map<String, dynamic> erpData =
          await getErpData(widget.paymentData?.toJson(), currentUser, cachedMember, fccStatusController);
      Map<String, dynamic> memberInfo = <String, dynamic>{};

      if (cachedMember != null) {
        // Use cached member information
        final CustomerType customerType = CustomerTypeUtils.detectCustomerType(cachedMember.metadata, memberId: cachedMember.id);
        memberInfo = <String, dynamic>{
          'member_id': cachedMember.id,
          'name': cachedMember.name,
          'phone': cachedMember.phone,
          'level': cachedMember.levelDisplayName,
          'vehicle_plate': cachedMember.plateNumbers.isNotEmpty
              ? cachedMember.plateNumbers.first
              : '',
          'vehicle_type': cachedMember.metadata['vehicleType'] ??
              cachedMember.metadata['vehicle'] ??
              '',
          'points_balance': cachedMember.points,
          'membership_level': cachedMember.levelDisplayName,
          'balance': cachedMember.balance,
          'customerType': customerType.value, // 添加客户类型字段
        };
        debugPrint('💾 使用缓存的会员信息构建memberInfo: ${cachedMember.name} (${cachedMember.phone})');
        debugPrint('   会员ID: ${memberInfo['member_id']}');
        debugPrint('   会员姓名: ${memberInfo['name']}');
        debugPrint('   会员手机: ${memberInfo['phone']}');
        debugPrint('   车辆类型: ${memberInfo['vehicle_type']}');
        debugPrint('   车牌号: ${memberInfo['vehicle_plate']}');
        debugPrint('   会员等级: ${memberInfo['membership_level']}');
        debugPrint('   积分余额: ${memberInfo['points_balance']}');
        debugPrint('   账户余额: ${memberInfo['balance']}');
      } else if (memberData.isNotEmpty) {
        // Use member data from API as fallback
        memberInfo = memberData;
        debugPrint('📡 使用API获取的会员信息作为备选');
        debugPrint('   API会员数据: $memberInfo');
      }

      // 最终memberInfo状态检查
      debugPrint('📋 ===== 最终memberInfo状态检查 =====');
      debugPrint('   memberInfo是否为空: ${memberInfo.isEmpty}');
      debugPrint('   memberInfo内容: $memberInfo');
      if (memberInfo.isEmpty) {
        debugPrint('❌ 警告: memberInfo为空，可能影响B2B检测和订单创建');
      } else {
        debugPrint('✅ memberInfo已准备就绪，包含${memberInfo.length}个字段');
      }
      debugPrint('📋 即将进入B2B检测流程...');
      debugPrint('📋 ===== memberInfo状态检查完成 =====');

      debugPrint('💳 Payment method information:');
      debugPrint('   Payment method name: ${widget.paymentData?.paymentMethodName}');
      debugPrint('   Payment method ID: $paymentMethodId');
      debugPrint('💰 Order creation will use original amount only: ${widget.paymentData?.totalAmount} (let order service calculate promotions)');

      // Prepare customer name - use actual member name, not vehicle plate
      String? customerNameForOrder;
      if (memberInfo.isNotEmpty && memberInfo['name'] != null && memberInfo['name'].toString().isNotEmpty) {
        final String memberName = memberInfo['name'].toString();
        // Skip auto-generated names like "Customer B1234ABC" or "Business Customer B1234ABC"
        if (!memberName.startsWith('Customer ') && !memberName.startsWith('Business Customer ')) {
          customerNameForOrder = memberName;
          debugPrint('✅ Using member name as customer name: $customerNameForOrder');
        } else {
          customerNameForOrder = null;
          debugPrint('⚠️ Member name is auto-generated, setting customer name to null');
        }
      } else if (widget.paymentData?.memberInfo?.name != null) {
        final String memberName = widget.paymentData!.memberInfo!.name;
        if (!memberName.startsWith('Customer ') && !memberName.startsWith('Business Customer ')) {
          customerNameForOrder = memberName;
          debugPrint('✅ Using PaymentData member name as customer name: $customerNameForOrder');
        } else {
          customerNameForOrder = null;
          debugPrint('⚠️ PaymentData member name is auto-generated, setting customer name to null');
        }
      } else {
        customerNameForOrder = null;
        debugPrint('⚠️ No valid member name available, customer name will be null');
      }

      // Extract customer phone from member info
      String? customerPhone;
      if (memberInfo.isNotEmpty && memberInfo['phone'] != null) {
        final String phoneFromMember = memberInfo['phone'].toString();
        // 过滤掉默认的手机号
        if (phoneFromMember.isNotEmpty && phoneFromMember != '0000000000') {
          customerPhone = phoneFromMember;
        }
      } else if (widget.paymentData?.memberInfo?.phone != null) {
        final String phoneFromPaymentData = widget.paymentData!.memberInfo!.phone;
        // 过滤掉默认的手机号
        if (phoneFromPaymentData.isNotEmpty && phoneFromPaymentData != '0000000000') {
          customerPhone = phoneFromPaymentData;
        }
      }

      // 根据支付方式设置客户信息
      final bool isTeraPayment = widget.paymentData?.paymentMethodName?.toUpperCase() == 'TERA';

      if (isTeraPayment) {
        // TERA 支付方式：无论是否有客户信息，都清空并设置为默认值
        customerNameForOrder = 'TERA';
        customerPhone = '0202020202';
        debugPrint('✅ TERA支付：清空客户信息并设置默认值 - 姓名: ANONIM, 手机: 1010101010');
      } else {
        // 其他支付方式：只有当客户姓名和手机号都为空时才设置默认值
        if ((customerNameForOrder == null || customerNameForOrder.trim().isEmpty) &&
            (customerPhone == null || customerPhone.trim().isEmpty)) {
          customerNameForOrder = 'ANONIM';
          customerPhone = '1010101010';
          debugPrint('✅ 其他支付：设置默认客户信息 - 姓名: ANONIM, 手机: 1010101010');
        }
      }

      // Extract vehicle type and convert to API format
      String? vehicleType;
      if (memberInfo.isNotEmpty && memberInfo['vehicle_type'] != null) {
        final String rawVehicleType = memberInfo['vehicle_type'].toString();
        // Convert internal format to API format
        switch (rawVehicleType.toUpperCase()) {
          case 'MOTORBIKE':
          case 'MOTORCYCLE':
            vehicleType = 'Motorbike';
            break;
          case 'CAR':
            vehicleType = 'Car';
            break;
          case 'TRUCK':
            vehicleType = 'Truck';
            break;
          case 'BUS':
            vehicleType = 'Bus';
            break;
          case 'VAN':
            vehicleType = 'Van';
            break;
          case 'SUV':
            vehicleType = 'SUV';
            break;
          default:
            vehicleType = rawVehicleType; // Use as-is if not recognized
        }
      } else if (widget.paymentData?.metadata?['member_vehicle_original'] != null) {
        vehicleType = widget.paymentData!.metadata!['member_vehicle_original'].toString();
      }

      // Extract license plate from member info
      String? licensePlate;
      if (memberInfo.isNotEmpty && memberInfo['vehicle_plate'] != null) {
        final plateData = memberInfo['vehicle_plate'];
        if (plateData is List && plateData.isNotEmpty) {
          licensePlate = plateData.first.toString();
        } else if (plateData is String && plateData.isNotEmpty) {
          licensePlate = plateData;
        }
      } else if (widget.paymentData?.memberInfo?.plateNumbers.isNotEmpty == true) {
        licensePlate = widget.paymentData!.memberInfo!.plateNumbers.first;
      } else if (widget.paymentData?.metadata?['vehicle_plate'] != null) {
        final plateData = widget.paymentData!.metadata!['vehicle_plate'];
        if (plateData is List && plateData.isNotEmpty) {
          licensePlate = plateData.first.toString();
        } else if (plateData is String && plateData.isNotEmpty) {
          licensePlate = plateData;
        }
      }

      // 检查是否为B2B客户，确定支付类型
      bool isB2BCustomer = _checkIfB2BCustomer();

      // 构建会员信息 (memberInfo 已在上面定义)
      if (cachedMember != null && memberInfo.isEmpty) {
        debugPrint('💾 找到缓存会员信息，开始详细检测:');
        debugPrint('   会员ID: ${cachedMember.id}');
        debugPrint('   会员姓名: ${cachedMember.name}');

        final Map<String, dynamic> memberMetadata = cachedMember.metadata;
        debugPrint('📋 会员完整metadata信息:');
        memberMetadata.forEach((key, value) {
          debugPrint('   $key: $value (${value.runtimeType})');
        });
        // 使用统一的客户类型检测工具
        final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(memberMetadata, memberId: cachedMember.id);
        isB2BCustomer = detectedType == CustomerType.b2b;

        debugPrint('🔍 客户类型检测结果:');
        debugPrint('   检测到的客户类型: ${detectedType.value}');
        debugPrint('   是否为B2B客户: $isB2BCustomer');
        debugPrint('   检测依据: 使用CustomerTypeUtils.detectCustomerType方法');

        if (isB2BCustomer) {
          debugPrint('🏢 ✅ 确认检测到B2B客户:');
          debugPrint('   └─ 会员ID: ${cachedMember.id}');
          debugPrint('   └─ 会员姓名: ${cachedMember.name}');
          debugPrint('   └─ 客户类型: ${detectedType.value}');
        } else {
          debugPrint('� 检测到B2C客户:');
          debugPrint('   └─ 会员ID: ${cachedMember.id}');
          debugPrint('   └─ 会员姓名: ${cachedMember.name}');
          debugPrint('   └─ 客户类型: ${detectedType.value}');
        }
      } else if (memberInfo.isNotEmpty) {
        debugPrint('📡 使用memberInfo进行B2B检测:');
        debugPrint('   会员ID: ${memberInfo['member_id']}');
        debugPrint('   会员姓名: ${memberInfo['name']}');

        // 使用memberInfo进行检测
        final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(memberInfo, memberId: memberInfo['member_id']?.toString());
        isB2BCustomer = detectedType == CustomerType.b2b;

        debugPrint('🔍 客户类型检测结果:');
        debugPrint('   检测到的客户类型: ${detectedType.value}');
        debugPrint('   是否为B2B客户: $isB2BCustomer');
        debugPrint('   检测依据: memberInfo中的customerType字段');

        if (isB2BCustomer) {
          debugPrint('🏢 ✅ 确认检测到B2B客户:');
          debugPrint('   └─ 会员ID: ${memberInfo['member_id']}');
          debugPrint('   └─ 会员姓名: ${memberInfo['name']}');
          debugPrint('   └─ 客户类型: ${detectedType.value}');
        } else {
          debugPrint('🏠 ❌ 检测到B2C客户:');
          debugPrint('   └─ 会员ID: ${memberInfo['member_id']}');
          debugPrint('   └─ 会员姓名: ${memberInfo['name']}');
          debugPrint('   └─ 客户类型: ${detectedType.value}');
        }
      } else {
        debugPrint('❌ 未找到缓存会员信息和memberInfo，默认为B2C客户');
      }
      
      final String finalPaymentType = isB2BCustomer ? 'B2B' : (widget.paymentData?.paymentMethodType ?? 'CASH');

      debugPrint('💼 支付类型确定流程:');
      debugPrint('   └─ B2B客户检测结果: $isB2BCustomer');
      debugPrint('   └─ 原始支付方式类型: ${widget.paymentData?.paymentMethodType}');
      debugPrint('   └─ 最终确定支付类型: $finalPaymentType');
      debugPrint('   └─ 决策逻辑: ${isB2BCustomer ? 'B2B客户 → 使用B2B支付类型' : 'B2C客户 → 使用原始支付类型或默认CASH'}');

      // Build order request
      debugPrint('📋 开始构建订单创建请求:');
      debugPrint('   └─ 燃料交易ID: $fuelTransactionId');
      debugPrint('   └─ 支付方式ID: $paymentMethodId');
      debugPrint('   └─ 站点ID: $stationId');
      debugPrint('   └─ 支付类型: $finalPaymentType');
      debugPrint('   └─ 分配金额: ${widget.paymentData?.totalAmount}');
      debugPrint('   └─ 员工编号: $employeeNo');
      debugPrint('   └─ 客户ID: ${memberInfo.isNotEmpty ? _safeParseInt(memberInfo['member_id']) : null}');
      debugPrint('   └─ 客户姓名: $customerNameForOrder');
      debugPrint('   └─ 客户手机: $customerPhone');
      debugPrint('   └─ 车辆类型: $vehicleType');
      debugPrint('   └─ 车牌号: $licensePlate');

      final CreateOrderRequest orderRequest = CreateOrderRequest(
        fuelTransactionId: fuelTransactionId,
        paymentMethod: paymentMethodId ?? 0, // Payment method ID as number
        stationId: stationId,
        paymentType: finalPaymentType, // 使用确定的支付类型
        allocatedAmount: widget.paymentData?.totalAmount ?? 0.0, // Always use original amount - let order service calculate discounts
        // Use fuel transaction's employee number (falls back to current user if not available)
        employeeNo: employeeNo,
        // If there's member ID, add to request
        customerId: memberInfo.isNotEmpty
            ? _safeParseInt(memberInfo['member_id'])
            : null,
        // Use actual member name (excluding auto-generated names with "Customer" prefix)
        customerName: customerNameForOrder,
        // Add new fields according to API documentation
        customerPhone: customerPhone, // 客户手机号
        vehicleType: vehicleType, // 车型（Car, Motorbike, Truck等）
        licensePlate: licensePlate, // 车牌号
        metadata: <String, dynamic>{
          'original_amount': widget.paymentData?.totalAmount ?? 0.0, // Original transaction amount (before any discounts)
          'received_amount': widget.paymentData?.totalAmount ?? 0.0, // Use original amount - let order service handle promotions
          'change_amount': 0.0, // No change for non-cash payments
          'user_id': userId ?? 'unknown', // Current user ID
          'payment_type': widget.paymentData?.paymentMethodType ?? 'CASH', // Payment type stored in metadata
          'payment_method_name': widget.paymentData?.paymentMethodName,
          'payment_note': 'Payment - promotion calculation handled by order service',
          
          // 车辆类型信息 - 从会员信息或PaymentTransactionData中获取
          if (memberInfo.isNotEmpty && memberInfo['vehicle_type'] != null && memberInfo['vehicle_type'].toString().isNotEmpty)
            'vehicle_type': memberInfo['vehicle_type'],
          if (widget.paymentData?.metadata?['vehicle_type'] != null)
            'vehicle_type': widget.paymentData!.metadata!['vehicle_type'],
          if (widget.paymentData?.metadata?['member_vehicle_original'] != null)
            'member_vehicle_original': widget.paymentData!.metadata!['member_vehicle_original'],
          
          // 车牌号信息
          if (memberInfo.isNotEmpty && memberInfo['vehicle_plate'] != null && memberInfo['vehicle_plate'].toString().isNotEmpty)
            'vehicle_plate': memberInfo['vehicle_plate'],
          if (widget.paymentData?.metadata?['vehicle_plate'] != null)
            'vehicle_plate': widget.paymentData!.metadata!['vehicle_plate'],
          
          // Put ERP data in erp_info
          'erp_info': erpData,

          // Put member data in member_info (if available)
          if (memberInfo.isNotEmpty) 'member_info': memberInfo,
          
          // Bank type information (if bank card payment)
          if (widget.paymentData?.paymentMethodConfig?['bank_type'] != null) 
            'bank_type': widget.paymentData!.paymentMethodConfig!['bank_type'],
            
          // Add applied promotions to metadata
          // if (_promotionResponse != null && _promotionResponse!.appliedPromotions.isNotEmpty)
          //   'appliedPromotions': _promotionResponse!.appliedPromotions
          //       .map((promotion) => promotion.toJson())
          //       .toList(),
          // 添加新的促销响应字段
          // if (_promotionResponse != null)
          //   'promotionInfo': {
          //     'orderId': _promotionResponse!.orderId,
          //     'vehicleType': _promotionResponse!.vehicleType,
          //     'originalAmount': _promotionResponse!.originalAmount,
          //     'discountedAmount': _promotionResponse!.discountedAmount,
          //     'discountAmount': _promotionResponse!.discountAmount,
          //     'calculationTime': _promotionResponse!.calculationTime,
          //     'totalItems': _promotionResponse!.totalItems,
          //     'totalQuantity': _promotionResponse!.totalQuantity,
          //     'items': _promotionResponse!.items.map((item) => item.toJson()).toList(),
          //   },
        },
      );

      // 验证订单请求中的B2B相关信息
      debugPrint('✅ 订单创建请求构建完成，验证B2B信息传递:');
      debugPrint('   └─ 请求中的支付类型: ${orderRequest.paymentType}');
      debugPrint('   └─ 请求中的客户ID: ${orderRequest.customerId}');
      debugPrint('   └─ 请求中的客户姓名: ${orderRequest.customerName}');
      debugPrint('   └─ 请求中的客户手机: ${orderRequest.customerPhone}');
      debugPrint('   └─ B2B信息是否正确传递: ${orderRequest.paymentType == 'B2B' ? '✅ 是' : '❌ 否'}');
      if (orderRequest.metadata?['member_info'] != null) {
        final memberInfoInRequest = orderRequest.metadata!['member_info'] as Map<String, dynamic>;
        debugPrint('   └─ 订单中的会员信息:');
        debugPrint('      ├─ member_id: ${memberInfoInRequest['member_id']}');
        debugPrint('      ├─ name: ${memberInfoInRequest['name']}');
        debugPrint('      └─ customer_type相关字段: ${memberInfoInRequest['customerType'] ?? memberInfoInRequest['customer_type'] ?? '未找到'}');
      }

      // Validate order request data integrity
      final String? validationError = orderRequest.validate();
      if (validationError != null) {
        // Close loading indicator
        if (context.mounted) Navigator.pop(context);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order data validation failed: $validationError'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
        return;
      }

      // Print detailed request information for debugging
      debugPrint('🚀 Create order request details:');
      debugPrint('   Fuel transaction ID: $fuelTransactionId');
      debugPrint('   Payment method ID: $paymentMethodId (number type)');
      debugPrint('   Payment type: ${widget.paymentData?.paymentMethodType ?? 'CASH'}');
      debugPrint('   Station ID: $stationId');
      debugPrint('   Allocated amount: ${widget.paymentData?.totalAmount} (original price - let order service calculate discounts)');
      debugPrint('   Customer Name: $customerNameForOrder');
      debugPrint('   Customer ID: ${memberInfo.isNotEmpty ? memberInfo['member_id'] : 'null'}');
      debugPrint('   Customer Phone: $customerPhone');
      debugPrint('   Vehicle Type: $vehicleType');
      debugPrint('   License Plate: $licensePlate');
      debugPrint('   Current User ID: $userId (${userId.runtimeType})');
      debugPrint('   Employee number (from transaction): $employeeNo');
      debugPrint('   Payment method config: ${widget.paymentData?.paymentMethodConfig}');

      // 输出车辆类型信息用于调试
      debugPrint('🚗 Vehicle type information for order:');
      debugPrint('   From memberInfo: ${memberInfo['vehicle_type']}');
      debugPrint('   From paymentData metadata: ${widget.paymentData?.metadata?['vehicle_type']}');
      debugPrint('   From paymentData original type: ${widget.paymentData?.metadata?['member_vehicle_original']}');
      debugPrint('   Final vehicleType for API: $vehicleType');
      
      debugPrint('   Complete request JSON: ${orderRequest.toJson()}');

      // Get OrderApi service instance
      final OrderApi orderApi = ApiService().orderApi;

      // Call create order API
      final Order order = await orderApi.createOrder(orderRequest);

      // Close loading indicator
      if (context.mounted) {
        Navigator.pop(context);

        // Check if payment successful - unified handling of 200 and 201 return codes
        // created, processing, completed are all considered successful states
        final bool orderCreated = order.status == OrderStatus.created ||
            order.status == OrderStatus.processing ||
            order.status == OrderStatus.completed;

        if (orderCreated) {
          // 🍽️ Consume member cache (if cached member information was used)
          Member? consumedMember;
          if (cachedMember != null) {
            consumedMember = memberCacheService.consumeCache();
            debugPrint('🍽️ Cash payment: Member cache consumed - ${consumedMember?.name}');
          }

          // Get payment details
          String paymentId = 'N/A';
          String paymentNumber = 'N/A';

          if (order.payments.isNotEmpty) {
            final Map<String, dynamic> payment = order.payments.first;
            paymentId = payment['metadata']?['payment_id']?.toString() ?? 'N/A';
            paymentNumber =
                payment['metadata']?['payment_number']?.toString() ?? 'N/A';
          }

          // Build success message, including member information (if available)
          String successMessage =
              'Payment successful! Order ID: ${order.orderId}';
          if (consumedMember != null) {
            final String plateNumber = consumedMember.plateNumbers.isNotEmpty
                ? consumedMember.plateNumbers.first
                : '';
            successMessage += '\nMember: ${consumedMember.name} ($plateNumber)';
          }

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(successMessage),
              backgroundColor: BPColors.primary,
              duration: const Duration(seconds: 3),
            ),
          );

          // 🔄 Call FCC reset
          // try {
          //   await _resetFccNozzle();
          // } catch (e) {
          //   debugPrint('❌ Cash payment: FCC reset call failed - $e');
          //   // Don't block process, reset failure doesn't affect successful payment follow-up processing
          // }

          // 🖨️ Auto print receipt
          debugPrint('🖨️ Cash payment successful, starting auto print...');
          debugPrint('   Order ID: ${order.orderId}');
          debugPrint('   Order Status: ${order.status}');
          debugPrint('   Order Final Amount: ${order.finalAmount}');

          try {
            await _autoPrintService.autoPrintOrderReceipt(
              context,
              order,
              showSuccessMessage: false, // Don't show print success message to avoid too many prompts
              showFailureMessage: true, // Show print failure message for user awareness
            );
            debugPrint('✅ Cash payment page: Auto print call completed');
          } catch (e) {
            debugPrint('❌ Cash payment page: Auto print call failed - $e');
          }
        } else {
          // Show failure message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Payment failed - Status: ${order.getStatusText()}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }

        // Navigate back to home (use refreshList parameter to notify home to refresh list)
        if (context.mounted) {
          context.go('/', extra: <String, bool>{'refreshList': true});
        }
      }
    } catch (e) {
      // Close loading indicator
      if (context.mounted) {
        Navigator.pop(context);

        // Detailed error logging
        debugPrint('❌ Create order failed:');
        debugPrint('   Error type: ${e.runtimeType}');
        debugPrint('   Error message: $e');
        
        // Provide more friendly error messages based on error type
        String errorMessage = 'Payment failed: $e';
        if (e.toString().contains('400')) {
          errorMessage = 'Request data format error, please check payment information or contact technical support';
        } else if (e.toString().contains('bank_type')) {
          errorMessage = 'Bank card payment configuration error, please contact administrator to check bank type settings';
        } else if (e.toString().contains('network') || e.toString().contains('connection')) {
          errorMessage = 'Network connection failed, please check network settings and try again';
        }

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5), // Extend display time
            action: SnackBarAction(
              label: 'Details',
              textColor: Colors.white,
              onPressed: () {
                debugPrint('User requested to view error details: $e');
              },
            ),
          ),
        );
      }
    }
  }

  /// 检查当前客户是否为B2B客户
  bool _checkIfB2BCustomer() {
    debugPrint('🔍 [_checkIfB2BCustomer] 开始B2B客户检测...');

    // 检查会员缓存中的客户类型标识
    final Member? cachedMember = memberCacheService.cachedMember;
    if (cachedMember == null) {
      debugPrint('❌ [_checkIfB2BCustomer] 未找到缓存会员信息，返回false');
      return false;
    }

    debugPrint('💾 [_checkIfB2BCustomer] 找到缓存会员: ${cachedMember.id} - ${cachedMember.name}');

    // 从会员的metadata中检查B2B标识
    final Map<String, dynamic> memberMetadata = cachedMember.metadata;
    debugPrint('📋 [_checkIfB2BCustomer] 会员metadata内容:');
    memberMetadata.forEach((key, value) {
      debugPrint('   $key: $value (${value.runtimeType})');
    });

    // 使用统一的客户类型检测工具
    final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(memberMetadata, memberId: cachedMember.id);
    final bool isB2B = detectedType == CustomerType.b2b;

    debugPrint('🔍 [_checkIfB2BCustomer] 客户类型检测结果:');
    debugPrint('   检测到的客户类型: ${detectedType.value}');
    debugPrint('   是否为B2B客户: $isB2B');
    debugPrint('🎯 [_checkIfB2BCustomer] 最终结果: $isB2B');

    if (isB2B) {
      debugPrint('🏢 [_checkIfB2BCustomer] ✅ 确认为B2B客户');
    } else {
      debugPrint('🏠 [_checkIfB2BCustomer] ❌ 确认为B2C客户');
    }

    return isB2B;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      // Replace WillPopScope
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) return;
        context.pop();
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF8F9FA), // Light gray background
        appBar: BPAppBar(
          title: 'Confirm Payment',
          showLogo: false,
          onBack: () => context.pop(),
        ),
        body: Column(
          children: <Widget>[
            // Main content area (scrollable)
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    // Enhanced Payment Summary - 显示促销优化的支付摘要
                    widget.paymentData != null
                        ? EnhancedPaymentSummary(
                            originalAmount: widget.paymentData!.totalAmount,
                            promotionResponse: _promotionResponse,
                            isLoading: _isLoadingPromotion,
                          )
                        : const SizedBox.shrink(),
                    
                    const SizedBox(height: 16),
                    
                    // Order details (fuel info and member info only)
                    widget.paymentData != null
                        ? Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: <BoxShadow>[
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.06),
                                  offset: const Offset(0, 2),
                                  blurRadius: 12,
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                            child: OrderDetailWidget(
                              paymentData: widget.paymentData!,
                              showPromotions: false, // 不显示促销信息，因为已经在增强摘要中显示
                              showPaymentSummary: false, // 不显示支付摘要，因为已经在增强摘要中显示
                              showMemberInfo: true,
                              promotionResponse: _promotionResponse,
                              isLoadingPromotion: _isLoadingPromotion,
                            ),
                          )
                        : Container(
                            padding: const EdgeInsets.all(24),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: Colors.grey.withValues(alpha: 0.2),
                                width: 1,
                              ),
                            ),
                            child: const Center(
                              child: Column(
                                children: <Widget>[
                                  Icon(
                                    Icons.error_outline,
                                    size: 48,
                                    color: Colors.grey,
                                  ),
                                  SizedBox(height: 16),
                                  Text(
                                    'No payment data available',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                    const SizedBox(height: 16),

                    // Cash payment calculator button
                    if (widget.paymentData?.paymentMethodName?.toUpperCase() ==
                        'CASH') ...<Widget>[
                      _buildCashCalculatorButton(),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
        // Confirm payment button fixed at bottom
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                offset: const Offset(0, -2),
                blurRadius: 12,
                spreadRadius: 0,
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 8.0),
              child: _buildConfirmButton(),
            ),
          ),
        ),
      ),
    );
  }

  /// Build cash calculator button
  Widget _buildCashCalculatorButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: BPColors.primary.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: BPColors.primary.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: OutlinedButton.icon(
        icon: const Icon(
          Icons.calculate_outlined,
          color: BPColors.primary,
          size: 24,
        ),
        label: Text(
          'Cash Calculator',
          style: EDCTextStyles.bodyText.copyWith(
            fontWeight: FontWeight.w600,
            color: BPColors.primary,
            fontSize: 16,
          ),
        ),
        onPressed: () async {
          final double payableAmount = _promotionResponse != null 
              ? _promotionResponse!.discountedAmount / 100 
              : widget.paymentData?.totalAmount ?? 0.0;
              
          final Map<String, double>? result = await showCashCalculatorDialog(
            context,
            payableAmount,
          );

          if (result != null && mounted) {
            // Show calculation result
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: <Widget>[
                    Icon(
                      result['changeAmount']! >= 0
                          ? Icons.check_circle
                          : Icons.warning,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Change: Rp ${NumberFormat('#,###').format(result['changeAmount'])}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
                backgroundColor: result['changeAmount']! >= 0
                    ? BPColors.success
                    : BPColors.warning,
                behavior: SnackBarBehavior.floating,
                margin: const EdgeInsets.all(16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                duration: const Duration(seconds: 3),
              ),
            );
          }
        },
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 18.0, horizontal: 20.0),
          foregroundColor: BPColors.primary,
          side: BorderSide.none, // Remove border, use container border
          backgroundColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          minimumSize: const Size(double.infinity, 56),
        ),
      ),
    );
  }

  /// Build confirm payment button
  Widget _buildConfirmButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: <Color>[
            BPColors.primary,
            BPColors.primary.withValues(alpha: 0.9),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: BPColors.primary.withValues(alpha: 0.3),
            offset: const Offset(0, 4),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      ),
      child: ElevatedButton.icon(
        icon: const Icon(
          Icons.check_circle_outline,
          size: 24,
        ),
        label: Text(
          'Confirm ${widget.paymentData?.paymentMethodName ?? 'Payment'}',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            letterSpacing: 0.5,
          ),
        ),
        onPressed: _confirmPayment,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 18.0),
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          minimumSize: const Size(double.infinity, 56),
        ),
      ),
    );
  }
}
