import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/shift_model.dart';
import '../constants/api_constants.dart';
import 'api/api_service.dart';
import 'shared/storage_service.dart';

/// 班次状态枚举（保持兼容性）
enum ShiftStatus {
  notStarted, // 未开班
  active, // 班次进行中
  ended, // 已结班
}

/// 班次信息模型（兼容旧版本）
class ShiftInfo {
  ShiftInfo({
    required this.shiftId,
    required this.operatorId,
    required this.startTime,
    required this.startingCash,
    required this.status,
    this.endTime,
    this.endingCash,
  });

  /// 从新的ShiftModel转换
  factory ShiftInfo.fromShiftModel(ShiftModel model,
      {String? operatorId, double? startingCash, double? endingCash}) {
    return ShiftInfo(
      shiftId: model.shiftNumber,
      operatorId: operatorId ?? 'UNKNOWN',
      startTime: model.startTime,
      startingCash: startingCash ?? 0.0,
      status: model.isActive
          ? ShiftStatus.active
          : (model.isClosed ? ShiftStatus.ended : ShiftStatus.notStarted),
      endTime: model.endTime,
      endingCash: endingCash,
    );
  }
  final String shiftId;
  final String operatorId;
  final DateTime startTime;
  final double startingCash;
  final ShiftStatus status;
  final DateTime? endTime;
  final double? endingCash;

  ShiftInfo copyWith({
    String? shiftId,
    String? operatorId,
    DateTime? startTime,
    double? startingCash,
    ShiftStatus? status,
    DateTime? endTime,
    double? endingCash,
  }) {
    return ShiftInfo(
      shiftId: shiftId ?? this.shiftId,
      operatorId: operatorId ?? this.operatorId,
      startTime: startTime ?? this.startTime,
      startingCash: startingCash ?? this.startingCash,
      status: status ?? this.status,
      endTime: endTime ?? this.endTime,
      endingCash: endingCash ?? this.endingCash,
    );
  }

  /// 获取班次运行时间
  String get elapsedTime {
    final DateTime now = DateTime.now();
    final Duration duration = now.difference(startTime);
    final int hours = duration.inHours;
    final int minutes = duration.inMinutes % 60;
    return '${hours}h ${minutes}m';
  }

  /// 格式化开班时间
  String get formattedStartTime {
    return '${startTime.day.toString().padLeft(2, '0')}/${startTime.month.toString().padLeft(2, '0')}/${startTime.year} ${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化结班时间
  String? get formattedEndTime {
    if (endTime == null) return null;
    return '${endTime!.day.toString().padLeft(2, '0')}/${endTime!.month.toString().padLeft(2, '0')}/${endTime!.year} ${endTime!.hour.toString().padLeft(2, '0')}:${endTime!.minute.toString().padLeft(2, '0')}';
  }
}

/// 班次管理服务
class ShiftService extends ChangeNotifier {
  factory ShiftService() => _instance;
  ShiftService._internal();
  static final ShiftService _instance = ShiftService._internal();

  /// API服务实例 - 使用统一的ApiService
  final ApiService _apiService = ApiService();

  /// 当前站点ID（需要配置）
  int _currentStationId = 1; // TODO: 从配置或登录信息中获取

  /// 当前班次信息
  ShiftInfo? _currentShift;
  ShiftModel? _currentShiftModel;

  /// 当前操作员信息
  String? _currentOperatorId;
  double? _currentStartingCash;
  double? _currentEndingCash;

  /// 是否已初始化
  bool _initialized = false;

  /// 获取初始化状态
  bool get isInitialized => _initialized;

  /// 班次状态流控制器
  final StreamController<ShiftInfo?> _shiftStatusController = 
      StreamController<ShiftInfo?>.broadcast();

  /// 初始化服务
  Future<void> initialize({
    ApiEnvironment? env,
    int? stationId,
  }) async {
    if (_initialized) return;

    try {
      // 如果没有指定环境，尝试从StorageService获取当前环境
      ApiEnvironment currentEnv = env ?? ApiEnvironment.local;
      if (env == null) {
        try {
          final StorageService storageService = StorageService();
          await storageService.init();
          currentEnv = await storageService.getApiEnvironment();
        } catch (e) {
          debugPrint('Failed to get current environment, using local: $e');
        }
      }

      // ApiService已经在main.dart中用正确配置初始化，无需重新配置
      if (stationId != null) {
        _currentStationId = stationId;
      }

      // 尝试获取当前活跃班次
      await _loadCurrentShift();

      _initialized = true;
      debugPrint('ShiftService initialized successfully with environment: $currentEnv');
    } catch (e) {
      debugPrint('Failed to initialize ShiftService: $e');
      // 即使初始化失败，也标记为已初始化，以便后续重试
      _initialized = true;
    }
  }

  /// 加载当前班次
  Future<void> _loadCurrentShift() async {
    try {
      _currentShiftModel = await _apiService.shiftManagementApi.getCurrentShift(_currentStationId);
      if (_currentShiftModel != null) {
        _currentShift = ShiftInfo.fromShiftModel(
          _currentShiftModel!,
          operatorId: _currentOperatorId,
          startingCash: _currentStartingCash,
          endingCash: _currentEndingCash,
        );
      } else {
        _currentShift = null;
      }
      notifyListeners();
      
      // 发送状态到流中
      if (!_shiftStatusController.isClosed) {
        _shiftStatusController.add(_currentShift);
      }
    } catch (e) {
      debugPrint('Failed to load current shift: $e');
      // 加载失败时，保持当前状态
    }
  }

  /// 获取当前班次信息
  ShiftInfo? get currentShift => _currentShift;

  /// 获取当前班次模型
  ShiftModel? get currentShiftModel => _currentShiftModel;

  /// 获取班次状态流
  Stream<ShiftInfo?> get shiftStatusStream => _shiftStatusController.stream;

  /// 获取当前班次状态
  ShiftStatus get currentStatus =>
      _currentShift?.status ?? ShiftStatus.notStarted;

  /// 是否有活跃班次
  bool get hasActiveShift => _currentShift?.status == ShiftStatus.active;

  /// 是否可以开班
  bool get canStartShift => _currentShift?.status != ShiftStatus.active;

  /// 是否可以结班
  bool get canEndShift => _currentShift?.status == ShiftStatus.active;

  /// 设置当前站点ID
  void setStationId(int stationId) {
    _currentStationId = stationId;
  }

  /// 开始班次
  Future<bool> startShift({
    required String operatorId,
    required double startingCash,
    String? notes,
  }) async {
    if (!_initialized) {
      await initialize();
    }

    try {
      final StartShiftRequest request = StartShiftRequest(
        stationId: _currentStationId,
        operatorId: operatorId,
        startingCash: startingCash,
        notes: notes,
      );

      _currentShiftModel = await _apiService.shiftManagementApi.startShift(request);
      _currentOperatorId = operatorId;
      _currentStartingCash = startingCash;

      _currentShift = ShiftInfo.fromShiftModel(
        _currentShiftModel!,
        operatorId: operatorId,
        startingCash: startingCash,
      );

      notifyListeners();
      
      // 发送状态到流中
      if (!_shiftStatusController.isClosed) {
        _shiftStatusController.add(_currentShift);
      }

      debugPrint('Shift started successfully: ${_currentShift!.shiftId}');
      return true;
    } catch (e) {
      debugPrint('Failed to start shift: $e');
      return false;
    }
  }

  /// 结束班次
  Future<bool> endShift({
    required double endingCash,
  }) async {
    if (!_initialized) {
      await initialize();
    }

    if (_currentShift == null || _currentShift!.status != ShiftStatus.active) {
      return false;
    }

    try {
      _currentShiftModel = await _apiService.shiftManagementApi.endShift(_currentStationId);
      _currentEndingCash = endingCash;

      _currentShift = ShiftInfo.fromShiftModel(
        _currentShiftModel!,
        operatorId: _currentOperatorId,
        startingCash: _currentStartingCash,
        endingCash: endingCash,
      );

      notifyListeners();
      
      // 发送状态到流中
      if (!_shiftStatusController.isClosed) {
        _shiftStatusController.add(_currentShift);
      }

      debugPrint('Shift ended successfully: ${_currentShift!.shiftId}');
      return true;
    } catch (e) {
      debugPrint('Failed to end shift: $e');
      return false;
    }
  }

  /// 获取班次详情
  Future<ShiftModel?> getShiftDetails(String shiftId) async {
    if (!_initialized) {
      await initialize();
    }

    try {
      return await _apiService.shiftManagementApi.getShiftById(shiftId);
    } catch (e) {
      debugPrint('Failed to get shift details: $e');
      return null;
    }
  }

  /// 刷新当前班次状态
  Future<void> refreshCurrentShift() async {
    if (!_initialized) {
      await initialize();
    }

    await _loadCurrentShift();
  }

  /// 重置班次状态（仅本地，不调用API）
  void resetShift() {
    _currentShift = null;
    _currentShiftModel = null;
    _currentOperatorId = null;
    _currentStartingCash = null;
    _currentEndingCash = null;
    notifyListeners();
    
    // 发送状态到流中
    if (!_shiftStatusController.isClosed) {
      _shiftStatusController.add(_currentShift);
    }
    
    debugPrint('Shift reset locally');
  }

  /// 获取班次状态描述
  String getStatusDescription() {
    switch (currentStatus) {
      case ShiftStatus.notStarted:
        return 'No Active Shift';
      case ShiftStatus.active:
        return 'Shift Active';
      case ShiftStatus.ended:
        return 'Shift Ended';
    }
  }

  /// 获取班次状态颜色
  String getStatusColorHex() {
    switch (currentStatus) {
      case ShiftStatus.notStarted:
        return '#666666'; // 灰色
      case ShiftStatus.active:
        return '#00A650'; // BP绿色
      case ShiftStatus.ended:
        return '#FFD903'; // BP黄色
    }
  }

  /// 切换API环境
  void switchEnvironment(ApiEnvironment env) {
    // ApiService会在全局配置更新时自动重新配置，无需手动切换
    debugPrint('Environment switch requested: ${env.name} (handled by global config)');
  }

  /// 检查网络连接
  Future<bool> checkConnection() async {
    if (!_initialized) {
      await initialize();
    }

    try {
      return await _apiService.shiftManagementApi.checkConnection();
    } catch (e) {
      debugPrint('Connection check failed: $e');
      return false;
    }
  }

  /// 获取错误信息（用于UI显示）
  String getLastError() {
    // TODO: 实现错误状态管理
    return 'No error information available';
  }

  /// 配置认证token
  void configureAuth(String? token) {
    if (_initialized) {
      _apiService.configureClient(token: token);
    }
  }

  /// 释放资源
  @override
  void dispose() {
    _shiftStatusController.close();
    super.dispose();
  }
}
