package com.example.edc_app

import android.app.Application
import android.util.Log
import com.sunmi.pay.hardware.aidlv2.readcard.ReadCardOptV2
import com.sunmi.pay.hardware.aidlv2.system.BasicOptV2
import sunmi.paylib.SunmiPayKernel

class EdcApplication : Application() {
    companion object {
        private const val TAG = "EdcApplication"
        lateinit var instance: EdcApplication
    }

    // 支付SDK模块
    var readCardOptV2: ReadCardOptV2? = null
        private set // Make setter private to control assignment
    var basicOptV2: BasicOptV2? = null
        private set

    // 连接状态
    @Volatile private var connectPaySDK = false
    @Volatile private var initializationAttempts = 0
    private val maxInitializationAttempts = 3

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "onCreate called")
        instance = this

        // 记录设备和SDK信息
        logDeviceInfo()

        // 初始化PaySDK
        bindPaySDKService()
    }

    /**
     * 记录设备和SDK信息
     */
    private fun logDeviceInfo() {
        try {
            Log.i(TAG, "=== Device Information ===")
            Log.i(TAG, "Device Model: ${android.os.Build.MODEL}")
            Log.i(TAG, "Device Manufacturer: ${android.os.Build.MANUFACTURER}")
            Log.i(TAG, "Android Version: ${android.os.Build.VERSION.RELEASE}")
            Log.i(TAG, "SDK Version: ${android.os.Build.VERSION.SDK_INT}")
            Log.i(TAG, "Application Package: ${packageName}")
            Log.i(TAG, "=========================")
        } catch (e: Exception) {
            Log.e(TAG, "Error logging device info: ${e.message}", e)
        }
    }

    /**
     * 测试读卡模块功能
     */
    private fun testReadCardModule() {
        try {
            Log.i(TAG, "=== Testing ReadCard Module ===")

            readCardOptV2?.let { readCard ->
                Log.d(TAG, "ReadCardOptV2 instance: $readCard")

                // 测试基本功能
                try {
                    // 检查读卡器状态
                    Log.d(TAG, "Testing card reader status...")

                    // 注意：这里不直接调用读卡，只是验证模块可用性
                    Log.i(TAG, "ReadCard module appears to be functional")

                } catch (e: Exception) {
                    Log.e(TAG, "Error testing ReadCard basic functions: ${e.message}", e)
                }

            } ?: run {
                Log.e(TAG, "ReadCardOptV2 is null, cannot test")
            }

            Log.i(TAG, "=== ReadCard Module Test Complete ===")

        } catch (e: Exception) {
            Log.e(TAG, "Error in testReadCardModule: ${e.message}", e)
        }
    }

    /**
     * 获取读卡模块状态信息
     */
    fun getReadCardStatus(): String {
        return try {
            when {
                !connectPaySDK -> "PaySDK not connected"
                readCardOptV2 == null -> "ReadCard module not initialized"
                else -> "ReadCard module ready"
            }
        } catch (e: Exception) {
            "Error checking status: ${e.message}"
        }
    }

    /**
     * 检查是否已连接PaySDK
     */
    fun isConnectPaySDK(): Boolean {
        Log.d(TAG, "isConnectPaySDK called, status: $connectPaySDK")
        return connectPaySDK
    }

    /**
     * 初始化商米支付SDK
     */
    @Synchronized // Ensure thread safety during initialization
    fun bindPaySDKService() {
        if (connectPaySDK) {
            Log.d(TAG, "PaySDK already connected.")
            return
        }

        initializationAttempts++
        Log.d(TAG, "Attempting to bind PaySDK service (attempt $initializationAttempts/$maxInitializationAttempts)...")

        try {
            val payKernel = SunmiPayKernel.getInstance()
            payKernel.initPaySDK(applicationContext, object : SunmiPayKernel.ConnectCallback {
                override fun onConnectPaySDK() {
                    try {
                        Log.i(TAG, "Successfully connected to SunmiPaySDK")

                        // 验证PayKernel状态
                        Log.d(TAG, "PayKernel instance: $payKernel")

                        // 获取各个功能模块
                        readCardOptV2 = payKernel.mReadCardOptV2
                        basicOptV2 = payKernel.mBasicOptV2

                        Log.d(TAG, "readCardOptV2 assigned: $readCardOptV2")
                        Log.d(TAG, "basicOptV2 assigned: $basicOptV2")

                        // 验证模块是否正确获取
                        if (readCardOptV2 != null) {
                            Log.i(TAG, "ReadCardOptV2 module successfully initialized")
                            // 测试读卡模块功能
                            testReadCardModule()
                        } else {
                            Log.w(TAG, "ReadCardOptV2 module is null")
                        }

                        if (basicOptV2 != null) {
                            Log.i(TAG, "BasicOptV2 module successfully initialized")
                        } else {
                            Log.w(TAG, "BasicOptV2 module is null")
                        }

                        // 更新连接状态
                        connectPaySDK = true
                        Log.i(TAG, "PaySDK initialization completed successfully")

                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing onConnectPaySDK: ${e.message}", e)
                        connectPaySDK = false
                        readCardOptV2 = null
                        basicOptV2 = null
                    }
                }
                
                override fun onDisconnectPaySDK() {
                    Log.e(TAG, "Disconnected from SunmiPaySDK")
                    connectPaySDK = false
                    readCardOptV2 = null
                    basicOptV2 = null

                    // 如果还有重试次数，尝试重新连接
                    if (initializationAttempts < maxInitializationAttempts) {
                        Log.w(TAG, "Attempting to reconnect PaySDK...")
                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                            bindPaySDKService()
                        }, 2000) // 2秒后重试
                    } else {
                        Log.e(TAG, "Max initialization attempts reached. PaySDK connection failed.")
                    }
                }
            })
            Log.d(TAG, "SunmiPaySDK initialization sequence started")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start SunmiPaySDK initialization: ${e.message}", e)
            connectPaySDK = false
            readCardOptV2 = null
            basicOptV2 = null
        }
    }
}
