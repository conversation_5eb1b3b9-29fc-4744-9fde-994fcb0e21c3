import 'package:flutter_test/flutter_test.dart';

/// 客户信息默认值设置功能测试
///
/// 测试场景：
/// 1. 当支付方式为TERA时，无论是否有客户信息，都清空并设置为ANONIM和**********
/// 2. 当支付方式为其他且客户信息为空时，应设置为ANONIM和**********
/// 3. 当支付方式为其他且有客户信息时，应保持原有信息
void main() {
  group('客户信息默认值设置测试', () {

    test('TERA支付方式 - 无论是否有客户信息都应清空并设置为ANONIM默认值', () {
      // 模拟TERA支付方式的逻辑（有客户信息的情况）
      String? customerName = 'John Doe';
      String? customerPhone = '************';
      final String paymentMethodName = 'TERA';

      // 模拟代码中的新逻辑
      final bool isTeraPayment = paymentMethodName.toUpperCase() == 'TERA';

      if (isTeraPayment) {
        // TERA 支付方式：无论是否有客户信息，都清空并设置为默认值
        customerName = 'ANONIM';
        customerPhone = '**********';
      } else {
        // 其他支付方式：只有当客户姓名和手机号都为空时才设置默认值
        if ((customerName == null || customerName.trim().isEmpty) &&
            (customerPhone == null || customerPhone.trim().isEmpty)) {
          customerName = 'ANONIM';
          customerPhone = '**********';
        }
      }

      expect(customerName, equals('ANONIM'));
      expect(customerPhone, equals('**********'));
    });
    
    test('TERA支付方式 - 空客户信息也应设置为ANONIM默认值', () {
      // 模拟TERA支付方式的逻辑（空客户信息的情况）
      String? customerName;
      String? customerPhone;
      final String paymentMethodName = 'TERA';

      // 模拟代码中的新逻辑
      final bool isTeraPayment = paymentMethodName.toUpperCase() == 'TERA';

      if (isTeraPayment) {
        // TERA 支付方式：无论是否有客户信息，都清空并设置为默认值
        customerName = 'ANONIM';
        customerPhone = '**********';
      } else {
        // 其他支付方式：只有当客户姓名和手机号都为空时才设置默认值
        if ((customerName == null || customerName.trim().isEmpty) &&
            (customerPhone == null || customerPhone.trim().isEmpty)) {
          customerName = 'ANONIM';
          customerPhone = '**********';
        }
      }

      expect(customerName, equals('ANONIM'));
      expect(customerPhone, equals('**********'));
    });

    test('非TERA支付方式 - 空客户信息应设置为ANONIM默认值', () {
      // 模拟其他支付方式的逻辑
      String? customerName;
      String? customerPhone;
      final String paymentMethodName = 'CASH';

      // 模拟代码中的新逻辑
      final bool isTeraPayment = paymentMethodName.toUpperCase() == 'TERA';

      if (isTeraPayment) {
        // TERA 支付方式：无论是否有客户信息，都清空并设置为默认值
        customerName = 'ANONIM';
        customerPhone = '**********';
      } else {
        // 其他支付方式：只有当客户姓名和手机号都为空时才设置默认值
        if ((customerName == null || customerName.trim().isEmpty) &&
            (customerPhone == null || customerPhone.trim().isEmpty)) {
          customerName = 'ANONIM';
          customerPhone = '**********';
        }
      }

      expect(customerName, equals('ANONIM'));
      expect(customerPhone, equals('**********'));
    });

    test('非TERA支付方式 - 有客户信息时应保持原有信息不变', () {
      // 模拟有客户信息的情况
      String? customerName = 'John Doe';
      String? customerPhone = '************';
      final String paymentMethodName = 'CASH';

      // 模拟代码中的新逻辑
      final bool isTeraPayment = paymentMethodName.toUpperCase() == 'TERA';

      if (isTeraPayment) {
        // TERA 支付方式：无论是否有客户信息，都清空并设置为默认值
        customerName = 'ANONIM';
        customerPhone = '**********';
      } else {
        // 其他支付方式：只有当客户姓名和手机号都为空时才设置默认值
        if ((customerName == null || customerName.trim().isEmpty) &&
            (customerPhone == null || customerPhone.trim().isEmpty)) {
          customerName = 'ANONIM';
          customerPhone = '**********';
        }
      }

      // 应该保持原有信息
      expect(customerName, equals('John Doe'));
      expect(customerPhone, equals('************'));
    });
    
    test('非TERA支付方式 - 空字符串客户信息应设置默认值', () {
      // 模拟空字符串的情况
      String? customerName = '';
      String? customerPhone = '   '; // 空白字符
      final String paymentMethodName = 'BANK_CARD';

      // 模拟代码中的新逻辑
      final bool isTeraPayment = paymentMethodName.toUpperCase() == 'TERA';

      if (isTeraPayment) {
        // TERA 支付方式：无论是否有客户信息，都清空并设置为默认值
        customerName = 'ANONIM';
        customerPhone = '**********';
      } else {
        // 其他支付方式：只有当客户姓名和手机号都为空时才设置默认值
        if ((customerName == null || customerName.trim().isEmpty) &&
            (customerPhone == null || customerPhone.trim().isEmpty)) {
          customerName = 'ANONIM';
          customerPhone = '**********';
        }
      }

      expect(customerName, equals('ANONIM'));
      expect(customerPhone, equals('**********'));
    });
  });
}
