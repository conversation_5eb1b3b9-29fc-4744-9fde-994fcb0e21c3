import 'package:flutter_test/flutter_test.dart';

/// 客户信息车牌号问题测试
/// 
/// 验证客户姓名不会被错误设置为车牌号，手机号不会被设置为0000000000
void main() {
  group('客户信息车牌号问题测试', () {
    
    test('客户姓名不应该是车牌号', () {
      // 模拟会员信息，包含车牌号和姓名
      final Map<String, dynamic> memberInfo = {
        'name': 'Customer B1234ABC',  // 自动生成的姓名
        'vehicle_plate': 'B1234ABC',  // 车牌号
        'phone': '081234567890',
      };
      
      // 模拟cash_payment_page.dart中的逻辑
      String? customerNameForOrder;
      
      if (memberInfo.isNotEmpty && memberInfo['name'] != null && memberInfo['name'].toString().isNotEmpty) {
        final String memberName = memberInfo['name'].toString();
        // Skip auto-generated names like "Customer B1234ABC" or "Business Customer B1234ABC"
        if (!memberName.startsWith('Customer ') && !memberName.startsWith('Business Customer ')) {
          customerNameForOrder = memberName;
        } else {
          customerNameForOrder = null;  // 自动生成的姓名应该被忽略
        }
      } else {
        customerNameForOrder = null;
      }
      
      // 验证客户姓名不是车牌号
      expect(customerNameForOrder, isNull);  // 应该为null，而不是车牌号
      expect(customerNameForOrder, isNot(equals('B1234ABC')));  // 不应该是车牌号
    });
    
    test('手机号不应该是0000000000', () {
      // 模拟会员信息，包含默认手机号
      final Map<String, dynamic> memberInfo = {
        'name': 'John Doe',
        'phone': '0000000000',  // 默认手机号
      };
      
      // 模拟cash_payment_page.dart中的逻辑
      String? customerPhone;
      
      if (memberInfo.isNotEmpty && memberInfo['phone'] != null) {
        final String phoneFromMember = memberInfo['phone'].toString();
        // 过滤掉默认的手机号
        if (phoneFromMember.isNotEmpty && phoneFromMember != '0000000000') {
          customerPhone = phoneFromMember;
        }
      }
      
      // 验证默认手机号被过滤
      expect(customerPhone, isNull);  // 应该为null，而不是0000000000
    });
    
    test('真实客户信息应该正常使用', () {
      // 模拟真实的会员信息
      final Map<String, dynamic> memberInfo = {
        'name': 'John Doe',  // 真实姓名
        'phone': '081234567890',  // 真实手机号
        'vehicle_plate': 'B1234ABC',
      };
      
      // 模拟cash_payment_page.dart中的逻辑
      String? customerNameForOrder;
      String? customerPhone;
      
      // 处理客户姓名
      if (memberInfo.isNotEmpty && memberInfo['name'] != null && memberInfo['name'].toString().isNotEmpty) {
        final String memberName = memberInfo['name'].toString();
        if (!memberName.startsWith('Customer ') && !memberName.startsWith('Business Customer ')) {
          customerNameForOrder = memberName;
        } else {
          customerNameForOrder = null;
        }
      } else {
        customerNameForOrder = null;
      }
      
      // 处理客户手机号
      if (memberInfo.isNotEmpty && memberInfo['phone'] != null) {
        final String phoneFromMember = memberInfo['phone'].toString();
        if (phoneFromMember.isNotEmpty && phoneFromMember != '0000000000') {
          customerPhone = phoneFromMember;
        }
      }
      
      // 验证真实信息被正确使用
      expect(customerNameForOrder, equals('John Doe'));
      expect(customerPhone, equals('081234567890'));
    });
    
    test('空客户信息应该设置默认值', () {
      // 模拟空的会员信息
      final Map<String, dynamic> memberInfo = {
        'name': 'Customer B1234ABC',  // 自动生成的姓名
        'phone': '0000000000',  // 默认手机号
        'vehicle_plate': 'B1234ABC',
      };
      
      // 模拟cash_payment_page.dart中的逻辑
      String? customerNameForOrder;
      String? customerPhone;
      
      // 处理客户姓名
      if (memberInfo.isNotEmpty && memberInfo['name'] != null && memberInfo['name'].toString().isNotEmpty) {
        final String memberName = memberInfo['name'].toString();
        if (!memberName.startsWith('Customer ') && !memberName.startsWith('Business Customer ')) {
          customerNameForOrder = memberName;
        } else {
          customerNameForOrder = null;
        }
      } else {
        customerNameForOrder = null;
      }
      
      // 处理客户手机号
      if (memberInfo.isNotEmpty && memberInfo['phone'] != null) {
        final String phoneFromMember = memberInfo['phone'].toString();
        if (phoneFromMember.isNotEmpty && phoneFromMember != '0000000000') {
          customerPhone = phoneFromMember;
        }
      }
      
      // 模拟默认值设置逻辑
      final String paymentMethodName = 'CASH';
      final bool isTeraPayment = paymentMethodName.toUpperCase() == 'TERA';
      
      if (isTeraPayment) {
        customerNameForOrder = 'ANONIM';
        customerPhone = '1010101010';
      } else {
        if ((customerNameForOrder == null || customerNameForOrder.trim().isEmpty) &&
            (customerPhone == null || customerPhone.trim().isEmpty)) {
          customerNameForOrder = 'ANONIM';
          customerPhone = '1010101010';
        }
      }
      
      // 验证设置了正确的默认值
      expect(customerNameForOrder, equals('ANONIM'));
      expect(customerPhone, equals('1010101010'));
    });
    
    test('TERA支付应该强制使用默认值', () {
      // 模拟有真实客户信息的情况
      final Map<String, dynamic> memberInfo = {
        'name': 'John Doe',
        'phone': '081234567890',
        'vehicle_plate': 'B1234ABC',
      };
      
      // 模拟cash_payment_page.dart中的逻辑
      String? customerNameForOrder;
      String? customerPhone;
      
      // 处理客户姓名
      if (memberInfo.isNotEmpty && memberInfo['name'] != null && memberInfo['name'].toString().isNotEmpty) {
        final String memberName = memberInfo['name'].toString();
        if (!memberName.startsWith('Customer ') && !memberName.startsWith('Business Customer ')) {
          customerNameForOrder = memberName;
        } else {
          customerNameForOrder = null;
        }
      } else {
        customerNameForOrder = null;
      }
      
      // 处理客户手机号
      if (memberInfo.isNotEmpty && memberInfo['phone'] != null) {
        final String phoneFromMember = memberInfo['phone'].toString();
        if (phoneFromMember.isNotEmpty && phoneFromMember != '0000000000') {
          customerPhone = phoneFromMember;
        }
      }
      
      // 模拟TERA支付的默认值设置逻辑
      final String paymentMethodName = 'TERA';
      final bool isTeraPayment = paymentMethodName.toUpperCase() == 'TERA';
      
      if (isTeraPayment) {
        // TERA支付：无论是否有客户信息，都清空并设置为默认值
        customerNameForOrder = 'ANONIM';
        customerPhone = '1010101010';
      } else {
        if ((customerNameForOrder == null || customerNameForOrder.trim().isEmpty) &&
            (customerPhone == null || customerPhone.trim().isEmpty)) {
          customerNameForOrder = 'ANONIM';
          customerPhone = '1010101010';
        }
      }
      
      // 验证TERA支付强制使用默认值
      expect(customerNameForOrder, equals('ANONIM'));
      expect(customerPhone, equals('1010101010'));
    });
    
    test('边界情况 - 各种无效的客户信息', () {
      final List<Map<String, dynamic>> testCases = [
        // 空姓名和默认手机号
        {'name': '', 'phone': '0000000000'},
        // null姓名和默认手机号
        {'name': null, 'phone': '0000000000'},
        // 自动生成姓名和默认手机号
        {'name': 'Customer B1234ABC', 'phone': '0000000000'},
        // 自动生成姓名和空手机号
        {'name': 'Business Customer B5678XYZ', 'phone': ''},
        // 只有空格的姓名和默认手机号
        {'name': '   ', 'phone': '0000000000'},
      ];
      
      for (final Map<String, dynamic> memberInfo in testCases) {
        // 模拟cash_payment_page.dart中的逻辑
        String? customerNameForOrder;
        String? customerPhone;
        
        // 处理客户姓名
        if (memberInfo.isNotEmpty && memberInfo['name'] != null && memberInfo['name'].toString().isNotEmpty) {
          final String memberName = memberInfo['name'].toString();
          if (!memberName.startsWith('Customer ') && !memberName.startsWith('Business Customer ')) {
            customerNameForOrder = memberName;
          } else {
            customerNameForOrder = null;
          }
        } else {
          customerNameForOrder = null;
        }
        
        // 处理客户手机号
        if (memberInfo.isNotEmpty && memberInfo['phone'] != null) {
          final String phoneFromMember = memberInfo['phone'].toString();
          if (phoneFromMember.isNotEmpty && phoneFromMember != '0000000000') {
            customerPhone = phoneFromMember;
          }
        }
        
        // 模拟默认值设置逻辑
        final String paymentMethodName = 'CASH';
        final bool isTeraPayment = paymentMethodName.toUpperCase() == 'TERA';
        
        if (isTeraPayment) {
          customerNameForOrder = 'ANONIM';
          customerPhone = '1010101010';
        } else {
          if ((customerNameForOrder == null || customerNameForOrder.trim().isEmpty) &&
              (customerPhone == null || customerPhone.trim().isEmpty)) {
            customerNameForOrder = 'ANONIM';
            customerPhone = '1010101010';
          }
        }
        
        // 验证所有边界情况都设置了正确的默认值
        expect(customerNameForOrder, equals('ANONIM'), 
               reason: 'Failed for memberInfo: $memberInfo');
        expect(customerPhone, equals('1010101010'), 
               reason: 'Failed for memberInfo: $memberInfo');
      }
    });
  });
}
