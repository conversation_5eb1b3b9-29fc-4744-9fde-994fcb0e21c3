import '../../models/fcc_device.dart';
import '../../models/dispenser_model.dart';
import 'package:flutter/foundation.dart';
import '../nozzle_authorization_cache_service.dart';

/// FCC Device Adapter V2 - Simplified Version
///
/// 优化后的解决方案:
/// 1. 直接使用 nozzle metadata 中的 dispenserId 和 pumpGroupId
/// 2. 移除复杂的 ID 计算和映射逻辑
/// 3. 简化数据转换流程
/// 4. 保持 FCC 数据缓存用于状态管理
class FCCDeviceAdapterV2 {
  // === FCC原始数据缓存 ===

  /// FCC设备缓存 - 根据设备ID快速查找
  static final Map<String, FCCDevice> _fccDeviceCache = <String, FCCDevice>{};

  /// FCC nozzle缓存 - 根据nozzle ID快速查找
  static final Map<String, FCCNozzle> _fccNozzleCache = <String, FCCNozzle>{};

  /// PumpGroup ID到FCC设备信息的映射（保留用于调试和状态查询）
  static final Map<String, Map<String, dynamic>> _pumpGroupToFccInfoMap =
      <String, Map<String, dynamic>>{};

  /// 重置所有缓存（用于测试或系统重置）
  static void resetCache() {
    _fccDeviceCache.clear();
    _fccNozzleCache.clear();
    _pumpGroupToFccInfoMap.clear();
    debugPrint('🔄 FCC 数据缓存已重置');
  }

  // === 简化的转换方法 ===
  // 注意：现在 dispenserId 和 pumpGroupId 都直接从 nozzle metadata 中获取
  // 不再需要复杂的 ID 提取和映射逻辑





  // === Core Conversion Methods ===

  /// 转换FCC Device到EDC PumpGroup
  /// 优化：现在基于 nozzle 的 dispenserId 和 pumpGroupId 来组织数据
  static List<PumpGroup> fccDeviceToPumpGroups(FCCDevice fccDevice) {
    // 缓存FCC设备数据
    _fccDeviceCache[fccDevice.id] = fccDevice;

    // 转换所有nozzles
    final List<Nozzle> edcNozzles = fccDevice.nozzles
        .map(fccNozzleToEDC)
        .toList();

    // 按 pumpGroupId 分组 nozzles
    final Map<String, List<Nozzle>> nozzlesByPumpGroup = <String, List<Nozzle>>{};
    for (final Nozzle nozzle in edcNozzles) {
      nozzlesByPumpGroup[nozzle.pumpGroupId] ??= <Nozzle>[];
      nozzlesByPumpGroup[nozzle.pumpGroupId]!.add(nozzle);
    }

    // 为每个 pumpGroup 创建 PumpGroup 对象
    final List<PumpGroup> pumpGroups = <PumpGroup>[];
    for (final MapEntry<String, List<Nozzle>> entry in nozzlesByPumpGroup.entries) {
      final String pumpGroupId = entry.key;
      final List<Nozzle> nozzles = entry.value;

      // 从第一个 nozzle 获取 dispenserId（所有 nozzle 应该有相同的 dispenserId）
      final String dispenserId = nozzles.isNotEmpty ? nozzles.first.dispenserId : '1';

      // 存储FCC设备信息用于后续查询
      _pumpGroupToFccInfoMap[pumpGroupId] = <String, dynamic>{
        'fcc_device_id': fccDevice.id,
        'fcc_device_address': fccDevice.deviceAddress,
        'fcc_controller_id': fccDevice.controllerId,
        'fcc_dispenser_number': fccDevice.dispenserNumber,
        'dispenser_id': dispenserId,
        'mapping_method': 'from_nozzle_data',
        'dispenser_source': 'nozzle_field',
      };

      // 修复：简化pump group名称，避免重复和混淆
      // 使用更清晰的命名：Pump + pumpGroupId
      final String pumpGroupName = 'Pump $pumpGroupId';

      pumpGroups.add(PumpGroup(
        id: pumpGroupId,
        dispenserId: dispenserId,
        name: pumpGroupName,
        nozzles: nozzles,
        lastUpdateTime: fccDevice.lastSeen ?? fccDevice.updatedAt,
      ));
    }

    return pumpGroups;
  }

  /// 转换FCC Nozzle到EDC Nozzle
  static Nozzle fccNozzleToEDC(FCCNozzle fccNozzle, [int? pumpGroupId]) {
    // 优化：优先使用 nozzle 上的 dispenserId 和 pumpGroupId
    final String targetDispenserId = fccNozzle.dispenserId ?? '1';
    final String targetPumpGroupId = fccNozzle.pumpGroupId ?? '1';
    final String edcNozzleId = fccNozzle.id; // 直接使用FCCNozzle.id，无需转换！

    // 检查对应的FCCDevice是否在线
    final FCCDevice? fccDevice = _fccDeviceCache[fccNozzle.deviceId];
    final bool isDeviceOnline = fccDevice?.isOnline ?? false;

    // 如果设备离线，强制设置nozzle状态为offline，否则使用原始状态
    NozzleStatus edcStatus;
    String? errorMessage = fccNozzle.errorMessage;
    double currentVolume = fccNozzle.currentVolume;
    double currentAmount = fccNozzle.currentAmount;
    AuthorizationRequest? authRequest;

    if (!isDeviceOnline) {
      edcStatus = NozzleStatus.offline;
      errorMessage = errorMessage ??
          'Device offline: ${fccDevice?.status.value ?? "unknown"}';
      // 修复2: 离线时清除实时数据，避免显示误导信息
      currentVolume = 0.0;
      currentAmount = 0.0;
      authRequest = null; // 离线时清除授权信息
      // debugPrint('🔴 Nozzle ${fccNozzle.id} 离线: 设备 ${fccNozzle.deviceId} 状态为 ${fccDevice?.status.value ?? "unknown"}，已清除实时数据');
    } else {
      edcStatus = _mapNozzleStatus(fccNozzle.status);
      authRequest = _createAuthRequestFromFCCNozzle(fccNozzle, edcNozzleId);
    }

    // 缓存FCC nozzle数据 (新增)
    _fccNozzleCache[edcNozzleId] = fccNozzle;

    // 转换预授权信息
    PreauthInfo? preauthInfo;
    if (fccNozzle.preauthType != null &&
        fccNozzle.preauthNumber != null &&
        fccNozzle.preauthCreatedAt != null &&
        fccNozzle.preauthExpiresAt != null) {
      preauthInfo = PreauthInfo(
        type: fccNozzle.preauthType!,
        number: fccNozzle.preauthNumber!,
        createdAt: fccNozzle.preauthCreatedAt!,
        expiresAt: fccNozzle.preauthExpiresAt!,
      );
    }

    final Nozzle edcNozzle = Nozzle(
      id: edcNozzleId,
      dispenserId: targetDispenserId, // 优化：使用 nozzle 上的 dispenserId
      pumpGroupId: targetPumpGroupId,
      deviceName: fccNozzle.deviceName, // 传递设备名称
      number: fccNozzle.number,
      name: fccNozzle.name,
      fuelType: fccNozzle.fuelType,
      fuelGrade: fccNozzle.grade,
      price: fccNozzle.price,
      status: edcStatus, // 使用修复后的状态逻辑
      currentAuth: authRequest, // 使用修复后的授权信息
      preauthInfo: preauthInfo, // 新增：传递预授权信息
      currentVolume: currentVolume, // 使用修复后的油量数据
      currentAmount: currentAmount, // 使用修复后的金额数据
      lastUpdateTime: fccNozzle.lastUpdateTime,
      errorMessage: errorMessage, // 使用修复后的错误信息
    );

    // 调试信息：打印转换后的EDC数据
    if (edcNozzle.currentVolume > 0 || edcNozzle.currentAmount > 0) {
      // debugPrint(
      //     '   EDC数据: volume=${edcNozzle.currentVolume}, amount=${edcNozzle.currentAmount}, status=${edcNozzle.status}');
    }

    return edcNozzle;
  }

  // === Information Retrieval ===

  /// 根据PumpGroup ID获取FCC设备信息
  static Map<String, dynamic>? getFccInfoByPumpGroupId(String pumpGroupId) { // 优化：改为 String 类型
    return _pumpGroupToFccInfoMap[pumpGroupId];
  }

  // === Dispenser Grouping ===



  // === Batch Conversion ===

  static List<PumpGroup> fccDevicesToPumpGroups(List<FCCDevice> fccDevices) {
    final List<PumpGroup> allPumpGroups = <PumpGroup>[];
    for (final FCCDevice device in fccDevices) {
      allPumpGroups.addAll(fccDeviceToPumpGroups(device));
    }
    return allPumpGroups;
  }

  static List<Dispenser> pumpGroupsToDispensers(List<PumpGroup> pumpGroups) {
    final Map<String, List<PumpGroup>> dispenserGroups = <String, List<PumpGroup>>{};

    for (final PumpGroup pumpGroup in pumpGroups) {
      final String dispenserId = pumpGroup.dispenserId;
      dispenserGroups[dispenserId] ??= <PumpGroup>[];
      dispenserGroups[dispenserId]!.add(pumpGroup);
    }

    // 修复：按 dispenserId 排序以确保一致的顺序
    final List<String> sortedDispenserIds = dispenserGroups.keys.toList()
      ..sort((String a, String b) => a.compareTo(b));

    return sortedDispenserIds.map((String dispenserId) {
      final List<PumpGroup> groups = dispenserGroups[dispenserId]!;

      // 优化：按 PumpGroup ID 排序，现在 ID 是 String 类型
      groups.sort((PumpGroup a, PumpGroup b) => a.id.compareTo(b.id));
      // debugPrint('🔄 Dispenser $dispenserId PumpGroup排序: ${groups.map((g) => '${g.name}(id:${g.id})').toList()}');

      final DateTime latestUpdate = groups
          .map((PumpGroup g) => g.lastUpdateTime)
          .reduce((DateTime a, DateTime b) => a.isAfter(b) ? a : b);

      // 修复: 检查所有关联的FCCDevice的实际在线状态
      bool isDispenserOnline = true;
      for (final PumpGroup pumpGroup in groups) {
        // 从映射信息获取FCC设备ID
        final Map<String, dynamic>? fccInfo =
            _pumpGroupToFccInfoMap[pumpGroup.id];
        if (fccInfo != null) {
          final String fccDeviceId = fccInfo['fcc_device_id'] as String;
          final FCCDevice? fccDevice = _fccDeviceCache[fccDeviceId];

          // 如果任何一个关联的FCC设备离线，整个Dispenser就离线
          if (fccDevice != null && !fccDevice.isOnline) {
            isDispenserOnline = false;
            debugPrint(
                '🔴 Dispenser $dispenserId 离线: FCC设备 $fccDeviceId 状态为 ${fccDevice.status.value}');
            break;
          }
        }
      }

      return Dispenser(
        id: dispenserId,
        name: 'Dispenser $dispenserId',
        displayName: 'Dispenser $dispenserId',
        pumpGroups: groups, // 已排序的groups
        isOnline: isDispenserOnline, // 使用修复后的状态逻辑
        lastUpdateTime: latestUpdate,
      );
    }).toList();
  }

  static List<Dispenser> fccDevicesToDispensers(List<FCCDevice> fccDevices) {
    // debugPrint('🔄 FCCDeviceAdapterV2: 开始转换 ${fccDevices.length} 个FCC设备');

    if (fccDevices.isEmpty) {
      // debugPrint('⚠️ FCCDeviceAdapterV2: 输入的FCC设备列表为空');
      return <Dispenser>[];
    }

    // 打印设备详情（已简化）
    // debugPrint('   转换了 ${fccDevices.length} 个 FCC 设备');

    final List<PumpGroup> pumpGroups = fccDevicesToPumpGroups(fccDevices);
    // debugPrint('🔄 FCCDeviceAdapterV2: 转换得到 ${pumpGroups.length} 个PumpGroup');

    final List<Dispenser> dispensers = pumpGroupsToDispensers(pumpGroups);
    // debugPrint('✅ FCCDeviceAdapterV2: 最终得到 ${dispensers.length} 个Dispenser');

    // 打印缓存统计
    // debugPrint('💾 FCCDeviceAdapterV2: 缓存统计 - 设备:${_fccDeviceCache.length}, nozzle:${_fccNozzleCache.length}');

    return dispensers;
  }

  // === Status Conversion (Unchanged) ===

  static NozzleStatus _mapNozzleStatus(FCCNozzleStatus fccStatus) {
    switch (fccStatus) {
      case FCCNozzleStatus.idle:
        return NozzleStatus.idle;
      case FCCNozzleStatus.selected:
      case FCCNozzleStatus.authorized:
        return NozzleStatus.auth;
      case FCCNozzleStatus.out:
      case FCCNozzleStatus.filling:
        return NozzleStatus.fuelling;
      case FCCNozzleStatus.completed:
        return NozzleStatus.complete;
      case FCCNozzleStatus.suspended:
        return NozzleStatus.auth;
      case FCCNozzleStatus.error:
      case FCCNozzleStatus.maintenance:
        return NozzleStatus.offline;
    }
  }

  static FCCNozzleStatus edcStatusToFCC(NozzleStatus edcStatus) {
    switch (edcStatus) {
      case NozzleStatus.idle:
        return FCCNozzleStatus.idle;
      case NozzleStatus.auth:
        return FCCNozzleStatus.authorized;
      case NozzleStatus.fuelling:
        return FCCNozzleStatus.filling;
      case NozzleStatus.complete:
      case NozzleStatus.pay:
        return FCCNozzleStatus.completed;
      case NozzleStatus.offline:
        return FCCNozzleStatus.error;
    }
  }

  // === Authorization Conversion (Simplified) ===

  static AuthorizationRequest? _createAuthRequestFromFCCNozzle(
      FCCNozzle fccNozzle, String edcNozzleId) {
    if (fccNozzle.status != FCCNozzleStatus.authorized &&
        fccNozzle.status != FCCNozzleStatus.filling &&
        fccNozzle.status != FCCNozzleStatus.suspended) {
      return null;
    }

    // 优先使用缓存的授权信息
    final AuthorizationRequest? cachedAuth = 
        nozzleAuthorizationCache.getAuthorization(edcNozzleId);
    
    if (cachedAuth != null) {
      debugPrint('✅ FCCDeviceAdapter: 使用缓存的授权信息 for nozzle $edcNozzleId');
      return cachedAuth;
    }

    // 如果缓存中没有，使用FCC返回的状态数据
    final Map<String, dynamic>? stateData = fccNozzle.stateData;
    if (stateData == null) {
      debugPrint('⚠️ FCCDeviceAdapter: 无FCC状态数据，使用默认授权信息 for nozzle $edcNozzleId');
      return AuthorizationRequest(
        nozzleId: edcNozzleId,
        mode: AuthMode.amount,
        value: null,
        staffId: 'FCC_AUTO',
        requestTime: fccNozzle.lastUpdateTime,
      );
    }

    final double? presetAmount = stateData['preset_amount'] as double?;
    final double? presetVolume = stateData['preset_volume'] as double?;
    final String staffId = stateData['staff_id'] as String? ?? 'FCC_AUTO';

    AuthMode mode = AuthMode.full;
    double? value;

    if (presetAmount != null && presetAmount > 0) {
      mode = AuthMode.amount;
      value = presetAmount;
    } else if (presetVolume != null && presetVolume > 0) {
      mode = AuthMode.volume;
      value = presetVolume;
    }

    debugPrint('⚠️ FCCDeviceAdapter: 使用FCC状态数据创建授权信息 for nozzle $edcNozzleId, staffId: $staffId');
    
    return AuthorizationRequest(
      nozzleId: edcNozzleId,
      mode: mode,
      value: value,
      staffId: staffId,
      requestTime: stateData['auth_time'] != null
          ? DateTime.parse(stateData['auth_time'] as String)
          : fccNozzle.lastUpdateTime,
    );
  }

  // === 优化的查找方法 (修改) ===

  /// 根据EDC Nozzle ID查找FCC设备（优化版本）
  /// 从缓存中获取真实的FCC设备对象
  static FCCDevice? getFccInfoByEdcId(String edcNozzleId) {
    // 从缓存中查找FCC nozzle (edcNozzleId就是fccNozzle.id)
    final FCCNozzle? fccNozzle = _fccNozzleCache[edcNozzleId];
    if (fccNozzle == null) {
      debugPrint('❌ 未找到EDC nozzle $edcNozzleId 对应的FCC数据');
      debugPrint('   当前缓存的nozzle数量: ${_fccNozzleCache.length}');
      debugPrint(
          '   缓存的nozzle IDs: ${_fccNozzleCache.keys.take(5).toList()}...');
      return null;
    }

    // 查找对应的FCC设备
    final FCCDevice? fccDevice = _fccDeviceCache[fccNozzle.deviceId];
    if (fccDevice == null) {
      debugPrint('❌ 未找到FCC nozzle ${fccNozzle.id} 对应的设备 ${fccNozzle.deviceId}');
      return null;
    }

    debugPrint(
        '✅ 成功查找FCC设备: EDC($edcNozzleId) -> FCC(${fccDevice.id}:${fccNozzle.number})');
    return fccDevice;
  }

  /// 根据EDC Nozzle ID查找FCC Nozzle（新增）
  /// 从缓存中获取真实的FCC nozzle对象
  static FCCNozzle? getFccNozzleByEdcId(String edcNozzleId) {
    final FCCNozzle? fccNozzle = _fccNozzleCache[edcNozzleId];
    if (fccNozzle != null) {
      debugPrint(
          '✅ 成功查找FCC nozzle: EDC($edcNozzleId) -> FCC(${fccNozzle.deviceId}:${fccNozzle.number})');
    }
    return fccNozzle;
  }

  /// 向后兼容：根据FCC信息查找EDC Nozzle ID
  static String? getEdcIdByFccInfo(String fccDeviceId, int fccNozzleNumber) {
    // 遍历FCC设备缓存查找匹配的设备和nozzle
    final FCCDevice? fccDevice = _fccDeviceCache[fccDeviceId];
    if (fccDevice == null) {
      debugPrint('❌ 未找到FCC设备: $fccDeviceId');
      return null;
    }

    // 在设备的nozzles中查找匹配的nozzle编号
    for (final FCCNozzle fccNozzle in fccDevice.nozzles) {
      if (fccNozzle.number == fccNozzleNumber) {
        debugPrint(
            '✅ 成功查找EDC ID: FCC($fccDeviceId:$fccNozzleNumber) -> EDC(${fccNozzle.id})');
        return fccNozzle.id; // edcNozzleId就是fccNozzle.id
      }
    }

    debugPrint('❌ 在FCC设备 $fccDeviceId 中未找到nozzle编号 $fccNozzleNumber');
    return null;
  }

  // === 缓存管理方法 (新增) ===

  /// 获取缓存中的FCC设备
  static FCCDevice? getCachedFccDevice(String deviceId) {
    return _fccDeviceCache[deviceId];
  }

  /// 获取缓存中的FCC nozzle
  static FCCNozzle? getCachedFccNozzle(String edcNozzleId) {
    return _fccNozzleCache[edcNozzleId];
  }

  /// 获取所有缓存的设备ID
  static List<String> getCachedDeviceIds() {
    return _fccDeviceCache.keys.toList();
  }

  /// 获取所有缓存的nozzle ID
  static List<String> getCachedNozzleIds() {
    return _fccNozzleCache.keys.toList();
  }

  // === Utilities and Debugging ===

  static bool isValidForMapping(FCCDevice fccDevice) {
    return fccDevice.isPump &&
        fccDevice.deviceAddress > 0 &&
        fccDevice.nozzles.length <= 15;
  }

  /// 获取简化的统计信息
  static Map<String, dynamic> getMappingStatistics() {
    return <String, dynamic>{
      'adapter_version': 'V2_simplified',
      'pumpgroup_to_fccinfo_mappings': _pumpGroupToFccInfoMap.length,
      'cached_fcc_devices': _fccDeviceCache.length,
      'cached_fcc_nozzles': _fccNozzleCache.length,
      'mapping_method': 'metadata_based',
      'fcc_cache_details': <String, Object>{
        'device_ids': _fccDeviceCache.keys.toList(),
        'nozzle_count_by_device': _fccDeviceCache.map<String, int>(
            (String deviceId, FCCDevice device) =>
                MapEntry(deviceId, device.nozzles.length)),
        'sample_nozzle_ids': _fccNozzleCache.keys.take(10).toList(),
        'total_nozzles_cached': _fccNozzleCache.length,
      },
    };
  }



  /// 验证缓存一致性
  static Map<String, dynamic> validateCacheConsistency() {
    final List<String> issues = <String>[];
    int validDevices = 0;
    int validNozzles = 0;

    // 检查设备缓存
    for (final MapEntry<String, FCCDevice> entry in _fccDeviceCache.entries) {
      final String deviceId = entry.key;
      final FCCDevice device = entry.value;

      if (device.id != deviceId) {
        issues.add('设备缓存键值不匹配: key=$deviceId, device.id=${device.id}');
      } else {
        validDevices++;
      }

      // 检查该设备的nozzles是否都在缓存中
      for (final FCCNozzle nozzle in device.nozzles) {
        if (_fccNozzleCache.containsKey(nozzle.id)) {
          final FCCNozzle cachedNozzle = _fccNozzleCache[nozzle.id]!;
          if (cachedNozzle.deviceId != deviceId) {
            issues.add(
                'Nozzle ${nozzle.id} 的deviceId不匹配: cached=${cachedNozzle.deviceId}, expected=$deviceId');
          } else {
            validNozzles++;
          }
        } else {
          issues.add('设备 $deviceId 的nozzle ${nozzle.id} 未在缓存中');
        }
      }
    }

    return <String, dynamic>{
      'total_issues': issues.length,
      'issues': issues,
      'valid_devices': validDevices,
      'valid_nozzles': validNozzles,
      'cache_health': issues.isEmpty ? 'healthy' : 'has_issues',
    };
  }

  // === Reverse Lookup Methods ===

  /// Find FCC nozzle by EDC nozzle ID for reverse operations
  static FCCNozzle? findFCCNozzleByEDCId(String edcNozzleId) {
    try {
      // 直接从缓存中查找，因为 EDC nozzle ID 就是 FCC nozzle ID
      final FCCNozzle? fccNozzle = _fccNozzleCache[edcNozzleId];

      if (fccNozzle != null) {
        debugPrint('🔍 找到反向映射: EDC:$edcNozzleId → FCC:${fccNozzle.deviceId}:${fccNozzle.id}');
        return fccNozzle;
      }

      debugPrint('❌ 未找到EDC Nozzle $edcNozzleId 对应的FCC信息');
      return null;
    } catch (e) {
      debugPrint('❌ 反向查找异常: $e');
      return null;
    }
  }
}
