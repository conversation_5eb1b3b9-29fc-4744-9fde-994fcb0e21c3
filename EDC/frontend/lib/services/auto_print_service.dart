import 'package:flutter/material.dart';
import '../models/order.dart';
import '../services/native/sunmi_printer_service.dart';
import '../services/timezone_service.dart';
import '../constants/bp_colors.dart';
import 'package:intl/intl.dart';

/// 自动打印服务
/// 提供订单创建成功后自动打印小票的功能
class AutoPrintService {
  factory AutoPrintService() => _instance;
  AutoPrintService._internal();
  static final AutoPrintService _instance = AutoPrintService._internal();

  final SunmiPrinterService _printerService = SunmiPrinterService.instance;
  final TimezoneService _timezoneService = TimezoneService();
  bool _isInitialized = false;

  /// 初始化打印服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    await _timezoneService.init();
    _isInitialized = true;
  }

  /// 订单创建成功后自动打印小票
  Future<void> autoPrintOrderReceipt(
    BuildContext context,
    Order order, {
    bool showSuccessMessage = true,
    bool showFailureMessage = true,
  }) async {
    try {
      if (!_isInitialized) await initialize();
      if (order.orderId.isEmpty) return;

      final PrinterStatus status = await _printerService.getPrinterStatus();
      if (status != PrinterStatus.normal) {
        if (showFailureMessage && context.mounted) {
          _showMessage(context, 'Printer status not normal: ${status.name}', isError: true);
        }
        return;
      }

      await printOrderReceipt(order);

      if (showSuccessMessage && context.mounted) {
        _showMessage(context, 'Receipt printed successfully', isError: false);
      }
    } catch (e) {
      if (showFailureMessage && context.mounted) {
        _showMessage(context, 'Auto-print failed: ${e.toString()}', isError: true);
      }
    }
  }

  /// 静默打印（不显示任何消息）
  Future<bool> silentPrintOrderReceipt(Order order) async {
    if (!_isInitialized) return false;

    try {
      final PrinterStatus status = await _printerService.getPrinterStatus();
      if (status != PrinterStatus.normal) return false;

      await printOrderReceipt(order);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 打印订单小票（公共静态方法）
  static Future<void> printOrderReceipt(
    Order order, {
    bool isReprint = false,
    int reprintCount = 1,
    String? vehiclePlate,
    String? vehicleType,
    String? stationName,
    String? stationAddress,
  }) async {
    final SunmiPrinterService printer = SunmiPrinterService.instance;

    try {
      await printer.enterPrinterBuffer(clearBuffer: true);

      // 打印配置
      const double headerFontSize = 32.0;
      const double bodyFontSize = 24.0;
      const double smallFontSize = 20.0;
      const String separator = '--------------------------------';

      final NumberFormat currencyFormat = NumberFormat('#,##0', 'id_ID');

      // === 1. BP-AKR LOGO ===
      await printer.setAlignment(PrintAlignment.center);
      // 使用灰度图片打印BP logo
      try {
        await printer.printImageFromAssetsCustom('assets/images/bp_akr_logo_receipt.png', 2, width: 200);
        await printer.printText('\n'); // 添加换行
      } catch (e) {
        // 如果图片打印失败，回退到文本方式
        await printer.setFontSize(headerFontSize);
        await printer.printText('BP-AKR\n');
        await printer.setFontSize(smallFontSize);
        await printer.printText('Fuels Retail\n\n');
      }

      // === 2. STATION INFO ===
      await printer.setFontSize(bodyFontSize);
      await printer.printText('${stationName ?? 'MERUYA ILIR STATION'}\n');
      await printer.setFontSize(smallFontSize);
      
      if (stationAddress != null && stationAddress.isNotEmpty) {
        final List<String> addressLines = stationAddress.split('\n');
        for (final String line in addressLines) {
          if (line.trim().isNotEmpty) {
            await printer.printText('${line.trim()}\n');
          }
        }
      } else {
        
        await printer.printText('JL.RAYA MERUYA NO. 26\n');
        
      }
      await printer.printText('\n');

      // === 3. TRANSACTION INFO ===
      await printer.setAlignment(PrintAlignment.left);
      await printer.setFontSize(bodyFontSize);
      
      final String invoiceNumber = _getInvoiceNumber(order);
      await printer.printText('Invoice No:$invoiceNumber\n');

      final DateTime? transactionDateTime = _getTransactionDateTime(order);
      final String dateStr = transactionDateTime != null 
          ? _formatDateTimeWithTimezone(transactionDateTime)
          : _formatDateTimeWithTimezone(order.createTime);
      await printer.printText('Date: $dateStr\n');

      final String pumpNumber = _extractPumpNumber(order);
      await printer.printText('Pump No: $pumpNumber\n');

      // 使用新的字段获取客户信息
      // 客户名字使用 customer_id
      final String customerName = order.customerName?.toString() ?? '';
      final String customerPhone = order.customerPhone ?? '';

      // 显示客户名字（如果不为空且不是默认值）
      if (customerName.isNotEmpty) {
        await printer.printText('$customerName\n');
      }

      
      // 显示客户电话（如果不为空且不是默认值）
      if (customerPhone.isNotEmpty) {
        final String maskedPhone = _formatPhoneNumber(customerPhone);
        await printer.printText('Telepon: $maskedPhone\n');
      }


      // 显示车牌号（如果 order.licensePlate 不为空）
      if (order.licensePlate != null && order.licensePlate!.isNotEmpty) {
        await printer.printText('NomorKendaraan: ${order.licensePlate!}\n');
      }
      
      await printer.printText('\n');

      // === 4. PRODUCT TABLE ===
      await printer.setAlignment(PrintAlignment.center);
      
      // 产品表格列宽设置 - 调整为[10, 5, 7, 10]
      final List<int> productColumnWidths = [10, 5, 6, 11];
      
      await printer.printColumnsText(
        <String>['Product', 'Qty', 'Price', 'Amount'],
        productColumnWidths,
        <int>[TableAlignment.center, TableAlignment.center, TableAlignment.center, TableAlignment.center],
      );
      await printer.printText('$separator\n');

      // Product details
      final Map<String, dynamic> productInfo = _getProductInfo(order);
      final String volumeStr = _formatVolumeString(productInfo['volume'].toString());
      final String priceStr = currencyFormat.format(productInfo['price']).replaceAll(',', '.');
      final String amountStr = currencyFormat.format(productInfo['amount']).replaceAll(',', '.');
      
      await printer.printColumnsText(
        <String>[productInfo['name'].toString(), volumeStr, priceStr, amountStr],
        productColumnWidths,
        <int>[TableAlignment.left, TableAlignment.center, TableAlignment.right, TableAlignment.center],
      );

      // === 5. PROMOTION ===
      final String promotionName = _getPromotionName(order);
      final Map<String, double> discounts = _getDiscountBreakdown(order);
      
      if (promotionName.isNotEmpty && (discounts['fuel']! > 0 || discounts['other']! > 0)) {
        final double totalDiscount = discounts['fuel']! + discounts['other']!;
        final String discountStr = currencyFormat.format(totalDiscount).replaceAll(',', '.');
        
        // 促销信息表格列宽设置
        final List<int> promotionColumnWidths = [15, 15];
        
        await printer.printColumnsText(
          <String>[promotionName, '-Rp $discountStr'],
          promotionColumnWidths,
          <int>[TableAlignment.left, TableAlignment.right],
        );
      }

      await printer.printText('$separator\n');

      // === 6. TOTALS ===  Tera 不需要这一部分
      final String paymentMethod = _getPaymentMethodTextFromOrder(order);

      if (paymentMethod.toUpperCase() != 'TERA') {
        await printer.setAlignment(PrintAlignment.left);
        
        // 总计部分 - 使用空格填充实现左右对齐
        const int totalWidth = 32; // 58mm纸张大约32个字符宽度
        
        String totalAmount = 'Rp ${currencyFormat.format(order.amount).replaceAll(',', '.')}';
        String totalLine = 'Total:' + ' ' * (totalWidth - 'Total:'.length - totalAmount.length) + totalAmount;
        await printer.printText('$totalLine\n');

        if (discounts['fuel']! > 0) {
          String fuelDiscountAmount = 'Rp ${currencyFormat.format(discounts['fuel']).replaceAll(',', '.')}';
          String fuelDiscountLine = 'Discount (Fuel):' + ' ' * (totalWidth - 'Discount (Fuel):'.length - fuelDiscountAmount.length) + fuelDiscountAmount;
          await printer.printText('$fuelDiscountLine\n');
        }

        if (discounts['other']! > 0) {
          String otherDiscountAmount = 'Rp ${currencyFormat.format(discounts['other']).replaceAll(',', '.')}';
          String otherDiscountLine = 'Discount (Other):' + ' ' * (totalWidth - 'Discount (Other):'.length - otherDiscountAmount.length) + otherDiscountAmount;
          await printer.printText('$otherDiscountLine\n');
        }

        String netAmount = 'Rp ${currencyFormat.format(order.finalAmount).replaceAll(',', '.')}';
        String netAmountLine = 'Net Amount:' + ' ' * (totalWidth - 'Net Amount:'.length - netAmount.length) + netAmount;
        await printer.printText('$netAmountLine\n');

        String paymentAmount = 'Rp ${currencyFormat.format(order.finalAmount).replaceAll(',', '.')}';
        String paymentLine = paymentMethod + ' ' * (totalWidth - paymentMethod.length - paymentAmount.length) + paymentAmount;
        await printer.printText('$paymentLine\n');

        String quantityValue = '${productInfo['volume']}L';
        String quantityLine = 'Total Quantity:' + ' ' * (totalWidth - 'Total Quantity:'.length - quantityValue.length) + quantityValue;
        await printer.printText('$quantityLine\n');

        await printer.printText('$separator\n');
      } else {

        // 标识 Tera(不需要收钱，因此不需要小记等 )
        await printer.printText('TERA\n');
        await printer.printText('$separator\n');
      }

      // === 7. FOOTER ===
      await printer.setAlignment(PrintAlignment.center);
      await printer.setFontSize(headerFontSize);
      await printer.printText('TERIMA KASIH\n');

      await printer.setFontSize(smallFontSize);
      await printer.printText('Untuk promo, cari tahu melalui\n');
      await printer.printText('WhatsApp WApp di nomor\n');
      await printer.printText('081119960646\n\n');

      await printer.printText('E & OE, No refund\n');
      await printer.printText(isReprint ? 'Salinan Ulang Pelanggan\n' : 'Salinan Pelanggan\n');

      await printer.lineWrap(3);
      await printer.exitPrinterBuffer(commit: true);

    } catch (e) {
      await printer.exitPrinterBuffer(commit: false);
      rethrow;
    }
  }

  /// 获取发票号码
  static String _getInvoiceNumber(Order order) {
    final Map<String, dynamic>? extInfo = order.extInfo;
    if (extInfo != null) {
      final Map<String, dynamic>? metadata = extInfo['metadata'] as Map<String, dynamic>?;
      if (metadata != null) {
        final String? orderSerialNo = metadata['order_serial_no'] as String?;
        if (orderSerialNo != null && orderSerialNo.isNotEmpty) {
          return orderSerialNo;
        }
      }
      
      final String? orderSerialNo = extInfo['order_serial_no'] as String?;
      if (orderSerialNo != null && orderSerialNo.isNotEmpty) {
        return orderSerialNo;
      }
    }
    
    return order.orderId.isNotEmpty ? order.orderId : 'ORDER-${order.id}';
  }

  /// 获取泵号
  static String _extractPumpNumber(Order order) {
    final Map<String, dynamic>? extInfo = order.extInfo;
    if (extInfo != null) {
      final Map<String, dynamic>? metadata = extInfo['metadata'] as Map<String, dynamic>?;
      if (metadata != null) {
        final dynamic pumpNo = metadata['pump_no'];
        if (pumpNo != null) {
          return pumpNo.toString();
        }
      }
      
      final dynamic pumpNo = extInfo['pump_no'];
      if (pumpNo != null) {
        return pumpNo.toString();
      }
    }
    
    if (order.pumpId.isNotEmpty) {
      final RegExp numberRegex = RegExp(r'\d+');
      final RegExpMatch? match = numberRegex.firstMatch(order.pumpId);
      if (match != null) {
        return match.group(0)!;
      }
      return order.pumpId;
    }
    
    return 'N/A';
  }

  /// 格式化电话号码 - 脱敏处理
  /// 前5个字符为*，后5个数字取电话号码的后5位
  /// 例如：081234567890 → *****67890
  static String _formatPhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) return '';
    
    // 移除所有非数字字符
    final String digitsOnly = phoneNumber.replaceAll(RegExp(r'[^0-9]'), '');
    
    if (digitsOnly.length < 5) {
      // 如果电话号码少于5位，全部用*代替
      return '*' * digitsOnly.length;
    }
    
    // 前5个字符为*，后5个数字取电话号码的后5位
    final String lastFiveDigits = digitsOnly.substring(digitsOnly.length - 5);
    return '*****$lastFiveDigits';
  }

  /// 公共方法：格式化电话号码（供测试使用）
  static String formatPhoneNumber(String phoneNumber) {
    return _formatPhoneNumber(phoneNumber);
  }

  /// 获取客户信息
  static Map<String, String> _getCustomerInfo(Order order) {
    // 优先从 member_info 获取客户信息
    final Map<String, dynamic>? memberInfo = order.extInfo['member_info'] as Map<String, dynamic>?;
    
    String customerName = '';
    String customerPhone = '';
    
    if (memberInfo != null && memberInfo.isNotEmpty) {
      final String? name = memberInfo['name'] as String?;
      final String? phone = memberInfo['phone'] as String?;
      
      if (name != null && name.isNotEmpty) {
        customerName = name;
      }
      
      if (phone != null && phone.isNotEmpty) {
        customerPhone = phone;
      }
    }
    
    // 如果 member_info 中没有有效信息，尝试从 order 直接获取
    if (customerName.isEmpty && order.customerName != null && order.customerName!.isNotEmpty) {
      customerName = order.customerName!;
    }

    if (customerPhone.isEmpty && order.memberPhone.isNotEmpty) {
      customerPhone = order.memberPhone;
    }

    // 如果客户名字和电话都为空，根据支付方式设置默认值
    if (customerName.isEmpty && customerPhone.isEmpty) {
      final String paymentMethod = order.paymentMethod.toLowerCase();

      if (paymentMethod.contains('tera')) {
        // TERA 支付方式
        customerName = 'ANONIM';
        customerPhone = '1010101010';
      } else {
        // 其他支付方式
        customerName = 'ANONIM';
        customerPhone = '1010101010';
      }
    }

    return {
      'name': order.customerId?.toString() ?? '',
      'phone': order.memberPhone ?? '',
    };
  }

  /// 获取车辆ID
  static String _getVehicleId(Order order, String? vehiclePlate) {
    if (vehiclePlate != null && vehiclePlate.isNotEmpty) {
      return vehiclePlate;
    }
    
    // if (order.customerName != null && order.customerName!.isNotEmpty) {
    //   final RegExp plateInNameRegex = RegExp(r'Customer\s+([A-Z0-9]+)');
    //   final RegExpMatch? match = plateInNameRegex.firstMatch(order.customerName!);
    //   if (match != null) {
    //     final String extractedPlate = match.group(1)!;
    //     if (extractedPlate.isNotEmpty && extractedPlate != 'B1505RKV') {
    //       return extractedPlate;
    //     }
    //   }
    // }
    
    final Map<String, dynamic>? extInfo = order.extInfo;
    if (extInfo != null) {
      final Map<String, dynamic>? memberInfo = extInfo['member_info'] as Map<String, dynamic>?;
      if (memberInfo != null) {
        final List<dynamic>? plateNumbers = memberInfo['plateNumbers'] as List<dynamic>?;
        if (plateNumbers != null && plateNumbers.isNotEmpty) {
          final String plateNumber = plateNumbers.first.toString();
          if (plateNumber.isNotEmpty && plateNumber != 'B1505RKV') {
            return plateNumber;
          }
        }
      }
    }
    
    return 'UNKNOWN VEHICLE';
  }

  /// 获取产品信息
  static Map<String, dynamic> _getProductInfo(Order order) {
    for (final Map<String, dynamic> item in order.items) {
      final String productType = item['product_type'] as String? ?? '';
      if (productType.toLowerCase() == 'fuel') {
        final String rawProductName = item['product_name'] as String? ?? 'BP 92';
        final String mappedProductName = _mapFuelProductName(rawProductName);
        return {
          'name': mappedProductName,
          'volume': (item['quantity'] as num?)?.toDouble().toStringAsFixed(3) ?? '0.000',
          'price': (item['unit_price'] as num?)?.toDouble() ?? 0.0,
          'amount': (item['total_price'] as num?)?.toDouble() ?? 0.0,
        };
      }
    }
    
    final String mappedProductName = _mapFuelProductName('BP 92');
    return {
      'name': mappedProductName,
      'volume': order.volume.toStringAsFixed(3),
      'price': order.volume > 0 ? (order.amount / order.volume).round() : 0.0,
      'amount': order.amount,
    };
  }

  /// 映射油品名称
  static String _mapFuelProductName(String productName) {
    final String upperName = productName.toUpperCase();
    
    // 映射规则：只有3种油品，删除BP后面的空格
    if (upperName.contains('BP 92') || upperName.contains('92')) {
      return 'BP92';
    } else if (upperName.contains('BP ULTIMATE DIESEL') || upperName.contains('ULTIMATE DIESEL')) {
      return 'BPULTIMATEDIESEL';
    } else if (upperName.contains('BP ULTIMATE') || upperName.contains('ULTIMATE')) {
      return 'BPULTIMATE';
    }
    
    // 默认返回去除空格的原名称
    return upperName.replaceAll(' ', '');
  }

  /// 格式化体积字符串
  /// 根据不同数值范围进行格式化：
  /// - volume >= 100: 数字前一个空格，如 " 123.456"
  /// - 10 <= volume < 100: 数字前一个空格，小数点前2个空格，如 " 45  .678"
  /// - volume < 10: 保持原格式，如 "5.678"
  static String _formatVolumeString(String volumeStr) {
    try {
      final double volume = double.parse(volumeStr);
      
      if (volume >= 100) {
        // volume >= 100: 数字前一个空格
        final List<String> parts = volumeStr.split('.');
        final String integerPart = parts[0];
        final String decimalPart = parts.length > 1 ? parts[1] : '000';
        
        // 确保小数部分至少有3位
        final String paddedDecimal = decimalPart.padRight(3, '0').substring(0, 3);
        
        return ' $integerPart.$paddedDecimal';
      } else if (volume >= 10) {
        // 10 <= volume < 100: 数字前一个空格，小数点前2个空格
        final List<String> parts = volumeStr.split('.');
        final String integerPart = parts[0];
        final String decimalPart = parts.length > 1 ? parts[1] : '000';
        
        // 确保小数部分至少有3位
        final String paddedDecimal = decimalPart.padRight(3, '0').substring(0, 3);
        
        return ' $integerPart  .$paddedDecimal';
      } else {
        // volume < 10: 保持原格式
        return volumeStr;
      }
    } catch (e) {
      // 解析失败时返回原字符串
      return volumeStr;
    }
  }

  /// 获取促销名称
  static String _getPromotionName(Order order) {
    // 优先从订单项的促销信息中获取促销名称
    for (final Map<String, dynamic> item in order.items) {
      final Map<String, dynamic>? itemMetadata = item['metadata'] as Map<String, dynamic>?;
      if (itemMetadata != null) {
        final List<dynamic>? appliedPromotions = itemMetadata['applied_promotions'] as List<dynamic>?;
        
        if (appliedPromotions != null && appliedPromotions.isNotEmpty) {
          for (final dynamic promotionData in appliedPromotions) {
            if (promotionData is Map<String, dynamic>) {
              final String? promotionName = promotionData['promotion_name'] as String?;
              if (promotionName != null && promotionName.isNotEmpty) {
                return promotionName;
              }
            }
          }
        }
      }
    }
    
    // 兼容旧的促销信息格式
    if (order.promotions.isNotEmpty) {
      final Map<String, dynamic> promotion = order.promotions.first;
      final String? name = promotion['name'] as String?;
      if (name != null && name.isNotEmpty) {
        return name;
      }
    }
    
    final Map<String, dynamic>? metadata = order.extInfo;
    if (metadata != null) {
      final String? promotionType = metadata['promotion_type'] as String?;
      if (promotionType != null && promotionType.isNotEmpty && promotionType != 'none') {
        return promotionType;
      }
    }
    
    return order.discountAmount > 0 ? 'DISCOUNT' : '';
  }

  /// 获取折扣分解
  static Map<String, double> _getDiscountBreakdown(Order order) {
    double fuelDiscount = 0.0;
    double otherDiscount = 0.0;
    
    final Map<String, dynamic>? metadata = order.extInfo;
    if (metadata != null && fuelDiscount == 0.0 && otherDiscount == 0.0) {
      final double? percentDiscount = (metadata['amount_Percent_Discount'] as num?)?.toDouble();
      final double? amountDiscount = (metadata['amount_Discount'] as num?)?.toDouble();
      
      if (percentDiscount != null && percentDiscount > 0) {
        fuelDiscount += percentDiscount;
      }
      
      if (amountDiscount != null && amountDiscount > 0) {
        otherDiscount += amountDiscount;
      }
    }
    
    // 如果仍然没有找到折扣信息，但订单有总折扣金额，将其归类为燃油折扣
    if (fuelDiscount == 0.0 && otherDiscount == 0.0 && order.discountAmount > 0) {
      fuelDiscount = order.discountAmount;
    }
    
    return {
      'fuel': fuelDiscount,
      'other': otherDiscount,
    };
  }

  /// 获取支付方式文本
  static String _getPaymentMethodTextFromOrder(Order order) {
    if (order.payments.isNotEmpty) {
      for (final Map<String, dynamic> payment in order.payments) {
        final String? paymentName = payment['payment_method_name'] as String?;
        if (paymentName != null && paymentName.isNotEmpty && !RegExp(r'^\d+$').hasMatch(paymentName)) {
          return _convertPaymentMethodName(paymentName);
        }
      }
    }
    
    return _getPaymentMethodText(order.paymentMethod);
  }

  /// 转换支付方式名称
  static String _convertPaymentMethodName(String paymentMethod) {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
      case 'tunai':
        return 'CASH';
      case 'card':
      case 'bank_card':
        return 'BANK CARD';
      case 'bca':
        return 'EDC BCA';
      case 'cimb':
        return 'EDC CIMB';
      case 'mandiri':
        return 'EDC MANDIRI';
      case 'pvs':
        return 'EDC PVS';
      case 'member':
        return 'MEMBER';
      case 'tera':
        return 'TERA';
      default:
        return paymentMethod.toUpperCase();
    }
  }

  /// 获取支付方式文本
  static String _getPaymentMethodText(String method) {
    if (RegExp(r'^\d+$').hasMatch(method)) {
      switch (method) {
        case '1':
          return 'TUNAI';
        case '2':
          return 'EDC BCA';
        case '3':
          return 'MEMBER';
        case '4':
          return 'TERA';
        case '5':
          return 'EDC CIMB';
        case '6':
          return 'EDC MANDIRI';
        case '7':
          return 'EDC PVS';
        case '9':
          return 'CASH';
        default:
          return 'UNKNOWN PAYMENT';
      }
    }
    
    switch (method.toLowerCase()) {
      case 'cash':
      case 'tunai':
        return 'TUNAI';
      case 'bca':
        return 'EDC BCA';
      case 'member':
        return 'MEMBER';
      case 'tera':
        return 'TERA';
      default:
        return method.toUpperCase();
    }
  }

  /// 获取交易日期时间
  static DateTime? _getTransactionDateTime(Order order) {
    final Map<String, dynamic>? extInfo = order.extInfo;
    if (extInfo != null) {
      final Map<String, dynamic>? metadata = extInfo['metadata'] as Map<String, dynamic>?;
      if (metadata != null) {
        final String? transactionDateTime = metadata['transaction_date_time'] as String?;
        if (transactionDateTime != null && transactionDateTime.isNotEmpty) {
          try {
            return DateTime.parse(transactionDateTime);
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    }
    
    return null;
  }

  /// 使用时区服务格式化日期时间
  static String _formatDateTimeWithTimezone(DateTime dateTime) {
    // 假设传入的 dateTime 是 UTC 时间（从API解析而来）
    // 创建临时的 TimezoneService 实例来处理时区转换
    final TimezoneService timezoneService = TimezoneService();
    
    // 简化处理：假设使用雅加达时区 (UTC+7)
    // 在静态方法中，我们不能使用实例变量，所以使用固定的时区偏移
    const int jakartaOffset = 7; // UTC+7 for Jakarta
    final DateTime localTime = dateTime.add(Duration(hours: jakartaOffset));
    
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(localTime);
  }

  /// 显示消息
  void _showMessage(BuildContext context, String message, {required bool isError}) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? BPColors.error : BPColors.success,
        duration: Duration(seconds: isError ? 3 : 2),
      ),
    );
  }

  /// 测试打印功能
  Future<bool> testPrint() async {
    try {
      if (!_isInitialized) await initialize();

      final PrinterStatus status = await _printerService.getPrinterStatus();
      if (status != PrinterStatus.normal) return false;

      await _printerService.enterPrinterBuffer(clearBuffer: true);

      try {
        await _printerService.setAlignment(PrintAlignment.center);
        
        // 测试logo打印
        try {
          await _printerService.printImageFromAssetsCustom('assets/images/bp_akr_logo_receipt.png', 2, width: 200);
          await _printerService.printText('\n');
        } catch (e) {
          await _printerService.setFontSize(32.0);
          await _printerService.printText('BP-AKR\n');
        }
        
        await _printerService.setFontSize(32.0);
        await _printerService.printText('=== TEST RECEIPT ===\n');
        await _printerService.printText('Auto Print Service\n');
        await _printerService.printText('Test Successful\n');
        await _printerService.printText('${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}\n');
        await _printerService.printText('====================\n');
        await _printerService.lineWrap(3);

        await _printerService.exitPrinterBuffer(commit: true);
        return true;
      } catch (e) {
        await _printerService.exitPrinterBuffer(commit: false);
        rethrow;
      }
    } catch (e) {
      return false;
    }
  }

  /// 获取服务状态
  Map<String, dynamic> getServiceInfo() {
    return <String, dynamic>{
      'service_name': 'Auto Print Service',
      'version': '1.0.0',
      'initialized': _isInitialized,
      'printer_service': _isInitialized ? 'Available' : 'Not Available',
    };
  }

  /// Order字段映射方法
  static String getCustomerNameAsVehicleId(Order order) {
    return _getVehicleId(order, order.customerName);
  }

  /// 获取客户信息：名字使用customer_id，电话使用member_phone
  static Map<String, String> getCustomerIdAsCustomerInfo(Order order) {
    return _getCustomerInfo(order);
  }

  static String getPaymentMethodText(String method) {
    return _getPaymentMethodText(method);
  }

  static String getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.created:
        return 'Created';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.cancelling:
        return 'Cancelling';
      case OrderStatus.failed:
        return 'Failed';
    }
  }
}
