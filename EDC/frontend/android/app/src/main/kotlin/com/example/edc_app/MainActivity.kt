package com.example.edc_app

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Bundle
import android.util.Base64
import android.util.Log
import com.sunmi.peripheral.printer.InnerResultCallback
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import sunmi.paylib.SunmiPayKernel

class MainActivity : FlutterActivity() {
    companion object {
        private const val TAG = "MainActivity"
        private const val CARD_CHANNEL = "com.example.edc_app/card"
    }
    
    private lateinit var sunmiPrintHelper: SunmiPrintHelper
    private lateinit var cardModule: CardModule
    private lateinit var cardMethodChannel: MethodChannel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 初始化打印机
        sunmiPrintHelper = SunmiPrintHelper.instance
        val success = sunmiPrintHelper.initPrinter(applicationContext)
        Log.d(TAG, "Printer initialization result: $success")
    }

    override fun onResume() {
        super.onResume()
        // 检查SDK连接状态，如果未连接则尝试重新连接
        if (!EdcApplication.instance.isConnectPaySDK()) {
             Log.w(TAG, "PaySDK not connected in onResume, attempting to reconnect.")
            EdcApplication.instance.bindPaySDKService()
        } else {
            Log.i(TAG, "PaySDK already connected in onResume.")
        }
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        // 在此处初始化 CardModule 实例
        Log.i(TAG, "=== Initializing Card Module ===")
        cardModule = CardModule(applicationContext)
        Log.d(TAG, "CardModule instance created: $cardModule")

        // 设置 MethodChannel
        Log.d(TAG, "Creating MethodChannel for card operations...")
        cardMethodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CARD_CHANNEL)
        Log.d(TAG, "MethodChannel created: $cardMethodChannel")

        Log.d(TAG, "Setting up MethodCallHandler...")
        cardMethodChannel.setMethodCallHandler(CardMethodChannel(cardModule))
        Log.d(TAG, "MethodCallHandler configured")

        // 将 MethodChannel 实例传递给 CardModule（如果它需要回调 Flutter）
        Log.d(TAG, "Linking MethodChannel to CardModule...")
        cardModule.setChannel(cardMethodChannel)
        Log.d(TAG, "CardModule-MethodChannel link established")

        Log.i(TAG, "✅ Flutter engine configured, card channels registered successfully")
        
        // 注册打印机 MethodChannel
        val CHANNEL = "com.example.edc_app/printer"
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            Log.d(TAG, "Method call received: ${call.method}")
            
            // Simplified callback for buffered commands
            val bufferedCommandCallback = object : InnerResultCallback() {
                override fun onRunResult(isSuccess: Boolean) {
                    // For buffered commands, we often succeed immediately on the Flutter side
                    // Errors handled by onRaiseException or commit/exit buffer results.
                    // Log potential issues if isSuccess is false, but don't block Flutter.
                    if (!isSuccess) {
                        Log.w(TAG, "Buffered command ${call.method} onRunResult reported failure, but continuing.")
                    }
                }
                override fun onReturnString(value: String?) { /* Ignore */ }
                override fun onRaiseException(code: Int, msg: String?) {
                    Log.e(TAG, "Buffered command ${call.method} exception: $code - $msg")
                    // We might still succeed on Flutter side, but log the error
                    // Final result depends on commit/exit buffer
                }
                override fun onPrintResult(code: Int, msg: String?) { /* Ignore */ }
            }
            
            // Callback for commands that return a single string via onReturnString
            val stringResultCallback = object : InnerResultCallback() {
                override fun onRunResult(isSuccess: Boolean) {
                     if (!isSuccess) { result.error("PRINTER_ERROR", "Failed to execute command ${call.method}", null) }
                 }
                override fun onReturnString(value: String?) { result.success(value) }
                override fun onRaiseException(code: Int, msg: String?) { 
                    Log.e(TAG, "${call.method} exception: $code - $msg")
                    result.error(code.toString(), msg ?: "Error in ${call.method}", null)
                 }
                override fun onPrintResult(code: Int, msg: String?) { /* Ignore */ }
            }
            
             // Callback for simple commands returning success/failure via onRunResult
            val simpleSuccessCallback = object : InnerResultCallback() {
                 override fun onRunResult(isSuccess: Boolean) { 
                     if(isSuccess) { result.success(true) } else { result.error("PRINTER_ERROR", "Command ${call.method} failed", null) } 
                 }
                 override fun onReturnString(value: String?) { /* Ignore */ }
                 override fun onRaiseException(code: Int, msg: String?) {
                     Log.e(TAG, "${call.method} exception: $code - $msg")
                     result.error(code.toString(), msg ?: "Error in ${call.method}", null)
                 }
                 override fun onPrintResult(code: Int, msg: String?) { /* Ignore */ }
             }

            try {
                when (call.method) {
                    // --- Basic Status --- 
                    "isPrinterConnected" -> result.success(sunmiPrintHelper.isPrinterConnected())
                    "getPrinterStatus" -> sunmiPrintHelper.getPrinterStatus(stringResultCallback) // Returns status code as String
                    "getPrinterSerialNo" -> sunmiPrintHelper.getPrinterSerialNo(stringResultCallback)
                    "getPrinterModal" -> sunmiPrintHelper.getPrinterModal(stringResultCallback)
                    "getPrinterVersion" -> sunmiPrintHelper.getPrinterVersion(stringResultCallback)
                    "getServiceVersion" -> sunmiPrintHelper.getServiceVersion(stringResultCallback)
                    "getPrintedLength" -> sunmiPrintHelper.getPrintedLength(stringResultCallback) // Returns length as String
                    "getPrinterPaper" -> sunmiPrintHelper.getPrinterPaper(stringResultCallback) // Returns type as String
                    "printerSelfChecking" -> sunmiPrintHelper.printerSelfChecking(simpleSuccessCallback)
                    "getPrinterMode" -> sunmiPrintHelper.getPrinterMode(stringResultCallback)
                    "getPrinterBBMDistance" -> sunmiPrintHelper.getPrinterBBMDistance(stringResultCallback)
                    "getCutPaperTimes" -> sunmiPrintHelper.getCutPaperTimes(stringResultCallback)
                    "getOpenDrawerTimes" -> sunmiPrintHelper.getOpenDrawerTimes(stringResultCallback)
                    "getDrawerStatus" -> sunmiPrintHelper.getDrawerStatus(stringResultCallback)

                    // --- ESC/POS --- 
                    "sendRAWData" -> {
                        val data = call.argument<ByteArray>("data")
                        if (data == null) { result.error("INVALID_ARGUMENT", "Raw data cannot be null", null); return@setMethodCallHandler }
                        sunmiPrintHelper.sendRAWData(data, simpleSuccessCallback)
                    }

                    // --- Printer Style --- 
                     "setPrinterStyle" -> {
                        val key = call.argument<Int>("key")
                        val value = call.argument<Int>("value")
                         if (key == null || value == null) { result.error("INVALID_ARGUMENT", "Style key/value cannot be null", null); return@setMethodCallHandler }
                        // This is buffered, but helper handles immediate success
                        sunmiPrintHelper.setPrinterStyle(key, value, bufferedCommandCallback)
                        result.success(true) 
                     }

                    // --- Buffered Commands --- 
                    "printText" -> {
                        val text = call.argument<String>("text")
                        if (text == null) { result.error("INVALID_ARGUMENT", "Text cannot be null", null); return@setMethodCallHandler }
                        sunmiPrintHelper.printText(text, bufferedCommandCallback)
                        result.success(true) // Success: Command buffered
                    }
                    "setAlignment" -> {
                        val alignment = call.argument<Int>("alignment")
                        if (alignment == null) { result.error("INVALID_ARGUMENT", "Alignment cannot be null", null); return@setMethodCallHandler }
                        sunmiPrintHelper.setAlignment(alignment, bufferedCommandCallback)
                        result.success(true) // Success: Command buffered
                    }
                     "setFontName" -> {
                        val typeface = call.argument<String>("typeface")
                        if (typeface == null) { result.error("INVALID_ARGUMENT", "Typeface cannot be null", null); return@setMethodCallHandler }
                        sunmiPrintHelper.setFontName(typeface, bufferedCommandCallback)
                         result.success(true)
                     }
                    "setFontSize" -> {
                        val size = call.argument<Double>("size")?.toFloat()
                        if (size == null) { result.error("INVALID_ARGUMENT", "Font size cannot be null", null); return@setMethodCallHandler }
                        sunmiPrintHelper.setFontSize(size, bufferedCommandCallback)
                        result.success(true) // Success: Command buffered
                    }
                     "printTextWithFont" -> {
                        val text = call.argument<String>("text")
                        val typeface = call.argument<String>("typeface")
                        val fontSize = call.argument<Double>("fontSize")?.toFloat()
                        if (text == null || typeface == null || fontSize == null) { result.error("INVALID_ARGUMENT", "printTextWithFont arguments cannot be null", null); return@setMethodCallHandler }
                        sunmiPrintHelper.printTextWithFont(text, typeface, fontSize, bufferedCommandCallback)
                        result.success(true)
                     }
                     "printOriginalText" -> {
                        val text = call.argument<String>("text")
                        if (text == null) { result.error("INVALID_ARGUMENT", "Text cannot be null", null); return@setMethodCallHandler }
                        sunmiPrintHelper.printOriginalText(text, bufferedCommandCallback)
                        result.success(true)
                     }
                     "printColumnsText" -> {
                        val texts = call.argument<List<String>>("texts")?.toTypedArray()
                        val widths = call.argument<List<Int>>("widths")?.toIntArray()
                        val aligns = call.argument<List<Int>>("aligns")?.toIntArray()
                        if (texts == null || widths == null || aligns == null) { result.error("INVALID_ARGUMENT", "Columns arguments cannot be null", null); return@setMethodCallHandler }
                        sunmiPrintHelper.printColumnsText(texts, widths, aligns, bufferedCommandCallback)
                        result.success(true)
                     }
                     "printColumnsString" -> {
                        val texts = call.argument<List<String>>("texts")?.toTypedArray()
                        val widths = call.argument<List<Int>>("widths")?.toIntArray()
                        val aligns = call.argument<List<Int>>("aligns")?.toIntArray()
                        if (texts == null || widths == null || aligns == null) { result.error("INVALID_ARGUMENT", "Columns arguments cannot be null", null); return@setMethodCallHandler }
                        sunmiPrintHelper.printColumnsString(texts, widths, aligns, bufferedCommandCallback)
                        result.success(true)
                     }
                    "printBarCode" -> {
                        val data = call.argument<String>("data")
                        val symbology = call.argument<Int>("symbology")
                        val height = call.argument<Int>("height")
                        val width = call.argument<Int>("width")
                        val textPosition = call.argument<Int>("textPosition")
                        if (data == null || symbology == null || height == null || width == null || textPosition == null) {
                            result.error("INVALID_ARGUMENT", "Barcode arguments cannot be null", null)
                            return@setMethodCallHandler
                        }
                        sunmiPrintHelper.printBarCode(data, symbology, height, width, textPosition, bufferedCommandCallback)
                        result.success(true) // Success: Command buffered
                    }
                    "printQRCode" -> {
                        val data = call.argument<String>("data")
                        val moduleSize = call.argument<Int>("moduleSize")
                        val errorLevel = call.argument<Int>("errorLevel")
                        if (data == null || moduleSize == null || errorLevel == null) {
                            result.error("INVALID_ARGUMENT", "QR code arguments cannot be null", null)
                            return@setMethodCallHandler
                        }
                        sunmiPrintHelper.printQRCode(data, moduleSize, errorLevel, bufferedCommandCallback)
                        result.success(true) // Success: Command buffered
                    }
                     "print2DCode" -> {
                        val data = call.argument<String>("data")
                        val symbology = call.argument<Int>("symbology")
                        val moduleSize = call.argument<Int>("moduleSize")
                        val errorLevel = call.argument<Int>("errorLevel")
                        if (data == null || symbology == null || moduleSize == null || errorLevel == null) { result.error("INVALID_ARGUMENT", "2D code arguments cannot be null", null); return@setMethodCallHandler }
                         sunmiPrintHelper.print2DCode(data, symbology, moduleSize, errorLevel, bufferedCommandCallback)
                        result.success(true)
                     }
                     "printBitmap" -> {
                        val base64Bitmap = call.argument<String>("bitmap")
                        if (base64Bitmap == null) { result.error("INVALID_ARGUMENT", "Bitmap data cannot be null", null); return@setMethodCallHandler }
                        try {
                            val bitmapBytes = Base64.decode(base64Bitmap, Base64.DEFAULT)
                            val bitmap = BitmapFactory.decodeByteArray(bitmapBytes, 0, bitmapBytes.size)
                            if (bitmap != null) {
                                sunmiPrintHelper.printBitmap(bitmap, bufferedCommandCallback)
                                result.success(true)
                            } else {
                                result.error("INVALID_BITMAP", "Failed to decode bitmap data", null)
                            }
                        } catch (e: IllegalArgumentException) {
                            result.error("INVALID_BITMAP", "Invalid Base64 bitmap data", e.message)
                        }
                    }
                     "printBitmapCustom" -> {
                        val base64Bitmap = call.argument<String>("bitmap")
                        val type = call.argument<Int>("type")
                        if (base64Bitmap == null || type == null) { result.error("INVALID_ARGUMENT", "Bitmap data or type cannot be null", null); return@setMethodCallHandler }
                        try {
                            val bitmapBytes = Base64.decode(base64Bitmap, Base64.DEFAULT)
                            val bitmap = BitmapFactory.decodeByteArray(bitmapBytes, 0, bitmapBytes.size)
                             if (bitmap != null) {
                                sunmiPrintHelper.printBitmapCustom(bitmap, type, bufferedCommandCallback)
                                result.success(true)
                            } else {
                                result.error("INVALID_BITMAP", "Failed to decode bitmap data", null)
                            }
                        } catch (e: IllegalArgumentException) {
                            result.error("INVALID_BITMAP", "Invalid Base64 bitmap data", e.message)
                        }
                     }
                    "lineWrap" -> {
                        val lines = call.argument<Int>("n") ?: 1 // Match argument name from Dart
                        sunmiPrintHelper.lineWrap(lines, bufferedCommandCallback)
                        result.success(true) // Success: Command buffered
                    }
                    "cutPaper" -> {
                        sunmiPrintHelper.cutPaper(bufferedCommandCallback)
                        result.success(true) // Return success, handle error via exception if device doesn't support
                    }
                    "openDrawer" -> sunmiPrintHelper.openDrawer(simpleSuccessCallback)
                    "labelLocate" -> sunmiPrintHelper.labelLocate(simpleSuccessCallback)
                    "labelOutput" -> sunmiPrintHelper.labelOutput(simpleSuccessCallback)

                    // --- Transaction Control --- 
                    "enterPrinterBuffer" -> {
                        val clearBuffer = call.argument<Boolean>("clearBuffer") ?: true
                        // enterPrinterBuffer in helper already calls callback.onRunResult(true)
                        // We can rely on that or just return success here immediately as well.
                        sunmiPrintHelper.enterPrinterBuffer(clearBuffer, object : InnerResultCallback() {
                            override fun onRunResult(isSuccess: Boolean) {
                                Log.d(TAG, "enterPrinterBuffer onRunResult: $isSuccess")
                                // This callback from the helper might be synchronous. Return success.
                                if (isSuccess) {
                                    result.success(true)
                                } else {
                                    // If helper's direct call fails, report error
                                    result.error("PRINTER_ERROR", "Failed to enter printer buffer", null)
                                }
                            }
                            override fun onReturnString(value: String?) {}
                            override fun onRaiseException(code: Int, msg: String?) {
                                Log.e(TAG, "enterPrinterBuffer exception: $code - $msg")
                                result.error(code.toString(), msg ?: "Error entering buffer", null)
                            }
                            override fun onPrintResult(code: Int, msg: String?) {}
                        })
                    }
                    "commitPrinterBuffer" -> {
                        // This needs to wait for onPrintResult
                        sunmiPrintHelper.commitPrinterBuffer(object : InnerResultCallback() {
                            override fun onRunResult(isSuccess: Boolean) {
                                if (!isSuccess) {
                                    Log.e(TAG, "commitPrinterBuffer command failed")
                                    result.error("PRINTER_ERROR", "Commit printer buffer command failed", null)
                                }
                                // Wait for onPrintResult for actual outcome
                            }
                            override fun onReturnString(value: String?) {}
                            override fun onRaiseException(code: Int, msg: String?) {
                                Log.e(TAG, "commitPrinterBuffer exception: $code - $msg")
                                result.error(code.toString(), msg ?: "Unknown error committing buffer", null)
                            }
                            override fun onPrintResult(code: Int, msg: String?) {
                                Log.d(TAG, "commitPrinterBuffer print result: $code - $msg")
                                if (code == 0) {
                                    result.success(true) // Final print success
                                } else {
                                    result.error(code.toString(), msg ?: "Print failed after commit", null)
                                }
                            }
                        })
                    }
                    "exitPrinterBuffer" -> {
                        val commit = call.argument<Boolean>("commit") ?: false
                        // This needs to wait for onPrintResult if commit is true
                        sunmiPrintHelper.exitPrinterBuffer(commit, object : InnerResultCallback() {
                            override fun onRunResult(isSuccess: Boolean) {
                                if (!isSuccess && commit) {
                                    // If committing, error in command execution fails the process
                                     Log.e(TAG, "exitPrinterBuffer(commit=true) command failed")
                                     result.error("PRINTER_ERROR", "Exit printer buffer command failed", null)
                                } else if (!commit) {
                                    // If not committing, onRunResult determines success
                                     if (isSuccess) {
                                        result.success(true)
                                    } else {
                                         result.error("PRINTER_ERROR", "Failed to exit printer buffer (no commit)", null)
                                    }
                                }
                                // If commit=true and isSuccess=true, wait for onPrintResult
                            }
                            override fun onReturnString(value: String?) {}
                            override fun onRaiseException(code: Int, msg: String?) {
                                Log.e(TAG, "exitPrinterBuffer exception: $code - $msg")
                                result.error(code.toString(), msg ?: "Unknown error exiting buffer", null)
                            }
                            override fun onPrintResult(code: Int, msg: String?) {
                                if (commit) { // Only handle if we were committing
                                    Log.d(TAG, "exitPrinterBuffer print result: $code - $msg")
                                    if (code == 0) {
                                        result.success(true) // Final print success
                                    } else {
                                        result.error(code.toString(), msg ?: "Print failed on exit", null)
                                    }
                                } 
                            }
                        })
                    }
                    
                    // 其他方法...也可以根据需要添加
                    
                    else -> {
                        result.notImplemented()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing method call: ${call.method}", e)
                val stackTrace = Log.getStackTraceString(e)
                result.error("NATIVE_EXCEPTION", "Exception in MainActivity: ${e.message}", stackTrace)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 销毁打印机连接
        sunmiPrintHelper.destroyPrinter()
    }
}

