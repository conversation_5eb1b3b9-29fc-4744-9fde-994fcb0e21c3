import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../services/bos_transaction_polling_service.dart';
import '../services/transaction_state_sync_service.dart';
import '../controllers/dispenser_controller.dart';
import '../controllers/fuel_transaction_controller.dart';
import '../services/api/api_service.dart';
import '../models/fuel_transaction.dart';

/// 燃油交易同步调试工具
/// 用于诊断 fuel_transaction_api 到 dispenser_controller 的数据同步问题
class FuelTransactionSyncDebugger {
  FuelTransactionSyncDebugger(this._ref);
  
  final WidgetRef _ref;

  /// 执行完整的同步诊断
  Future<void> runFullDiagnosis() async {
    debugPrint('🔍 ========== 燃油交易同步诊断开始 ==========');
    
    // 1. 检查服务状态
    await _checkServiceStatus();
    
    // 2. 检查API连接
    await _checkApiConnection();
    
    // 3. 检查数据流
    await _checkDataFlow();
    
    // 4. 检查控制器状态
    _checkControllerStates();
    
    // 5. 手动触发同步测试
    await _testManualSync();
    
    debugPrint('🔍 ========== 燃油交易同步诊断结束 ==========');
  }

  /// 检查服务状态
  Future<void> _checkServiceStatus() async {
    debugPrint('\n📊 1. 检查服务状态...');
    
    try {
      // 检查BOS轮询服务
      final BosTransactionPollingService bosService = 
          _ref.read(bosTransactionPollingServiceProvider);
      debugPrint('✅ BOS轮询服务已获取');
      bosService.printDebugInfo();
      
    } catch (e) {
      debugPrint('❌ 获取BOS轮询服务失败: $e');
    }
  }

  /// 检查API连接
  Future<void> _checkApiConnection() async {
    debugPrint('\n🌐 2. 检查API连接...');
    
    try {
      final ApiService apiService = ApiService();
      
      // 测试获取燃油交易
      final FuelTransactionQueryParams testParams = FuelTransactionQueryParams(
        status: 'pending',
        page: 1,
        limit: 1,
      );
      
      debugPrint('🔄 测试API调用...');
      final FuelTransactionResponse response = 
          await apiService.fuelTransactionApi.getFuelTransactions(testParams);
      
      debugPrint('✅ API连接正常，返回 ${response.items.length} 条记录');
      debugPrint('   总记录数: ${response.total}');
      
      if (response.items.isNotEmpty) {
        final FuelTransaction firstTransaction = response.items.first;
        debugPrint('   示例交易: ${firstTransaction.transactionNumber}');
        debugPrint('   状态: ${firstTransaction.status}');
        debugPrint('   Nozzle ID: ${firstTransaction.nozzleId}');
      }
      
    } catch (e) {
      debugPrint('❌ API连接测试失败: $e');
    }
  }

  /// 检查数据流
  Future<void> _checkDataFlow() async {
    debugPrint('\n🔄 3. 检查数据流...');
    
    try {
      // 手动触发一次BOS轮询
      final BosTransactionPollingService bosService = 
          _ref.read(bosTransactionPollingServiceProvider);
      
      debugPrint('🔄 手动触发BOS轮询...');
      await bosService.pollOnce();
      
      debugPrint('✅ BOS轮询完成');
      
    } catch (e) {
      debugPrint('❌ 数据流检查失败: $e');
    }
  }

  /// 检查控制器状态
  void _checkControllerStates() {
    debugPrint('\n🎮 4. 检查控制器状态...');
    
    try {
      // 检查Dispenser控制器
      final DispenserState dispenserState = _ref.read(dispenserControllerProvider);
      debugPrint('✅ Dispenser控制器状态:');
      debugPrint('   连接状态: ${dispenserState.isConnected}');
      debugPrint('   加载状态: ${dispenserState.isLoading}');
      debugPrint('   错误信息: ${dispenserState.errorMessage ?? "无"}');
      debugPrint('   Nozzle数量: ${dispenserState.nozzleStatusMap.length}');
      debugPrint('   完成交易数量: ${dispenserState.completedTransactions.length}');
      debugPrint('   BOS保护Nozzle数量: ${dispenserState.bosProtectedNozzles.length}');
      
      // 检查燃油交易控制器
      final FuelTransactionState fuelTransactionState = 
          _ref.read(fuelTransactionControllerProvider);
      debugPrint('✅ 燃油交易控制器状态:');
      debugPrint('   加载状态: ${fuelTransactionState.isLoading}');
      debugPrint('   错误信息: ${fuelTransactionState.errorMessage ?? "无"}');
      debugPrint('   交易数量: ${fuelTransactionState.transactions.length}');
      debugPrint('   选择状态: ${fuelTransactionState.selectedStatus ?? "全部"}');
      
    } catch (e) {
      debugPrint('❌ 控制器状态检查失败: $e');
    }
  }

  /// 测试手动同步
  Future<void> _testManualSync() async {
    debugPrint('\n🧪 5. 测试手动同步...');
    
    try {
      // 获取当前pending交易
      final ApiService apiService = ApiService();
      final FuelTransactionQueryParams params = FuelTransactionQueryParams(
        status: 'pending',
        page: 1,
        limit: 5,
      );
      
      final FuelTransactionResponse response = 
          await apiService.fuelTransactionApi.getFuelTransactions(params);
      
      if (response.items.isEmpty) {
        debugPrint('📭 没有pending交易可供测试');
        return;
      }
      
      debugPrint('🔄 找到 ${response.items.length} 条pending交易，开始手动同步测试...');
      
      // 这里我们无法直接创建TransactionStateSyncService，因为它需要WidgetRef
      // 但我们可以检查回调是否被正确设置
      final BosTransactionPollingService bosService = 
          _ref.read(bosTransactionPollingServiceProvider);
      
      if (bosService.onTransactionsFound != null) {
        debugPrint('✅ BOS服务的onTransactionsFound回调已设置');
        debugPrint('🔄 模拟回调调用...');
        bosService.onTransactionsFound!(response.items);
        debugPrint('✅ 回调调用完成');
      } else {
        debugPrint('❌ BOS服务的onTransactionsFound回调未设置！');
      }
      
    } catch (e) {
      debugPrint('❌ 手动同步测试失败: $e');
    }
  }

  /// 快速状态检查（简化版）
  void quickStatusCheck() {
    debugPrint('🔍 ========== 快速状态检查 ==========');
    
    try {
      final BosTransactionPollingService bosService = 
          _ref.read(bosTransactionPollingServiceProvider);
      final Map<String, dynamic> status = bosService.getServiceStatus();
      
      debugPrint('📊 BOS轮询服务状态:');
      debugPrint('   轮询状态: ${status['is_polling']}');
      debugPrint('   交易轮询间隔: ${status['transaction_polling_interval_seconds']}秒');
      debugPrint('   已处理交易数: ${status['processed_transactions_count']}');
      debugPrint('   回调设置状态: ${bosService.onTransactionsFound != null ? "已设置" : "未设置"}');
      
      final DispenserState dispenserState = _ref.read(dispenserControllerProvider);
      debugPrint('📊 Dispenser状态:');
      debugPrint('   连接状态: ${dispenserState.isConnected}');
      debugPrint('   Nozzle数量: ${dispenserState.nozzleStatusMap.length}');
      debugPrint('   完成交易数量: ${dispenserState.completedTransactions.length}');
      
    } catch (e) {
      debugPrint('❌ 快速状态检查失败: $e');
    }
    
    debugPrint('🔍 ========== 快速状态检查结束 ==========');
  }

  /// 检查特定Nozzle的状态
  void checkNozzleStatus(String nozzleId) {
    debugPrint('🔍 检查Nozzle $nozzleId 状态:');
    
    try {
      final DispenserState state = _ref.read(dispenserControllerProvider);
      final Nozzle? nozzle = state.nozzleStatusMap[nozzleId];
      
      if (nozzle != null) {
        debugPrint('✅ Nozzle找到:');
        debugPrint('   状态: ${nozzle.status.name}');
        debugPrint('   当前体积: ${nozzle.currentVolume}L');
        debugPrint('   当前金额: ${nozzle.currentAmount}');
        debugPrint('   最后更新: ${nozzle.lastUpdateTime}');
        
        // 检查是否有完成的交易
        final List<FuelTransaction>? completedTransactions = 
            state.completedTransactions[nozzleId];
        if (completedTransactions != null && completedTransactions.isNotEmpty) {
          debugPrint('   完成交易数: ${completedTransactions.length}');
          for (final FuelTransaction tx in completedTransactions) {
            debugPrint('     - ${tx.transactionNumber}: ${tx.volume}L, ${tx.amount}');
          }
        } else {
          debugPrint('   完成交易数: 0');
        }
        
        // 检查BOS保护状态
        final DateTime? bosProtection = state.bosProtectedNozzles[nozzleId];
        if (bosProtection != null) {
          final Duration remaining = bosProtection.difference(DateTime.now());
          debugPrint('   BOS保护: 剩余${remaining.inSeconds}秒');
        } else {
          debugPrint('   BOS保护: 无');
        }
        
      } else {
        debugPrint('❌ Nozzle $nozzleId 未找到');
      }
      
    } catch (e) {
      debugPrint('❌ 检查Nozzle状态失败: $e');
    }
  }
}
