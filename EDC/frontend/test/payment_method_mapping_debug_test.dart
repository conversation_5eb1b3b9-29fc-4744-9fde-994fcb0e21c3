import 'package:flutter_test/flutter_test.dart';
import 'package:edc_app/models/shift_attendant_model.dart';

void main() {
  group('Payment Method Mapping Debug Tests', () {
    test('Debug: Track payment_method_name mapping process step by step', () {
      print('\n=== 开始测试支付方式映射过程 ===');
      
      // 步骤1: 模拟API返回的原始数据
      final Map<String, dynamic> rawApiData = {
        'payment_method': 'cash',
        'payment_method_name': '现金',
        'total_amount': 100000.0,
        'transaction_count': 5,
        'percentage': 50.0,
      };
      
      print('\n步骤1: API返回的原始数据');
      print('  - payment_method: ${rawApiData['payment_method']}');
      print('  - payment_method_name: ${rawApiData['payment_method_name']} (原始中文)');
      print('  - total_amount: ${rawApiData['total_amount']}');
      print('  - transaction_count: ${rawApiData['transaction_count']}');
      print('  - percentage: ${rawApiData['percentage']}');
      
      // 步骤2: 调用fromJson方法
      print('\n步骤2: 调用 PaymentMethodData.fromJson()');
      print('  - 开始解析JSON数据...');
      
      final PaymentMethodData paymentMethod = PaymentMethodData.fromJson(rawApiData);
      
      print('  - fromJson() 调用完成');
      
      // 步骤3: 验证映射结果
      print('\n步骤3: 验证映射结果');
      print('  - paymentMethod: ${paymentMethod.paymentMethod}');
      print('  - paymentMethodName: ${paymentMethod.paymentMethodName} (映射后英文)');
      print('  - totalAmount: ${paymentMethod.totalAmount}');
      print('  - transactionCount: ${paymentMethod.transactionCount}');
      print('  - percentage: ${paymentMethod.percentage}');
      
      // 步骤4: 验证映射是否正确
      print('\n步骤4: 验证映射是否正确');
      expect(paymentMethod.paymentMethod, equals('cash'));
      expect(paymentMethod.paymentMethodName, equals('Cash'));
      expect(paymentMethod.totalAmount, equals(100000.0));
      expect(paymentMethod.transactionCount, equals(5));
      expect(paymentMethod.percentage, equals(50.0));
      
      print('  ✅ 所有验证通过！');
      print('  ✅ "现金" 成功映射为 "Cash"');
      
      print('\n=== 测试完成 ===\n');
    });

    test('Debug: Test multiple payment methods mapping', () {
      print('\n=== 测试多种支付方式映射 ===');
      
      final List<Map<String, dynamic>> testCases = [
        {
          'input': '现金',
          'expected': 'Cash',
          'description': '现金支付'
        },
        {
          'input': '银行卡',
          'expected': 'Bank Card',
          'description': '银行卡支付'
        },
        {
          'input': '信用卡',
          'expected': 'Credit Card',
          'description': '信用卡支付'
        },
        {
          'input': '借记卡',
          'expected': 'Debit Card',
          'description': '借记卡支付'
        },
        {
          'input': '电子钱包',
          'expected': 'E-Wallet',
          'description': '电子钱包支付'
        },
        {
          'input': '代金券',
          'expected': 'Voucher',
          'description': '代金券支付'
        },
        {
          'input': '车队卡',
          'expected': 'Fleet Card',
          'description': '车队卡支付'
        },
        {
          'input': 'Mandiri',
          'expected': 'Mandiri',
          'description': '已经是英文的支付方式'
        },
        {
          'input': 'Unknown Payment',
          'expected': 'Unknown Payment',
          'description': '未知支付方式'
        },
      ];

      for (int i = 0; i < testCases.length; i++) {
        final testCase = testCases[i];
        
        print('\n--- 测试案例 ${i + 1}: ${testCase['description']} ---');
        
        final Map<String, dynamic> testData = {
          'payment_method': 'test_${i + 1}',
          'payment_method_name': testCase['input'],
          'total_amount': (i + 1) * 10000.0,
          'transaction_count': i + 1,
          'percentage': (i + 1) * 10.0,
        };
        
        print('输入数据:');
        print('  - payment_method_name: "${testCase['input']}"');
        
        final PaymentMethodData paymentMethod = PaymentMethodData.fromJson(testData);
        
        print('映射结果:');
        print('  - paymentMethodName: "${paymentMethod.paymentMethodName}"');
        print('  - 预期结果: "${testCase['expected']}"');
        
        expect(
          paymentMethod.paymentMethodName,
          equals(testCase['expected']),
          reason: 'Failed to map ${testCase['input']} to ${testCase['expected']}',
        );
        
        if (paymentMethod.paymentMethodName == testCase['expected']) {
          print('  ✅ 映射成功！');
        } else {
          print('  ❌ 映射失败！');
        }
      }
      
      print('\n=== 多种支付方式映射测试完成 ===\n');
    });

    test('Debug: Test edge cases and error handling', () {
      print('\n=== 测试边界情况和错误处理 ===');
      
      // 测试空字符串
      print('\n--- 测试空字符串 ---');
      final Map<String, dynamic> emptyData = {
        'payment_method': 'test',
        'payment_method_name': '',
        'total_amount': 0.0,
        'transaction_count': 0,
        'percentage': 0.0,
      };
      
      print('输入: payment_method_name = ""');
      final PaymentMethodData emptyResult = PaymentMethodData.fromJson(emptyData);
      print('输出: paymentMethodName = "${emptyResult.paymentMethodName}"');
      expect(emptyResult.paymentMethodName, equals(''));
      print('✅ 空字符串处理正确');
      
      // 测试包含空格的字符串
      print('\n--- 测试包含空格的字符串 ---');
      final Map<String, dynamic> spaceData = {
        'payment_method': 'test',
        'payment_method_name': ' 现金 ',
        'total_amount': 0.0,
        'transaction_count': 0,
        'percentage': 0.0,
      };
      
      print('输入: payment_method_name = " 现金 "');
      final PaymentMethodData spaceResult = PaymentMethodData.fromJson(spaceData);
      print('输出: paymentMethodName = "${spaceResult.paymentMethodName}"');
      // 注意：当前实现不处理空格，会保持原样
      expect(spaceResult.paymentMethodName, equals(' 现金 '));
      print('✅ 包含空格的字符串保持原样');
      
      // 测试大小写敏感
      print('\n--- 测试大小写敏感 ---');
      final Map<String, dynamic> caseData = {
        'payment_method': 'test',
        'payment_method_name': '现金',
        'total_amount': 0.0,
        'transaction_count': 0,
        'percentage': 0.0,
      };
      
      print('输入: payment_method_name = "现金"');
      final PaymentMethodData caseResult = PaymentMethodData.fromJson(caseData);
      print('输出: paymentMethodName = "${caseResult.paymentMethodName}"');
      expect(caseResult.paymentMethodName, equals('Cash'));
      print('✅ 大小写敏感匹配正确');
      
      print('\n=== 边界情况测试完成 ===\n');
    });
  });
} 