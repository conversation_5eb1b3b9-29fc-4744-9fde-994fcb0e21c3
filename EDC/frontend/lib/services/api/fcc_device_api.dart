import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../models/fcc_device.dart';
import '../../constants/api_constants.dart';
import 'package:flutter/foundation.dart';

/// FCC Device API Service
///
/// Implements FCC Service V2 Device Management API endpoints
/// Handles communication with FCC system for device and nozzle operations
///
/// API Endpoints Covered:
/// - GET /api/v2/devices - List all devices
/// - GET /api/v2/devices/:id - Get device details
/// - PUT /api/v2/devices/:id - Update device
/// - GET /api/v2/devices/:id/nozzles - List device nozzles
/// - PUT /api/v2/devices/:id/nozzles/:number - Update nozzle
/// - POST /api/v2/devices/:id/authorize - Authorize nozzle
/// - POST /api/v2/devices/:id/stop - Stop device operations
/// - GET /api/v2/controllers - List controllers
/// - GET /api/v2/health - Service health check
class FCCDeviceApi {
  FCCDeviceApi({
    http.Client? httpClient,
    String? baseUrl,
  })  : _httpClient = httpClient ?? http.Client(),
        _baseUrl = baseUrl ?? _getDefaultFccUrl();
  final http.Client _httpClient;
  final String _baseUrl;

  // === Device Management APIs ===

  /// Get all devices
  Future<List<FCCDevice>> getDevices({
    String? stationId,
    String? islandId,
    FCCDeviceType? type,
    FCCDeviceStatus? status,
    int? limit,
    int? offset,
  }) async {
    try {
      final Map<String, String> queryParams = <String, String>{};
      if (stationId != null) queryParams['station_id'] = stationId;
      if (islandId != null) queryParams['island_id'] = islandId;
      if (type != null) queryParams['type'] = type.value;
      if (status != null) queryParams['status'] = status.value;
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();

      final Uri uri = Uri.parse('$_baseUrl/api/v2/devices').replace(
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      final http.Response response = await _httpClient.get(
        uri,
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        // 添加调试日志
        print('🔍 FCC API Response: $data');

        // 安全处理可能为null的devices字段
        final List deviceList =
            data['devices'] as List<dynamic>? ?? <dynamic>[];

        if (deviceList.isEmpty) {
          print('⚠️  No devices found in response');
          return <FCCDevice>[];
        }

        // 详细检查每个设备的价格数据
        for (final deviceJson in deviceList) {
          final deviceId = deviceJson['id'];
          final List nozzles =
              deviceJson['nozzles'] as List<dynamic>? ?? <dynamic>[];

          debugPrint('📡 设备 $deviceId: 包含 ${nozzles.length} 个nozzle');

          for (final nozzleJson in nozzles) {
            final nozzleId = nozzleJson['id'];
            final nozzleNumber = nozzleJson['nozzle_number'];
            final currentPrice = nozzleJson['current_price'];
            final fuelGrade = nozzleJson['fuel_grade_name'];

            if (currentPrice == null ||
                (currentPrice is num && currentPrice <= 0)) {
              debugPrint(
                  '❌ 价格数据异常: 设备$deviceId, Nozzle$nozzleId(#$nozzleNumber)');
              debugPrint('   燃油类型: $fuelGrade, current_price: $currentPrice');
              debugPrint('   原始数据: ${nozzleJson.keys.toList()}');
            } else {
              debugPrint(
                  '✅ 价格正常: 设备$deviceId, Nozzle$nozzleId(#$nozzleNumber), price=$currentPrice, 燃油:$fuelGrade');
            }
          }
        }

        return deviceList
            .map((deviceJson) =>
                FCCDevice.fromJson(deviceJson as Map<String, dynamic>))
            .toList();
      } else {
        throw FCCApiException(
          'Failed to get devices: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error getting devices: $e');
    }
  }

  /// Get device by ID with nozzles
  Future<FCCDevice> getDevice(String deviceId) async {
    try {
      final http.Response response = await _httpClient.get(
        Uri.parse('$_baseUrl/api/v2/devices/$deviceId'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return FCCDevice.fromJson(data as Map<String, dynamic>);
      } else if (response.statusCode == 404) {
        throw FCCApiException('Device not found: $deviceId', 404);
      } else {
        throw FCCApiException(
          'Failed to get device: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error getting device: $e');
    }
  }

  /// Update device status
  Future<FCCDevice> updateDevice(
    String deviceId, {
    FCCDeviceStatus? status,
    FCCDeviceHealth? health,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final Map<String, dynamic> updateData = <String, dynamic>{};
      if (status != null) updateData['status'] = status.value;
      if (health != null) updateData['health'] = health.value;
      if (metadata != null) updateData['metadata'] = metadata;

      final http.Response response = await _httpClient.put(
        Uri.parse('$_baseUrl/api/v2/devices/$deviceId'),
        headers: _getHeaders(),
        body: json.encode(updateData),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return FCCDevice.fromJson(data as Map<String, dynamic>);
      } else {
        throw FCCApiException(
          'Failed to update device: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error updating device: $e');
    }
  }

  // === Nozzle Management APIs ===

  /// Get device nozzles
  Future<List<FCCNozzle>> getDeviceNozzles(String deviceId) async {
    try {
      final http.Response response = await _httpClient.get(
        Uri.parse('$_baseUrl/api/v2/devices/$deviceId/nozzles'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        // 安全处理可能为null的nozzles字段
        final List nozzleList =
            data['nozzles'] as List<dynamic>? ?? <dynamic>[];
        final String deviceName =
            data['device_name'] as String? ?? 'Unknown Device';
        return nozzleList
            .map((nozzleJson) => FCCNozzle.fromJson(
                nozzleJson as Map<String, dynamic>,
                deviceId: deviceId,
                deviceName: deviceName))
            .toList();
      } else {
        throw FCCApiException(
          'Failed to get device nozzles: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error getting device nozzles: $e');
    }
  }

  /// Update nozzle status
  Future<FCCNozzle> updateNozzle(
    String deviceId,
    String deviceName,
    int nozzleNumber, {
    FCCNozzleStatus? status,
    bool? isEnabled,
    bool? isSelected,
    Map<String, dynamic>? stateData,
  }) async {
    try {
      final Map<String, dynamic> updateData = <String, dynamic>{};
      if (status != null) updateData['status'] = status.value;
      if (isEnabled != null) updateData['is_enabled'] = isEnabled;
      if (isSelected != null) updateData['is_selected'] = isSelected;
      if (stateData != null) updateData['state_data'] = stateData;

      final http.Response response = await _httpClient.put(
        Uri.parse('$_baseUrl/api/v2/devices/$deviceId/nozzles/$nozzleNumber'),
        headers: _getHeaders(),
        body: json.encode(updateData),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        debugPrint('🔄 FCCDeviceAPI: 更新nozzle状态: $data');
        return FCCNozzle.fromJson(data as Map<String, dynamic>,
            deviceId: deviceId, deviceName: deviceName);
      } else {
        throw FCCApiException(
          'Failed to update nozzle: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error updating nozzle: $e');
    }
  }

  // === Device Operations APIs ===

  /// Authorize nozzle for fueling
  // Future<Map<String, dynamic>> authorizeNozzle(
  //   String deviceId,
  //   String nozzleId, {
  //   String? presetType, // 'amount', 'volume', 'full'
  //   double? presetValue,
  //   String? employeeId,
  //   String? tag,
  // }) async {
  //   try {
  //     // 使用Wayne DART API的authorize端点
  //     const String endpoint = '/api/v2/wayne/authorize';

  //     final Map<String, dynamic> requestData = <String, dynamic>{
  //       'device_id': deviceId,
  //       'command': 'authorize',
  //       'nozzle_id': nozzleId,
  //       if (presetType != null) 'preset_type': presetType,
  //       if (presetValue != null) 'preset_value': presetValue,
  //       if (employeeId != null) 'employee_id': employeeId,
  //       if (tag != null) 'tag': tag,
  //       'timestamp': DateTime.now().toIso8601String(),
  //     };

  //     final http.Response response = await _httpClient.post(
  //       Uri.parse('$_baseUrl$endpoint'),
  //       headers: _getHeaders(),
  //       body: json.encode(requestData),
  //     );

  //     if (response.statusCode == 200) {
  //       return json.decode(response.body) as Map<String, dynamic>;
  //     } else {
  //       throw FCCApiException(
  //         'Failed to authorize nozzle: ${response.statusCode}',
  //         response.statusCode,
  //         response.body,
  //       );
  //     }
  //   } catch (e) {
  //     throw FCCApiException('Error authorizing nozzle: $e');
  //   }
  // }

  /// Set preset values for nozzle (separate from authorization)
  Future<Map<String, dynamic>> presetNozzle(
    String deviceId,
    String nozzleId, {
    required String presetType, // 'amount', 'volume', 'full'
    double? presetValue,
    String? employeeId,
    String? tag,
  }) async {
    try {
      // 新增：使用合并的 preauth 接口
      const String endpoint = '/api/v2/wayne/preauth';
      
      final Map<String, dynamic> requestData = <String, dynamic>{
        'device_id': deviceId,
        'nozzle_id': nozzleId,
        'decimals': 3,
        if (employeeId != null) 'employee_id': employeeId,
        if (tag != null) 'tag': tag,
      };

      switch (presetType) {
        case 'amount':
          requestData['command'] = 'preset_amount';
          requestData['auth_type'] = 'preset_amount';
          if (presetValue != null) {
            requestData['amount'] = presetValue.toStringAsFixed(2);
          }
          break;
        case 'volume':
          requestData['command'] = 'preset_volume';
          requestData['auth_type'] = 'preset_volume';
          if (presetValue != null) {
            requestData['volume'] = presetValue.toStringAsFixed(3);
          }
          break;
        case 'full':
          requestData['command'] = 'preset_full';
          requestData['auth_type'] = 'preset_full';
          break;
        default:
          throw FCCApiException('Unsupported preset type: $presetType');
      }

      debugPrint('🔄 FCCDeviceAPI: 预授权请求: $requestData');

      final http.Response response = await _httpClient.post(
        Uri.parse('$_baseUrl$endpoint'),
        headers: _getHeaders(),
        body: json.encode(requestData),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw FCCApiException(
          'Failed to preauth nozzle: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error preauth nozzle: $e');
    }
  }

  /// Cancel preauth for specific nozzle
  Future<Map<String, dynamic>> cancelPreauth(
    String deviceId,
    String nozzleId, {
    String? employeeId,
    String? reason,
  }) async {
    try {
      // 使用 DELETE 方法取消预授权
      final String endpoint = '/api/v2/wayne/preauth/$deviceId/$nozzleId';
      
      final Map<String, dynamic> requestData = <String, dynamic>{
        if (employeeId != null) 'employee_id': employeeId,
        if (reason != null) 'reason': reason,
        'timestamp': DateTime.now().toIso8601String(),
      };

      debugPrint('🔄 FCCDeviceAPI: 取消预授权请求: $endpoint');
      debugPrint('📤 请求数据: $requestData');

      final http.Response response = await _httpClient.delete(
        Uri.parse('$_baseUrl$endpoint'),
        headers: _getHeaders(),
        body: json.encode(requestData),
      );

      debugPrint('📥 FCCDeviceAPI: 取消预授权响应: ${response.statusCode} - ${response.body}');

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw FCCApiException(
          'Failed to cancel preauth: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error cancelling preauth: $e');
    }
  }

  /// Stop device or specific nozzle
  Future<Map<String, dynamic>> stopDevice(
    String deviceId, {
    int? nozzleNumber,
    String? reason,
    String? employeeId,
  }) async {
    try {
      final Map<String, dynamic> requestData = <String, dynamic>{
        'action': 'stop',
        if (nozzleNumber != null) 'nozzle_number': nozzleNumber,
        if (reason != null) 'reason': reason,
        if (employeeId != null) 'user_id': employeeId,
        'timestamp': DateTime.now().toIso8601String(),
      };

      final http.Response response = await _httpClient.post(
        Uri.parse('$_baseUrl/api/v2/devices/$deviceId/stop'),
        headers: _getHeaders(),
        body: json.encode(requestData),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw FCCApiException(
          'Failed to stop device: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error stopping device: $e');
    }
  }

  /// Reset device or nozzle
  Future<Map<String, dynamic>> resetDevice(
    String deviceId, {
    int? nozzleNumber,
    String? employeeId,
  }) async {
    try {
      debugPrint(
          '🔄 FCCDeviceAPI: 尝试重置设备 $deviceId${nozzleNumber != null ? ' nozzle $nozzleNumber' : ''}');

      // ✅ 修复：首先尝试Wayne DART API reset端点
      try {
        final Map<String, dynamic> wayneDartResult =
            await _resetDeviceUsingWayneDart(
          deviceId,
          nozzleNumber: nozzleNumber,
          employeeId: employeeId,
        );
        debugPrint('✅ Wayne DART reset成功');
        return wayneDartResult;
      } catch (wayneDartError) {
        debugPrint('⚠️ Wayne DART reset失败: $wayneDartError');
        debugPrint('🔄 回退到标准设备reset API...');
      }

      // 回退到原有的设备reset API
      final Map<String, dynamic> requestData = <String, dynamic>{
        'action': 'reset',
        if (nozzleNumber != null) 'nozzle_number': nozzleNumber,
        if (employeeId != null) 'user_id': employeeId,
        'timestamp': DateTime.now().toIso8601String(),
      };

      final http.Response response = await _httpClient.post(
        Uri.parse('$_baseUrl/api/v2/devices/$deviceId/reset'),
        headers: _getHeaders(),
        body: json.encode(requestData),
      );

      debugPrint('📡 标准reset API响应: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('✅ 标准设备reset成功');
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        final String errorBody = response.body;
        debugPrint('❌ 标准设备reset失败: ${response.statusCode} - $errorBody');

        // ✅ 修复：404错误提供更好的错误信息
        if (response.statusCode == 404) {
          throw FCCApiException(
            'Device $deviceId not found or reset endpoint not available. This may be normal if the device is not currently active.',
            response.statusCode,
            errorBody,
          );
        }

        throw FCCApiException(
          'Failed to reset device: ${response.statusCode}',
          response.statusCode,
          errorBody,
        );
      }
    } catch (e) {
      debugPrint('❌ resetDevice总异常: $e');
      throw FCCApiException('Error resetting device: $e');
    }
  }

  /// 使用Wayne DART API进行设备重置
  Future<Map<String, dynamic>> _resetDeviceUsingWayneDart(
    String deviceId, {
    int? nozzleNumber,
    String? employeeId,
  }) async {
    const String endpoint = '/api/v2/wayne/reset';

    final Map<String, dynamic> requestData = <String, dynamic>{
      'device_id': deviceId,
      'command': 'reset',
      if (nozzleNumber != null) 'nozzle_number': nozzleNumber,
      if (employeeId != null) 'user_id': employeeId,
      'timestamp': DateTime.now().toIso8601String(),
    };

    final http.Response response = await _httpClient.post(
      Uri.parse('$_baseUrl$endpoint'),
      headers: _getHeaders(),
      body: json.encode(requestData),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body) as Map<String, dynamic>;
    } else {
      throw FCCApiException(
        'Failed to reset device via Wayne DART: ${response.statusCode}',
        response.statusCode,
        response.body,
      );
    }
  }

  /// 使用Wayne DART API停止设备操作
  Future<Map<String, dynamic>> stopDeviceUsingWayneDart(
    String deviceId, {
    int? nozzleNumber,
    String? employeeId,
    String? reason,
  }) async {
    const String endpoint = '/api/v2/wayne/stop';

    final Map<String, dynamic> requestData = <String, dynamic>{
      'device_id': deviceId,
      'command': 'stop',
      'async': false,
      if (nozzleNumber != null) 'nozzle_number': nozzleNumber,
      if (employeeId != null) 'user_id': employeeId,
      if (reason != null) 'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    };

    debugPrint('🔄 FCCDeviceAPI: Wayne Stop API请求: ${json.encode(requestData)}');

    final http.Response response = await _httpClient.post(
      Uri.parse('$_baseUrl$endpoint'),
      headers: _getHeaders(),
      body: json.encode(requestData),
    );

    debugPrint('📥 FCCDeviceAPI: Wayne Stop API响应: ${response.statusCode} - ${response.body}');

    if (response.statusCode == 200) {
      debugPrint('✅ Wayne DART stop成功');
      return json.decode(response.body) as Map<String, dynamic>;
    } else {
      throw FCCApiException(
        'Failed to stop device via Wayne DART: ${response.statusCode}',
        response.statusCode,
        response.body,
      );
    }
  }

  // === Controller Management APIs ===

  /// Get all controllers
  Future<List<FCCController>> getControllers({
    String? stationId,
    FCCDeviceStatus? status,
    FCCProtocolType? protocol,
  }) async {
    try {
      final Map<String, String> queryParams = <String, String>{};
      if (stationId != null) queryParams['station_id'] = stationId;
      if (status != null) queryParams['status'] = status.value;
      if (protocol != null) queryParams['protocol'] = protocol.value;

      final Uri uri = Uri.parse('$_baseUrl/api/v2/controllers').replace(
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      final http.Response response = await _httpClient.get(
        uri,
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        // 安全处理可能为null的controllers字段
        final List controllerList =
            data['controllers'] as List<dynamic>? ?? <dynamic>[];

        return controllerList
            .map((controllerJson) =>
                FCCController.fromJson(controllerJson as Map<String, dynamic>))
            .toList();
      } else {
        throw FCCApiException(
          'Failed to get controllers: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error getting controllers: $e');
    }
  }

  /// Get controller by ID
  Future<FCCController> getController(String controllerId) async {
    try {
      final http.Response response = await _httpClient.get(
        Uri.parse('$_baseUrl/api/v2/controllers/$controllerId'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return FCCController.fromJson(data as Map<String, dynamic>);
      } else if (response.statusCode == 404) {
        throw FCCApiException('Controller not found: $controllerId', 404);
      } else {
        throw FCCApiException(
          'Failed to get controller: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error getting controller: $e');
    }
  }

  // === Transaction Management APIs ===

  /// Get device transactions
  Future<Map<String, dynamic>> getDeviceTransactions(
    String deviceId, {
    String? status,
    String? type,
    String? dateFrom,
    String? dateTo,
    int page = 1,
    int limit = 10,
  }) async {
    // FCC API 不处理燃油交易数据，这应该通过 BOS API 查询
    throw FCCApiException(
        'FCC API does not handle fuel transaction data. Use BOS API instead.');
  }

  /// Get active transactions for device
  Future<List<Map<String, dynamic>>> getActiveTransactions(
      String deviceId) async {
    // FCC API 不处理燃油交易数据，这应该通过 BOS API 查询
    throw FCCApiException(
        'FCC API does not handle fuel transaction data. Use BOS API instead.');
  }

  /// Get nozzle transaction data
  Future<Map<String, dynamic>?> getNozzleTransaction(
      String deviceId, int nozzleNumber) async {
    // FCC API 不处理燃油交易数据，这应该通过 BOS API 查询
    debugPrint(
        '⚠️ FCC API: getNozzleTransaction not supported. Transaction data should come from BOS API.');
    return null;
  }

  /// Get transaction summary for device
  Future<Map<String, dynamic>> getTransactionSummary(
    String deviceId, {
    String? dateFrom,
    String? dateTo,
  }) async {
    try {
      final Map<String, String> queryParams = <String, String>{};
      if (dateFrom != null) queryParams['date_from'] = dateFrom;
      if (dateTo != null) queryParams['date_to'] = dateTo;

      final Uri uri =
          Uri.parse('$_baseUrl/api/v2/devices/$deviceId/transactions/summary')
              .replace(
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      final http.Response response = await _httpClient.get(
        uri,
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw FCCApiException(
          'Failed to get transaction summary: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error getting transaction summary: $e');
    }
  }

  // === Health and Status APIs ===

  /// Check FCC service health status
  Future<Map<String, dynamic>> getDeviceHealth({
    String? deviceId,
    String? stationId,
  }) async {
    try {
      // Use the correct health endpoint: /api/v2/health
      final http.Response response = await _httpClient.get(
        Uri.parse('$_baseUrl/api/v2/health'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw FCCApiException(
          'Failed to get service health: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error getting service health: $e');
    }
  }

  /// Get device status summary
  Future<Map<String, dynamic>> getDeviceStatusSummary(String deviceId) async {
    try {
      final http.Response response = await _httpClient.get(
        Uri.parse('$_baseUrl/api/v2/devices/$deviceId/status'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw FCCApiException(
          'Failed to get device status: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      throw FCCApiException('Error getting device status: $e');
    }
  }

  // === Utility Methods ===

  /// Get default HTTP headers
  Map<String, String> _getHeaders() {
    return <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'EDC-Frontend/1.0',
      // Add authentication headers when needed
      // 'Authorization': 'Bearer $token',
    };
  }

  /// Dispose HTTP client
  void dispose() {
    _httpClient.close();
  }

  /// 获取默认的FCC服务URL（使用当前环境配置和自定义URL）
  static String _getDefaultFccUrl() {
    try {
      // 首先检查是否有自定义FCC URL
      final String? customUrl = ApiConstants.getCustomServiceUrl(BackendService.fcc);
      if (customUrl != null) {
        return customUrl;
      }

      // 如果没有自定义URL，使用默认配置
      // 这里使用local环境作为默认值，因为这是一个同步方法
      return ApiConstants.getServiceUrl(BackendService.fcc, ApiEnvironment.local);
    } catch (e) {
      // 如果获取失败，使用硬编码的默认值
      return 'http://172.18.7.82:8081';
    }
  }
}

/// FCC API Exception
class FCCApiException implements Exception {
  FCCApiException(this.message, [this.statusCode, this.responseBody]);
  final String message;
  final int? statusCode;
  final String? responseBody;

  @override
  String toString() {
    if (statusCode != null) {
      return 'FCCApiException($statusCode): $message';
    }
    return 'FCCApiException: $message';
  }

  /// Check if this is a network error
  bool get isNetworkError => statusCode == null || statusCode! >= 500;

  /// Check if this is a client error
  bool get isClientError =>
      statusCode != null && statusCode! >= 400 && statusCode! < 500;

  /// Check if this is a not found error
  bool get isNotFound => statusCode == 404;

  /// Check if this is an unauthorized error
  bool get isUnauthorized => statusCode == 401;

  /// Check if this is a timeout error
  bool get isTimeout => message.toLowerCase().contains('timeout');
}
