import 'package:flutter_test/flutter_test.dart';
import '../lib/constants/customer_type_constants.dart';
import '../lib/models/member_model.dart';
import '../lib/services/member_cache_service.dart';

void main() {
  group('B2B Flow Tests', () {
    late MemberCacheService memberCacheService;

    setUp(() {
      memberCacheService = MemberCacheService();
    });

    tearDown(() {
      memberCacheService.clearCache();
    });

    test('B2B 客户类型检测', () {
      // 创建 B2B 会员
      final Map<String, dynamic> b2bMetadata = <String, dynamic>{
        'plateNumbers': <String>['B1234CD'],
        'vehicle': 'Car',
        'registrationSource': 'EDC_QUICK_REGISTRATION',
        'isTemporary': true,
        'cachedAt': DateTime.now().toIso8601String(),
      };
      
      // 设置 B2B 客户类型
      CustomerTypeUtils.setCustomerType(b2bMetadata, CustomerType.b2b);

      final Member b2bMember = Member(
        id: 'B2B_12345',
        memberCardId: 'B2B_12345',
        name: 'Test B2B Company',
        phone: '',
        email: '<EMAIL>',
        level: MemberLevel.bronze,
        status: MemberStatus.active,
        balance: 0.0,
        points: 0,
        registrationDate: DateTime.now(),
        lastVisitDate: DateTime.now(),
        metadata: b2bMetadata,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 缓存 B2B 会员
      memberCacheService.cacheMember(b2bMember);

      // 验证缓存
      expect(memberCacheService.hasCachedMember, true);
      final Member? cachedMember = memberCacheService.cachedMember;
      expect(cachedMember, isNotNull);

      // 验证客户类型检测
      final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(
        cachedMember!.metadata, 
        memberId: cachedMember.id
      );
      expect(detectedType, CustomerType.b2b);

      // 验证 B2B 检测方法
      expect(CustomerTypeUtils.isB2BCustomer(cachedMember.metadata, memberId: cachedMember.id), true);
      expect(CustomerTypeUtils.isB2CCustomer(cachedMember.metadata, memberId: cachedMember.id), false);
    });

    test('B2C 客户类型检测', () {
      // 创建 B2C 会员
      final Map<String, dynamic> b2cMetadata = <String, dynamic>{
        'plateNumbers': <String>['D5678EF'],
        'vehicle': 'Car',
        'registrationSource': 'EDC_QUICK_REGISTRATION',
        'isTemporary': true,
        'cachedAt': DateTime.now().toIso8601String(),
      };
      
      // 设置 B2C 客户类型
      CustomerTypeUtils.setCustomerType(b2cMetadata, CustomerType.b2c);

      final Member b2cMember = Member(
        id: 'B2C_67890',
        memberCardId: 'B2C_67890',
        name: 'Test Individual Customer',
        phone: '081234567890',
        email: '<EMAIL>',
        level: MemberLevel.bronze,
        status: MemberStatus.active,
        balance: 0.0,
        points: 0,
        registrationDate: DateTime.now(),
        lastVisitDate: DateTime.now(),
        metadata: b2cMetadata,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 缓存 B2C 会员
      memberCacheService.cacheMember(b2cMember);

      // 验证缓存
      expect(memberCacheService.hasCachedMember, true);
      final Member? cachedMember = memberCacheService.cachedMember;
      expect(cachedMember, isNotNull);

      // 验证客户类型检测
      final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(
        cachedMember!.metadata, 
        memberId: cachedMember.id
      );
      expect(detectedType, CustomerType.b2c);

      // 验证 B2C 检测方法
      expect(CustomerTypeUtils.isB2BCustomer(cachedMember.metadata, memberId: cachedMember.id), false);
      expect(CustomerTypeUtils.isB2CCustomer(cachedMember.metadata, memberId: cachedMember.id), true);
    });

    test('默认客户类型检测 (无 customerType 字段)', () {
      // 创建没有 customerType 字段的会员
      final Map<String, dynamic> defaultMetadata = <String, dynamic>{
        'plateNumbers': <String>['G9012HI'],
        'vehicle': 'Car',
        'registrationSource': 'EDC_QUICK_REGISTRATION',
        'isTemporary': true,
        'cachedAt': DateTime.now().toIso8601String(),
        // 注意：没有 customerType 字段
      };

      final Member defaultMember = Member(
        id: 'DEFAULT_11111',
        memberCardId: 'DEFAULT_11111',
        name: 'Test Default Customer',
        phone: '081234567890',
        email: '<EMAIL>',
        level: MemberLevel.bronze,
        status: MemberStatus.active,
        balance: 0.0,
        points: 0,
        registrationDate: DateTime.now(),
        lastVisitDate: DateTime.now(),
        metadata: defaultMetadata,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 验证默认客户类型检测（应该默认为 B2C）
      final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(
        defaultMember.metadata, 
        memberId: defaultMember.id
      );
      expect(detectedType, CustomerType.b2c);

      // 验证检测方法
      expect(CustomerTypeUtils.isB2BCustomer(defaultMember.metadata, memberId: defaultMember.id), false);
      expect(CustomerTypeUtils.isB2CCustomer(defaultMember.metadata, memberId: defaultMember.id), true);
    });

    test('CustomerType 枚举功能', () {
      // 测试 fromString 方法
      expect(CustomerType.fromString('B2B'), CustomerType.b2b);
      expect(CustomerType.fromString('B2C'), CustomerType.b2c);
      expect(CustomerType.fromString('b2b'), CustomerType.b2b);
      expect(CustomerType.fromString('b2c'), CustomerType.b2c);
      expect(CustomerType.fromString('invalid'), null);
      expect(CustomerType.fromString(null), null);
      expect(CustomerType.fromString(''), null);

      // 测试 isB2B 和 isB2C 静态方法
      expect(CustomerType.isB2B('B2B'), true);
      expect(CustomerType.isB2B('B2C'), false);
      expect(CustomerType.isB2C('B2C'), true);
      expect(CustomerType.isB2C('B2B'), false);

      // 测试 toString 方法
      expect(CustomerType.b2b.toString(), 'B2B');
      expect(CustomerType.b2c.toString(), 'B2C');

      // 测试 value 属性
      expect(CustomerType.b2b.value, 'B2B');
      expect(CustomerType.b2c.value, 'B2C');
    });

    test('CustomerTypeUtils 工具方法', () {
      // 测试 getAllValues
      final List<String> allValues = CustomerTypeUtils.getAllValues();
      expect(allValues, contains('B2B'));
      expect(allValues, contains('B2C'));
      expect(allValues.length, 2);

      // 测试 getDisplayText
      expect(CustomerTypeUtils.getDisplayText(CustomerType.b2b), 'Business Customer');
      expect(CustomerTypeUtils.getDisplayText(CustomerType.b2c), 'Individual Customer');

      // 测试 setCustomerType 和 detectCustomerType
      final Map<String, dynamic> testMetadata = <String, dynamic>{};
      
      CustomerTypeUtils.setCustomerType(testMetadata, CustomerType.b2b);
      expect(testMetadata[CustomerTypeFields.customerType], 'B2B');
      expect(CustomerTypeUtils.detectCustomerType(testMetadata), CustomerType.b2b);

      CustomerTypeUtils.setCustomerType(testMetadata, CustomerType.b2c);
      expect(testMetadata[CustomerTypeFields.customerType], 'B2C');
      expect(CustomerTypeUtils.detectCustomerType(testMetadata), CustomerType.b2c);
    });
  });
}
